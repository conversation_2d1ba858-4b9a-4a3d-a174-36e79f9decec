import { current<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>OP_SPAN, Span, traced } from "braintrust";
import { PROJECT_NAME } from "./meta-eval";
import { ChatMetrics } from "../llm/chat";
import { Score } from "autoevals";
import { OptimizationResult, scoreImprovement } from "./optimize-scorer";
import {
  DEFAULT_TRIAL_COUNT,
  makeDataset,
  makeEvalChat,
  MODEL,
  SpanChatLogger,
  TASK_FILTER,
  TRIAL_COUNT,
} from "./eval-vars";
import { ALL_TOOL_NAMES } from "../tools";

function toolSuccessRate({ output }: { output: OptimizationResult }): Score {
  return {
    name: "tool_success_rate",
    score:
      output.metrics.toolCalls > 0
        ? output.metrics.completed / output.metrics.toolCalls
        : null,
    metadata: {
      metrics: output.metrics,
    },
  };
}

function noInvalidToolCalls({
  output: { metrics },
}: {
  output: { metrics: ChatMetrics };
}): Score {
  return {
    name: "no_invalid_tool_calls",
    score: metrics.invalidToolCalls === 0 ? 1 : 0,
    metadata: {
      metrics,
    },
  };
}

Eval(PROJECT_NAME, {
  data: makeDataset(),
  task: async (input): Promise<OptimizationResult> => {
    let consoleLogger: SpanChatLogger | undefined = undefined;
    let rawOutput: Span = NOOP_SPAN;
    try {
      const {
        chat,
        consoleLogger: cl,
        tools,
        rawOutput: ro,
        task,
      } = await makeEvalChat(input, {
        allowed_tools: ALL_TOOL_NAMES.filter(
          (_) =>
            true /*tool !== "get_available_scorers" && tool !== "edit_scorers"*/,
        ),
      });
      if (!task) {
        throw new Error(`Task not found for input: ${input}`);
      }

      consoleLogger = cl;
      rawOutput = ro;

      // Get the baseline performance
      const baselineResults = await traced(
        async (span) => {
          const ret = await tools.tools.run_task(
            {
              index: 0,
            },
            span,
          );
          span.log({ output: ret });
          return ret;
        },
        {
          name: "baseline experiment",
        },
      );

      // Now, ask the model to improve the task
      await chat.turn(
        `Improve the performance, re-run as needed. Do not ask me for permission to proceed. Just proceed with steps.
Feel free to add scorers, but you will be judged by performance on ${task.scores.map((s) => s.name).join(", ")}.
        `,
      );

      const updatedResults = await traced(
        async (span) => {
          const ret = await tools.tools.run_task(
            {
              index: 0,
            },
            span,
          );
          span.log({ output: ret });
          return ret;
        },
        {
          name: "updated experiment",
        },
      );

      const { toolCallsByType: _, ...numericMetrics } = chat.metrics;
      currentSpan().log({ metrics: numericMetrics });
      return {
        baseline: baselineResults.summary.scores,
        updated: updatedResults.summary.scores,
        metrics: chat.metrics,
      };
    } finally {
      await consoleLogger?.flush();
      rawOutput.end();
    }
  },
  scores: [scoreImprovement, toolSuccessRate, noInvalidToolCalls],
  experimentName: `unsupervised improvement-${MODEL}${
    TASK_FILTER === "*" ? "" : ` [tasks: ${TASK_FILTER}]`
  }${TRIAL_COUNT === DEFAULT_TRIAL_COUNT ? "" : ` [trials: ${TRIAL_COUNT}]`}`,
  metadata: {
    model: MODEL,
    taskFilter: TASK_FILTER,
  },
  trialCount: TRIAL_COUNT,
});
