// This package doesn't have types
// @ts-ignore
import inquirerCommandPrompt from "inquirer-command-prompt";

import { ArgumentParser } from "argparse";
import { Chat<PERSON>ontex<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, jsonTo<PERSON>aml } from "./llm/chat";
import {
  _internalGetGlobalState,
  currentSpan,
  initLogger,
  login,
} from "braintrust";
import inquirer from "inquirer";
import chalk from "chalk";
import figlet from "figlet";
import ora from "ora";
import boxen from "boxen";
import { z } from "zod";
import { EVALS } from "./evals/tasks";
import { makeTools } from "./cli-provider";
import path from "path";
import os from "os";
import { PROJECT_NAME } from "./evals/meta-eval";
import { ALL_TOOL_NAMES, ToolName } from "./tools";
import { SpanComponentsV3, SpanObjectTypeV3 } from "@braintrust/core";
import { DEFAULT_MODEL } from "./llm/system-prompt";

// Configure command history
inquirerCommandPrompt.setConfig({
  history: {
    save: true,
    folder: path.join(os.homedir(), ".braintrust"),
    limit: 100,
    blacklist: ["exit"],
  },
});

// Configure and register the command prompt
inquirer.registerPrompt("command", inquirerCommandPrompt);

export type Spinner = ReturnType<typeof ora>;

// Custom logger that accumulates output instead of printing to console
class CustomLogger implements ChatLogger {
  private output: string = "";
  private spinner: ReturnType<typeof ora> | null = null;

  constructor(spinner: Spinner | null = null) {
    this.spinner = spinner;
  }

  write(message: string): void {
    this.output += message;

    // Update spinner text with the actual message as it comes in
    if (this.spinner) {
      // Display the last few lines of text in the spinner
      const lines = this.output.split("\n");
      const lastLines = lines.slice(-8).join("\n"); // Show last 8 lines
      this.spinner.text = lastLines;
    }
  }

  flush(): void {
    if (this.spinner) {
      this.spinner.text = "...";
      this.spinner.clear();
    }
    console.log(
      boxen(this.output, {
        padding: 1,
        borderStyle: "round",
        borderColor: "green",
      }),
    );
    this.output = "";
  }
}

function clearScreen(): void {
  console.clear();
}

async function displayBanner() {
  clearScreen();

  // Create a minimal banner with figlet
  const bannerText = figlet.textSync("Braintrust", {
    font: "Slant",
  });

  console.log(chalk.cyan(bannerText));
  console.log(chalk.dim("  AI Assistant"));
  console.log();
}

const argsSchema = z.object({
  mode: z.enum(["eval", "logs"]),
  eval_project_name: z.string(),
  log_project_name: z.string(),
  eval_name: z.string().optional(),
  project_name: z.string().optional(),
  model: z.string(),
});

async function startChat() {
  const parser = new ArgumentParser({
    description: "Optimize evals",
  });
  parser.add_argument("mode", {
    help: "Mode to run in",
    choices: ["eval", "logs"],
  });
  parser.add_argument("--eval-project-name", {
    type: "string",
    help: "The name of the project containing the eval dataset",
    default: PROJECT_NAME,
  });
  parser.add_argument("--log-project-name", {
    type: "string",
    help: "The name of the project to store optimization logs",
    default: "Optimization cli logs",
  });
  parser.add_argument("--model", {
    type: "string",
    help: "The model to use for optimization",
    default: DEFAULT_MODEL,
  });
  parser.add_argument("name", {
    help: "Evaluation name (for eval mode) or Project name (for logs mode)",
    nargs: "?",
  });

  const rawArgs = parser.parse_args();

  // Validate that name is provided based on mode
  if (!rawArgs.name) {
    parser.error(
      `${rawArgs.mode === "eval" ? "Evaluation" : "Project"} name is required`,
    );
  }

  // Additional validation for eval mode - name must be in EVALS
  if (rawArgs.mode === "eval" && !Object.keys(EVALS).includes(rawArgs.name)) {
    parser.error(
      `Invalid evaluation name. Must be one of: ${Object.keys(EVALS).join(", ")}`,
    );
  }

  const args = argsSchema.parse({
    mode: rawArgs.mode,
    eval_name: rawArgs.mode === "eval" ? rawArgs.name : undefined,
    project_name: rawArgs.mode === "logs" ? rawArgs.name : undefined,
    ...rawArgs,
  });

  // Start the spinner which will also show partial text
  const responseSpinner = ora({
    text: "...",
    spinner: "dots",
    color: "green",
  });

  const customLogger = new CustomLogger(responseSpinner);

  await login();

  let queryObjectType: string | undefined;
  let queryObjectId: string | undefined;
  if (args.project_name) {
    const logger = initLogger({
      projectName: args.project_name,
      setCurrent: false,
    });
    // If in logs mode, we need to set the query context
    queryObjectType = "project";
    queryObjectId = (await logger.project).id;
  }

  const tools = await makeTools({
    def: args.eval_name ? EVALS[args.eval_name] : undefined,
    queryObjectType,
    queryObjectId,
    applicationContext: "playground",
    projectName: args.eval_project_name,
    logProjectName: args.log_project_name,
    spinner: responseSpinner,
    printExperimentResult: (result) => {
      console.log(
        "\n" +
          boxen(result, {
            padding: 1,
            borderStyle: "round",
            borderColor: "blue",
          }),
      );
    },
    model: args.model,
  });

  await displayBanner();

  // Display welcome message with instructions
  console.log(
    chalk.dim("Type /help for available commands or 'exit' to quit\n"),
  );

  const btLogger = initLogger({
    projectName: args.log_project_name,
    setCurrent: false,
  });

  // Create an AbortController to handle interruptions
  let abortController = new AbortController();
  let isGenerating = false;

  // We'll manage Ctrl+C interrupts manually during generation
  const onGenerationSigint = () => {
    if (isGenerating) {
      // Cancel the generation but don't exit
      abortController.abort();

      if (responseSpinner.isSpinning) {
        responseSpinner.stop();
        console.log(chalk.yellow("\nGeneration interrupted"));
      }

      // Create a new controller for next time
      abortController = new AbortController();
      isGenerating = false;
    }
  };

  // Start the chat loop
  let chatActive = true;

  // Create a new chat context for each turn with a custom logger
  const chat = new ChatContext({
    loggingState: _internalGetGlobalState(),
    orgName: undefined,
    tools: tools ?? undefined,
    loggingRoot: new SpanComponentsV3({
      object_type: SpanObjectTypeV3.PROJECT_LOGS,
      object_id: (await btLogger.project).id,
    }),
    loggingMetadata: {
      user_id: undefined,
      email: undefined,
      project_id: undefined,
      org_id: undefined,
    },
    enableLogging: true,
    consoleLogger: customLogger,
    model: args.model,
    modelParams: {
      max_tokens: 10240,
    },
  });

  while (chatActive) {
    // Format the user prompt with minimal styling
    console.log(chalk.blue("You"));

    let response;
    try {
      // Remove any listeners from previous turns
      process.removeAllListeners("SIGINT");

      // Use type assertion to bypass type checking
      response = await inquirer.prompt({
        // @ts-ignore - command type is registered at runtime but TypeScript doesn't know about it
        type: "command",
        name: "message",
        message: "› ",
        // This will ensure history is separated by mode and name
        context:
          args.mode === "eval"
            ? `${args.mode}-${args.eval_name}`
            : `${args.mode}-${args.project_name}`,
        // Support viewing available commands with autocomplete
        autocompletePrompt: ">> Available commands:",
        noColorOnAnswered: true,
        onClose: () => {
          process.exit(0);
        },
      });
    } catch {
      // Just continue - don't exit here
      continue;
    }

    const userMessage = String(response.message).trim();

    if (userMessage.toLowerCase() === "exit") {
      chatActive = false;
      process.exit(0);
    } else if (userMessage.startsWith("/")) {
      // Handle slash commands
      const commandLower = userMessage.toLowerCase().trim();
      const commandOriginal = userMessage.trim();

      // Check if it's a tool execution command
      if (
        commandOriginal.startsWith("/execute_tool") ||
        commandOriginal.startsWith("/exec")
      ) {
        const toolCommandMatch = commandOriginal.match(
          /^\/(?:execute_tool|exec)\s+(\w+)\((.*)\)$/,
        );
        if (!toolCommandMatch) {
          console.error(
            chalk.red(
              "Invalid format. Expected: /execute_tool toolName(key1=value1, key2=value2) or /exec toolName(key1=value1, key2=value2)",
            ),
          );
          continue;
        }

        const toolName = toolCommandMatch[1];
        const argsString = toolCommandMatch[2];

        // Parse the arguments
        const args: Record<string, unknown> = {};
        if (argsString.trim()) {
          const argPairs = argsString.split(",");
          for (const pair of argPairs) {
            const [key, value] = pair.trim().split("=");
            if (key && value !== undefined) {
              try {
                args[key] = JSON.parse(value);
              } catch {
                args[key] = value;
              }
            }
          }
        }

        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        if (!ALL_TOOL_NAMES.includes(toolName as ToolName)) {
          console.error(chalk.red(`Unknown tool: ${toolName}`));
          continue;
        }

        console.log(chalk.green("Tool"));
        try {
          const result = await tools.executeTool(
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            toolName as ToolName,
            args,
            currentSpan(),
          );
          customLogger.write(jsonToYaml(result));
          customLogger.flush();
        } catch (error) {
          console.error(chalk.red("Error:"));
          if (error instanceof Error && error.stack) {
            console.error(chalk.red(error.stack));
          } else {
            console.error(chalk.red(String(error)));
          }
        }
        continue;
      }

      switch (commandLower) {
        case "/tools":
          if (tools) {
            console.log(chalk.cyan("\nAvailable tools:"));
            for (const toolName of tools.list()) {
              console.log(chalk.white(`  • ${toolName}`));
            }
          } else {
            console.log(chalk.yellow("No tools available"));
          }
          break;

        case "/help":
          console.log(chalk.cyan("\nAvailable commands:"));
          console.log(chalk.white("  /tools         - List available tools"));
          console.log(
            chalk.white(
              "  /exec          - Execute a tool (short for /execute_tool)",
            ),
          );
          console.log(
            chalk.white("  /execute_tool  - Execute a tool with arguments"),
          );
          console.log(chalk.white("  /help          - Show this help message"));
          console.log(chalk.white("  /clear         - Clear the screen"));
          console.log(chalk.white("  /mode          - Show current mode"));
          console.log(chalk.white("  /model         - Show current model"));
          console.log(chalk.white("  exit           - Exit the chat"));
          console.log(
            chalk.white(
              "\nTool execution format: /exec toolName(key1=value1, key2=value2)",
            ),
          );
          break;

        case "/clear":
          clearScreen();
          await displayBanner();
          break;

        case "/mode":
          console.log(chalk.cyan(`\nCurrent mode: ${args.mode}`));
          if (args.mode === "eval") {
            console.log(chalk.cyan(`Evaluation: ${args.eval_name}`));
          } else {
            console.log(chalk.cyan(`Project: ${args.project_name}`));
          }
          break;

        case "/model":
          console.log(chalk.cyan(`\nCurrent model: ${args.model}`));
          break;

        default:
          console.log(chalk.red(`Unknown command: ${commandLower}`));
          console.log(chalk.dim("Type /help for available commands"));
          break;
      }
    } else if (userMessage.length > 0) {
      console.log(chalk.green("AI"));

      // Reset abort controller
      abortController = new AbortController();
      isGenerating = true;

      // Set up SIGINT handler just for this generation
      process.on("SIGINT", onGenerationSigint);

      responseSpinner.start();
      try {
        await chat.turn(userMessage, { abortController });
        responseSpinner.stop();
      } catch (error) {
        responseSpinner.stop();

        if (!(error instanceof Error && error.name === "AbortError")) {
          console.error(chalk.red("Error:"));
          if (error instanceof Error && error.stack) {
            console.error(chalk.red(error.stack));
          } else {
            console.error(chalk.red(String(error)));
          }
        }
      } finally {
        isGenerating = false;
        // Clean up the SIGINT handler
        process.removeAllListeners("SIGINT");
      }
    }

    // Add a subtle separator line
    console.log();
  }
}

// Wrap the entire application in a try/catch to handle uncaught errors
try {
  startChat().catch((error) => {
    console.error(chalk.red("Error:"));
    if (error instanceof Error && error.stack) {
      console.error(chalk.red(error.stack));
    } else {
      console.error(chalk.red(String(error)));
    }
    process.exit(1);
  });
} catch (error) {
  console.error(chalk.red("Uncaught error:"));
  if (error instanceof Error && error.stack) {
    console.error(chalk.red(error.stack));
  } else {
    console.error(chalk.red(String(error)));
  }
  process.exit(1);
}
