import { isObject, mergeDicts } from "@braintrust/core";
import {
  doubleQuote,
  jsonGet,
  jsonPath,
  MetricsField,
  RootSpanIdField,
  ScoresField,
  singleQuote,
  SpanIdField,
  TagFilterField,
  TransactionIdField,
  unfoldNested<PERSON><PERSON>s,
  CostField,
} from "./sql-utils";
import { batch, makeTagFilter, makeTypedTags, TypedTag } from "./types";
import {
  ProjectScore,
  projectScoreCategory,
  projectScoreSchema,
} from "@braintrust/core/typespecs";
import { z } from "zod";
import { Expr as ParsedExpr, Parser } from "@braintrust/btql/parser";
import { BindContext, bindExpr, BoundExpr } from "@braintrust/btql/binder";
import { planExpr } from "@braintrust/btql/planner";
import {
  JSONSchemaObject,
  jsonSchemaObjectSchema,
  LogicalSchema,
} from "@braintrust/btql/schema";
import {
  BRAINTRUST_LOGICAL_SCHEMA,
  BraintrustLogicalSchema,
  braintrustLogicalSchema,
  DUCKDB_PHYSICAL_SCHEMA,
  MetricDefinition,
} from "@lib/api-schema";
import { lexicographicalComparison } from "./object";

export function experimentScanSpanSummary({
  experimentScanRaw,
  modelSpecScan,
  auditLogScan,
  scoreFields,
  scoreConfig,
  comparisonKey,
  plainFilters,
  postAggregationFilters,
  commentsFilters,
  tags,
  hasErrorField,
  isRootAvailable,
  originDatasetRowId,
}: {
  experimentScanRaw: string | null;
  modelSpecScan?: string | null;
  auditLogScan?: string | null;
  scoreFields: string[];
  scoreConfig: DiscriminatedProjectScore[];
  comparisonKey: string | null;
  plainFilters?: string[];
  postAggregationFilters?: string[];
  commentsFilters?: string[];
  tags?: string[];
  hasErrorField: boolean;
  isRootAvailable: boolean;
  originDatasetRowId?: boolean;
}): string | null {
  if (!experimentScanRaw) {
    return null;
  }
  const spanGroupFields = [doubleQuote(RootSpanIdField)];
  const spanGroupExpr = spanGroupFields.join(",");
  const joinExpr = (base_tbl: string, join_tbl: string) => {
    const onConds = spanGroupFields.map(
      (f) => `${base_tbl}.${f} IS NOT DISTINCT FROM ${join_tbl}.${f}`,
    );
    return `JOIN ${join_tbl} ON (${onConds.concat("TRUE").join(" AND ")})`;
  };

  const aggregateScores = scoreConfig.filter((s) =>
    isAggregateScore(s.score_type),
  );
  const nonAggregateScores = scoreFields.filter(
    (s) => !aggregateScores.find((a) => a.name === s),
  );

  const scoreFieldsExtracted: [string, string][] = nonAggregateScores.map(
    (f, idx) => [f, `__score${idx}`],
  );

  const aggregateScoreSQL = makeWeightedScoreSQL({
    scoreConfig,
    scoreNames: nonAggregateScores,
    scoreToField: Object.fromEntries(scoreFieldsExtracted),
  });
  const aggregateScoreSQLEntries = Object.entries(aggregateScoreSQL);

  const scoresRepacked =
    aggregateScoreSQLEntries.length + scoreFieldsExtracted.length > 0
      ? `STRUCT_PACK(${aggregateScoreSQLEntries
          .concat(scoreFieldsExtracted)
          .map(([name, expr]) => `${doubleQuote(name)} := ${expr}\n`)
          .join(",")}) AS "${ScoresField}"`
      : `NULL AS "${ScoresField}"`;

  const scoresMapRepacked =
    scoreFieldsExtracted.length > 0
      ? `map_from_entries([${scoreFieldsExtracted
          .map(([name, expr]) => `(${singleQuote(name)}, ${expr})`)
          .join(",")}]) AS "${ScoresField}_map"`
      : `NULL AS "${ScoresField}_map"`;

  const extractedTags = makeTypedTags(tags).map((typedTag, idx) => ({
    typedTag,
    field: `"__tag${idx}"`,
  }));

  const comparisonKeySQL = makeComparisonKeySQL({ comparisonKey });

  const makeTagCaseExpr = (typedTag: TypedTag) => {
    const tagFilter = makeTagFilter(typedTag);
    return `CASE WHEN ${tagFilter} THEN 1 ELSE 0 END`;
  };
  const makeTagAggExpr = (typedTag: TypedTag, field: string) =>
    typedTag.type === "+" ? `MAX(${field})` : `MIN(${field})`;

  const tagFilterExprs = extractedTags.map(({ field }) => `${field} = 1`);
  const tagFilterExpr = tagFilterExprs.length
    ? `(${tagFilterExprs.join(" AND ")}) AS "${TagFilterField}"`
    : `TRUE AS "${TagFilterField}"`;

  const hasCommentsFilters = commentsFilters && commentsFilters.length > 0;

  // NOTE: We may want to generalize this to compute both scorer and non-scorer metrics, but for now let's just keep
  // it simple with non-scorer metrics.
  const experimentScanWithHoistedMetrics = `
    SELECT
      ${[
        "*",
        `${isRootAvailable ? "is_root" : "root_span_id = span_id"} AS is_root`,
        `COALESCE(json_extract_string("span_attributes", '$.purpose'), 'default') AS "purpose"`,
        `json_extract_string("span_attributes", '$.type') AS "span_type"`,
        `CASE WHEN purpose = 'scorer' THEN NULL ELSE COALESCE(${jsonGet(MetricsField, ["prompt_tokens"], "INTEGER")}, 0) END AS "prompt_tokens"`,
        `CASE WHEN purpose = 'scorer' THEN NULL ELSE COALESCE(${jsonGet(MetricsField, ["completion_tokens"], "INTEGER")}, 0) END AS "completion_tokens"`,
        `CASE WHEN purpose = 'scorer' THEN NULL ELSE COALESCE(${jsonGet(MetricsField, ["prompt_cached_tokens"], "INTEGER")}, 0) END AS "prompt_cached_tokens"`,
        `CASE WHEN purpose = 'scorer' THEN NULL ELSE COALESCE(${jsonGet(MetricsField, ["prompt_cache_creation_tokens"], "INTEGER")}, 0) END AS "prompt_cache_creation_tokens"`,
        `json_extract_string("metadata", '$.model') AS "model"`,
      ].join(", ")}
    FROM (${experimentScanRaw}) "t"
  `;

  const baseScan = modelSpecScan
    ? `SELECT *,
      (prompt_tokens - prompt_cached_tokens - prompt_cache_creation_tokens) * "input_cost_per_mil_tokens" / 1000000 +
      prompt_cached_tokens * COALESCE("input_cache_read_cost_per_mil_tokens", "input_cost_per_mil_tokens") / 1000000 +
      prompt_cache_creation_tokens * COALESCE("input_cache_write_cost_per_mil_tokens", "input_cost_per_mil_tokens") / 1000000 AS "prompt_cost",
      completion_tokens * "output_cost_per_mil_tokens" / 1000000 as "completion_cost",
    FROM (
      ${experimentScanWithHoistedMetrics}
    ) exp LEFT JOIN model_specs ON exp.model = model_specs.model`
    : experimentScanWithHoistedMetrics;

  // NOTES:
  // - Comparison key is now precomputed and saved in this view
  return `
WITH
${modelSpecScan ? `model_specs as (${modelSpecScan}),` : ""}
base AS (
    ${baseScan}
),
spans_unpacked AS (
SELECT
    ${spanGroupFields
      .concat([
        `json_extract(${ScoresField}, [${scoreFieldsExtracted.map(
          ([origF, _]) => jsonPath([origF]),
        )}]) AS all_scores`,
      ])
      .concat(["is_root"])
      .concat(
        extractedTags.map(
          ({ typedTag, field }) => `${makeTagCaseExpr(typedTag)} AS ${field}`,
        ),
      )
      .concat([
        `COALESCE(json_extract(${MetricsField}, 'cached')::int, 0) AS cached`,
        `${jsonGet(MetricsField, ["end"], "DOUBLE")} AS end`,
        `${jsonGet(MetricsField, ["start"], "DOUBLE")} AS start`,
        `${jsonGet(MetricsField, ["end"], "DOUBLE")}-${jsonGet(
          MetricsField,
          ["start"],
          "DOUBLE",
        )} AS duration`,
        "prompt_tokens",
        "completion_tokens",
        "prompt_cached_tokens",
        "prompt_cache_creation_tokens",
      ])
      .concat(modelSpecScan ? ['"prompt_cost"', '"completion_cost"'] : [])
      .concat(["span_attributes", "purpose", "span_type"])
      .join(",")}
FROM
    base
),
spans_aggregated AS (
SELECT
    ${spanGroupFields
      .concat(
        scoreFieldsExtracted.map(
          ([_, extractedF], i) =>
            `AVG(all_scores[${i + 1}]::double) AS ${extractedF}`,
        ),
      )
      .concat(
        extractedTags.map(
          ({ typedTag, field }) =>
            `${makeTagAggExpr(typedTag, field)} AS ${field}`,
        ),
      )
      .concat([
        `MIN(CASE WHEN span_type = 'llm' AND purpose <> 'scorer' THEN cached ELSE NULL END) AS cached`,
        `MIN(CASE WHEN is_root THEN "start" ELSE NULL END) AS "start"`,
        `MAX(CASE WHEN is_root THEN "end" ELSE NULL END) AS "end"`,
        `MAX("duration") AS "total_duration"`,
        `MAX(CASE WHEN json_extract_string(span_attributes, '$.name') = 'task' THEN "duration" ELSE NULL END) AS "task_duration"`,
        `AVG(CASE WHEN span_type = 'llm' AND purpose <> 'scorer' THEN "duration" ELSE NULL END) AS llm_duration`,
        `SUM(prompt_tokens) AS prompt_tokens`,
        `SUM(completion_tokens) AS completion_tokens`,
        `SUM(prompt_cached_tokens) AS prompt_cached_tokens`,
        `SUM(prompt_cache_creation_tokens) AS prompt_cache_creation_tokens`,
      ])
      .concat(
        modelSpecScan
          ? [`SUM("prompt_cost" + "completion_cost") as "${CostField}"`]
          : [],
      )
      .join(",")}
FROM
    spans_unpacked
GROUP BY
    ${spanGroupExpr}
),
spans_repacked_unfiltered AS (
SELECT
    ${spanGroupExpr},
    ${scoresRepacked},
    ${scoresMapRepacked},
    ${tagFilterExpr},
    STRUCT_PACK(
      cached := cached,
      "start" := "start",
      "end" := "end",
      duration := COALESCE("task_duration", "total_duration"),
      llm_duration := llm_duration,
      prompt_tokens := prompt_tokens,
      completion_tokens := completion_tokens,
      prompt_cached_tokens := prompt_cached_tokens,
      prompt_cache_creation_tokens := prompt_cache_creation_tokens,
      total_tokens := prompt_tokens + completion_tokens,
      "${CostField}" := ${modelSpecScan ? `"${CostField}"` : "NULL"}
    ) AS metrics
FROM
    spans_aggregated
),
spans_repacked AS (
    SELECT
      *
    FROM
      spans_repacked_unfiltered AS "base" -- This helps filters work
${
  postAggregationFilters?.length
    ? `WHERE ${postAggregationFilters.map((f) => `(${f})`).join(" AND ")}`
    : ""
}
),
${
  auditLogScan
    ? `audit_log AS (
  ${auditLogScan}
),
comments AS (
  SELECT * FROM (
    SELECT
      array_agg(audit_log ORDER BY created DESC) FILTER (comment IS NOT NULL) as comments,
      json_extract_string(origin, '$.id') AS origin_id
    FROM audit_log
    GROUP BY origin_id
  ) as grouped
  SEMI JOIN (
    SELECT DISTINCT origin_id FROM (
      SELECT
        comment,
        json_extract_string(origin, '$.id') AS origin_id
      FROM audit_log
    ) AS "base" -- This helps filters work
    WHERE true AND
    ${
      hasCommentsFilters
        ? `${commentsFilters.map((f) => `(${f})`).join(" AND ")}`
        : "true"
    }
  ) AS filtered_comments ON grouped.origin_id = filtered_comments.origin_id
),`
    : ""
}
candidate_spans AS (
SELECT
    base."${RootSpanIdField}"
FROM
    base
WHERE base.is_root AND
    ${
      plainFilters?.length
        ? `${plainFilters.map((f) => `(${f})`).join(" AND ")}`
        : "true"
    }
)
SELECT
    base.id,
    base."${TransactionIdField}",
    -- If the comparison key is empty, we don't want the expression to return null, because
    -- that messes up other comparisons. This ensures that it's always a string.
    COALESCE(MD5(${comparisonKeySQL}), '<empty>') AS comparison_key,
    base."${SpanIdField}",
    base."${RootSpanIdField}",
    ${originDatasetRowId ? `json_extract_string(base.origin, '$.id')` : "NULL"} AS dataset_row_id,
    json_object(
      'name', json_extract_string("span_attributes", '$.name'),
      'type', json_extract_string("span_attributes", '$.type'),
      'cached', COALESCE(spans_repacked.metrics."cached", 0),
      'remote', COALESCE(json_extract(base.${MetricsField}, '$.remote')::int, 0),
      'has_error', ${hasErrorField ? `json_extract(base."error", '$') IS NOT NULL` : "false"},
      'in_progress', json_extract(base.metrics, '$.end') IS NULL and json_extract(base.metrics, '$.start') IS NOT NULL,
      'generation', json_extract_string("span_attributes", '$.generation')
    ) as span_type_info,
    ${auditLogScan ? `comments.comments` : "NULL"} as comments,
    spans_repacked."${ScoresField}_map",
    spans_repacked."${ScoresField}" AS ${ScoresField},
    spans_repacked.metrics AS ${MetricsField}
FROM
    base
        ${joinExpr("base", "spans_repacked")}
    ${
      auditLogScan
        ? `${hasCommentsFilters ? "" : "LEFT "} JOIN comments ON (base.id IS NOT DISTINCT FROM comments.origin_id)`
        : ""
    }
SEMI JOIN
    candidate_spans
ON
    base."${RootSpanIdField}"=candidate_spans."${RootSpanIdField}"
WHERE
    spans_repacked."${TagFilterField}"
    AND base.is_root
  `;
}

export function constructFullSummaryScan({
  experimentScanRaw,
  summaryScan,
  auditLogScan,
  customColumns,
  includeScoresMap,
  customColumnFilters,
  originDatasetRowId,
}: {
  experimentScanRaw: string | null;
  summaryScan: string | null;
  auditLogScan?: string | null;
  customColumns?: string[];
  includeScoresMap?: boolean;
  customColumnFilters?: string[];
  originDatasetRowId?: boolean;
}): string | null {
  return (
    experimentScanRaw &&
    summaryScan &&
    `(SELECT * FROM
      (
        SELECT ${[includeScoresMap ? "*" : '* EXCLUDE "scores_map"'].concat(customColumns ?? []).join(", ")}
        FROM
        (
          SELECT
            ${[
              "exp.* EXCLUDE (scores, metrics)",
              "summary.comparison_key",
              "summary.scores",
              "summary.scores_map",
              "summary.metrics",
              "summary.span_type_info",
              ...(originDatasetRowId ? ["summary.dataset_row_id"] : []),
              ...(auditLogScan ? ["summary.comments"] : []),
            ].join(", ")}
          FROM
            (${experimentScanRaw}) exp
            INNER JOIN (${summaryScan}) summary ON exp.id = summary.id
        ) t
      ) base ${
        customColumnFilters?.length
          ? `WHERE ${customColumnFilters.join(" AND ")}`
          : ""
      }
    )`
  );
}

export function constructFullSummaryScanAbbrev({
  summaryScan,
  filters,
  scoreNames,
  metricNames,
  integerMetrics,
}: {
  summaryScan: string | null;
  filters?: string[];
  scoreNames: string[] | null;
  metricNames: string[] | null;
  integerMetrics: Set<string> | null;
}): string | null {
  return (
    summaryScan &&
    `(SELECT *
      FROM (
      SELECT * REPLACE (
        ${scoreNames === null ? "scores" : scoreNames.length === 0 ? "NULL::json" : `struct_pack(${scoreNames.map((n) => `${doubleQuote(n)} := scores[${singleQuote(n)}]`).join(",")})`} AS scores,
        ${metricNames === null ? "metrics" : metricNames.length === 0 ? "NULL::json" : `struct_pack(${metricNames.map((n) => `${doubleQuote(n)} := metrics[${singleQuote(n)}]${integerMetrics?.has(n) ? "::INTEGER" : "::DOUBLE"}`).join(",")})`} AS metrics
      )
      , true as is_root FROM (${summaryScan})) base
      WHERE ${
        filters?.length
          ? `${filters.map((f) => `(${f})`).join(" AND ")}`
          : "true"
      }
    )`
  );
}

// DEVNOTE: Make sure this is smaller than the # of scores generated in
// tests/text_experiment_summary.py:test_many_scores
const SCORE_BATCH_SIZE = 20;

// For some insane reason, arrow serializes bigints as extra quoted strings, i.e.
// '"\"12\""' instead of 12. This function processes the string by parsing it first,
// and then converting it to a number (which allows two rounds of deserialization)
//
// To reproduce this, you can (a) remove this preProcess function and then (b) run
// the summary queries (that average, etc.) over computed metrics, and (c) call toJSON()
// on them. You'll receive ProxyRow objects, which if you `JSON.stringify(...)` will
// produce these extra quoted strings.
function preProcessDeepString(s: unknown) {
  if (typeof s === "string") {
    s = JSON.parse(s);
  }
  return Number(s);
}

export const scoreSummaryItemSchema = z.object({
  _name: z.string().optional(),
  avg: z.preprocess(preProcessDeepString, z.number()).nullish(),
  min: z.preprocess(preProcessDeepString, z.number()).nullish(),
  max: z.preprocess(preProcessDeepString, z.number()).nullish(),
  sum: z.preprocess(preProcessDeepString, z.number()).nullish(),
  median: z.preprocess(preProcessDeepString, z.number()).nullish(),
  stddev: z.preprocess(preProcessDeepString, z.number()).nullish(),
  diffAvg: z.preprocess(preProcessDeepString, z.number()).nullish(),
  diffMin: z.preprocess(preProcessDeepString, z.number()).nullish(),
  diffMax: z.preprocess(preProcessDeepString, z.number()).nullish(),
  diffSum: z.preprocess(preProcessDeepString, z.number()).nullish(),
  diffMedian: z.preprocess(preProcessDeepString, z.number()).nullish(),
  compareAvg: z.preprocess(preProcessDeepString, z.number()).nullish(),
  compareMin: z.preprocess(preProcessDeepString, z.number()).nullish(),
  compareMax: z.preprocess(preProcessDeepString, z.number()).nullish(),
  compareSum: z.preprocess(preProcessDeepString, z.number()).nullish(),
  compareMedian: z.preprocess(preProcessDeepString, z.number()).nullish(),
  improvements: z.array(z.string()).nullish(),
  regressions: z.array(z.string()).nullish(),
  equal: z.array(z.string()).nullish(),
  all_keys: z.array(z.string()).nullish(),
  compare_keys: z.array(z.string()).nullish(),

  // full calculations of keys in the comparison experiment regardless of filter
  compare_all_avg: z.preprocess(preProcessDeepString, z.number()).nullish(),
  compare_all_min: z.preprocess(preProcessDeepString, z.number()).nullish(),
  compare_all_max: z.preprocess(preProcessDeepString, z.number()).nullish(),
  compare_all_sum: z.preprocess(preProcessDeepString, z.number()).nullish(),
  compare_all_median: z.preprocess(preProcessDeepString, z.number()).nullish(),
  compare_all_keys: z.array(z.string()).nullish(),
});

export const scoreSummarySchema = z.record(scoreSummaryItemSchema);

export type ScoreSummary = z.infer<typeof scoreSummarySchema>;

export type SummaryDataParams = {
  primaryExperimentScan: string | null;
  comparisonExperimentScan: string | null;
  groupKey: ((relation: string) => string) | null;
  comparisonKey: (relation: string) => string;
  comparisonKeyFilterClause?: (relation: string) => string;
  scoreFields: string[];
  comparisonScoreFields: string[];
  metricFields: string[];
  comparisonMetricFields: string[];
  scoreConfig: DiscriminatedProjectScore[];
  metricDefinitions: MetricDefinition[];
  hasErrorField?: boolean;
};

export function computeSummaryScoreBatches({
  scoreFields,
  metricFields,
  comparisonMetricFields,
  comparisonScoreFields,
  scoreConfig,
  metricDefinitions,
  hasErrorField,
}: Pick<
  SummaryDataParams,
  | "scoreFields"
  | "metricFields"
  | "comparisonMetricFields"
  | "comparisonScoreFields"
  | "scoreConfig"
  | "hasErrorField"
  | "metricDefinitions"
>): {
  allSummaryDefBatches: {
    name: string;
    def: (p: string) => string;
    desc: boolean;
  }[][];
  comparableDefBatches: {
    name: string;
    def: (p: string) => string;
    desc: boolean;
  }[][];
  summaryMetricNames: string[];
} {
  const summaryScores = scoreFields
    .concat(
      scoreConfig
        .filter(
          (s) =>
            isAggregateScore(s.score_type) && !scoreFields.includes(s.name),
        )
        .map((s) => s.name),
    )
    .map((n) => ({
      name: n,
      def: (p: string) => `${p}.scores.${doubleQuote(n)}`,
      desc: false,
    }));
  const summaryMetrics: {
    name: string;
    def: (p: string) => string;
    desc: boolean;
  }[] = [];

  for (const def of metricDefinitions) {
    if (metricFields.includes(def.field_name)) {
      summaryMetrics.push({
        name: def.field_name,
        def: (p: string) => {
          const metricName = `${p}.metrics.${doubleQuote(def.field_name)}`;
          return def.ignore_if_cached
            ? `(CASE WHEN ${p}.metrics."cached" THEN 0::DOUBLE ELSE ${metricName} END)`
            : metricName;
        },
        desc: def.optimize_dir === "minimize",
      });
    }
  }

  if (metricFields.includes("start") && metricFields.includes("end")) {
    [
      "start",
      "end",
      "duration",
      "llm_duration",
      SUMMARY_CACHED_METRIC_NAME,
    ].forEach((m) => {
      if (summaryMetrics.find((sm) => sm.name === m)) {
        return;
      }

      summaryMetrics.push({
        name: m,
        def: (p: string) =>
          `${p}.metrics.${m === SUMMARY_CACHED_METRIC_NAME ? "cached" : m}`,
        desc: true,
      });
    });
  }

  const summaryMetricNames = summaryMetrics.map((m) => m.name);

  const comparableSummaryMetrics = [
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    ...(comparisonMetricFields.filter((n) => n === "tokens") as string[]),
    ...(["start", "end"].every((n) => comparisonMetricFields.includes(n))
      ? ["duration", "llm_duration", SUMMARY_CACHED_METRIC_NAME]
      : []),
  ];

  for (const def of metricDefinitions) {
    if (
      comparisonMetricFields.includes(def.field_name) &&
      !comparableSummaryMetrics.includes(def.field_name)
    ) {
      comparableSummaryMetrics.push(def.field_name);
    }
  }

  const allSummaryDefs = [
    ...summaryScores,
    ...summaryMetrics,
    ...(hasErrorField
      ? [{ name: "error", def: (p: string) => `${p}."error"`, desc: true }]
      : []),
  ];
  const comparableDefs = allSummaryDefs.filter(
    ({ name }) =>
      comparisonScoreFields.includes(name) ||
      comparableSummaryMetrics.includes(name) ||
      name === "error" ||
      name === "cached" ||
      scoreConfig.find(
        (s) => s.name === name && isAggregateScore(s.score_type),
      ),
  );

  const allSummaryDefBatches = batch(allSummaryDefs, SCORE_BATCH_SIZE);
  const comparableDefBatches = batch(comparableDefs, SCORE_BATCH_SIZE);
  return { allSummaryDefBatches, comparableDefBatches, summaryMetricNames };
}

function defaultStructFieldsFn(
  fieldExpr: string,
  comparisonKeyStr: string,
  isError?: boolean,
) {
  const allKeys = `all_keys := ARRAY_AGG(DISTINCT ${comparisonKeyStr}) FILTER (${fieldExpr} IS NOT NULL)`;
  if (isError) {
    return allKeys;
  }
  return `
        avg := AVG(${fieldExpr}),
        min := MIN(${fieldExpr}),
        max := MAX(${fieldExpr}),
        sum := SUM(${fieldExpr}),
        median := MEDIAN(${fieldExpr}),
        stddev := /*STDDEV_SAMP(${fieldExpr})*/ NULL,
        ${allKeys}
    `;
}

export function compareStructFieldsFn(
  fieldExpr: string,
  comparisonKeyStr: string,
  isError?: boolean,
) {
  const allKeys = `compare_all_keys := ARRAY_AGG(DISTINCT ${comparisonKeyStr}) FILTER (${fieldExpr} IS NOT NULL)`;
  if (isError) {
    return allKeys;
  }

  return `compare_all_avg := AVG(${fieldExpr}),
        compare_all_min := MIN(${fieldExpr}),
        compare_all_max := MAX(${fieldExpr}),
        compare_all_sum := SUM(${fieldExpr}),
        compare_all_median := MEDIAN(${fieldExpr}),
        ${allKeys}
      `;
}

export function makeSummaryQueries({
  experimentScan,
  allSummaryDefBatches,
  groupKey,
  comparisonKey,
  comparisonKeyFilterClause,
  structFieldsFn = defaultStructFieldsFn,
  includeNullGroups,
}: Pick<
  SummaryDataParams,
  "groupKey" | "comparisonKey" | "comparisonKeyFilterClause"
> & {
  experimentScan: string | null;
  allSummaryDefBatches: ReturnType<
    typeof computeSummaryScoreBatches
  >["allSummaryDefBatches"];
  structFieldsFn?: (
    scoreName: string,
    comparisonKeyStr: string,
    isError?: boolean,
  ) => string;
  includeNullGroups?: boolean;
}): string[] | null {
  return experimentScan && allSummaryDefBatches.length > 0 && groupKey
    ? allSummaryDefBatches.map(
        (batch) => `
    SELECT
      ${groupKey("e")} AS "__bt_group_key",
      ${batch
        .map(
          ({ name, def }) =>
            `struct_pack(
              ${structFieldsFn(def("e"), comparisonKey("e"), name === "error")}
            ) AS ${doubleQuote(name)},
            `,
        )
        .join("")}
      FROM (${experimentScan}) e
      ${comparisonKeyFilterClause?.("e") ?? ""}
      WHERE ${includeNullGroups ? "true" : "__bt_group_key IS NOT NULL"}
      GROUP BY 1
      `,
      )
    : null;
}

// Round the comparisons to 4 digits to avoid spurious differences due to
// floating point precision.
export const COMPARISON_DIGITS = 4;

export function makeChangesQueries({
  primaryExperimentScan,
  comparisonExperimentScan,
  comparableDefBatches,
  groupKey,
  comparisonKey,
  comparisonKeyFilterClause,
  includeNullGroups,
}: Pick<
  SummaryDataParams,
  | "primaryExperimentScan"
  | "comparisonExperimentScan"
  | "groupKey"
  | "comparisonKey"
  | "comparisonKeyFilterClause"
> & {
  comparableDefBatches: ReturnType<
    typeof computeSummaryScoreBatches
  >["comparableDefBatches"];
  includeNullGroups?: boolean;
}) {
  return primaryExperimentScan &&
    comparisonExperimentScan &&
    comparableDefBatches.length > 0 &&
    groupKey
    ? comparableDefBatches.map(
        (batch) => `
  WITH
  e1 AS (
    SELECT * FROM (${primaryExperimentScan}) e1
  ),
  e2 AS (
    SELECT * FROM (${comparisonExperimentScan}) e2
  ),
  e1_grouped AS (
    SELECT
      ${comparisonKey("e1")} "comparison_key",
      ANY_VALUE(${groupKey("e1")}) "__bt_group_key",
      ${
        // we only need to do this for comparable metrics since the grouping is used to compute regressions
        batch
          .map(({ name: n, def }) =>
            n === "error"
              ? `ANY_VALUE(${def("e1")}) AS ${doubleQuote(n)}`
              : `ROUND(AVG(${def("e1")}), ${COMPARISON_DIGITS}) AS ${doubleQuote("avg_" + n)},
      MIN(${def("e1")}) AS ${doubleQuote("min_" + n)},
      MAX(${def("e1")}) AS ${doubleQuote("max_" + n)},
      SUM(${def("e1")}) AS ${doubleQuote("sum_" + n)},
      MEDIAN(${def("e1")}) AS ${doubleQuote("median_" + n)},
      COUNT(${def("e1")}) AS ${doubleQuote("count_" + n)}`,
          )
          .join(",\n")
      }
      FROM e1
      GROUP BY 1
  ),
  e2_grouped AS (
    SELECT
      ${comparisonKey("e2")} "comparison_key",
      ${batch
        .map(({ name: n, def }) =>
          n === "error"
            ? `ANY_VALUE(${def("e2")}) AS ${doubleQuote(n)}`
            : `ROUND(AVG(${def("e2")}), ${COMPARISON_DIGITS}) AS ${doubleQuote("avg_" + n)},
      MIN(${def("e2")}) AS ${doubleQuote("min_" + n)},
      MAX(${def("e2")}) AS ${doubleQuote("max_" + n)},
      SUM(${def("e2")}) AS ${doubleQuote("sum_" + n)},
      MEDIAN(${def("e2")}) AS ${doubleQuote("median_" + n)},
      COUNT(${def("e2")}) AS ${doubleQuote("count_" + n)}`,
        )
        .join(",\n")}
      FROM e2
      GROUP BY 1
  ),
  diffs AS (
    SELECT
      "__bt_group_key",
      ${batch
        .map(({ name }) =>
          name === "error"
            ? ""
            : `struct_pack(
            compareAvg := SUM(e2.${doubleQuote("sum_" + name)}) / SUM(e2.${doubleQuote("count_" + name)}),
            compareMin := MIN(e2.${doubleQuote("min_" + name)}),
            compareMax := MAX(e2.${doubleQuote("max_" + name)}),
            compareSum := SUM(e2.${doubleQuote("sum_" + name)}),
            compareMedian := MEDIAN(e2.${doubleQuote("median_" + name)}),
            -- difference between only overlapping rows
            diffAvg := SUM(e1.${doubleQuote("sum_" + name)}) / SUM(e1.${doubleQuote("count_" + name)}) - SUM(e2.${doubleQuote("sum_" + name)}) / SUM(e2.${doubleQuote("count_" + name)}),
            diffMin := MIN(e1.${doubleQuote("min_" + name)}) - MIN(e2.${doubleQuote("min_" + name)}),
            diffMax := MAX(e1.${doubleQuote("max_" + name)}) - MAX(e2.${doubleQuote("max_" + name)}),
            diffSum := SUM(e1.${doubleQuote("sum_" + name)}) - SUM(e2.${doubleQuote("sum_" + name)}),
            diffMedian := MEDIAN(e1.${doubleQuote("median_" + name)}) - MEDIAN(e2.${doubleQuote("median_" + name)})
          ) AS ${doubleQuote(name)}
          `,
        )
        .join(",")}
    FROM e1_grouped e1 JOIN e2_grouped e2 ON e1."comparison_key" = e2."comparison_key"
    GROUP BY 1
  )
  SELECT
    (e1_grouped."__bt_group_key")::text AS "__bt_group_key",
    ${batch
      .map(
        ({ name, desc }) => `
    struct_pack(${
      name === "error"
        ? `compare_keys := ARRAY_AGG(DISTINCT e1_grouped."comparison_key") FILTER (e2_grouped.${doubleQuote(
            name,
          )} IS NOT NULL)`
        : `
      -- There is only one row in diffs so ANY_VALUE is fine
      _name := ${singleQuote(name)},
      compareAvg := ANY_VALUE(diffs.${doubleQuote(name)}.compareAvg),
      compareMin := ANY_VALUE(diffs.${doubleQuote(name)}.compareMin),
      compareMax := ANY_VALUE(diffs.${doubleQuote(name)}.compareMax),
      compareSum := ANY_VALUE(diffs.${doubleQuote(name)}.compareSum),
      compareMedian := ANY_VALUE(diffs.${doubleQuote(name)}.compareMedian),
      diffAvg := ANY_VALUE(diffs.${doubleQuote(name)}.diffAvg),
      diffMin := ANY_VALUE(diffs.${doubleQuote(name)}.diffMin),
      diffMax := ANY_VALUE(diffs.${doubleQuote(name)}.diffMax),
      diffSum := ANY_VALUE(diffs.${doubleQuote(name)}.diffSum),
      diffMedian := ANY_VALUE(diffs.${doubleQuote(name)}.diffMedian),
      improvements := ARRAY_AGG(DISTINCT e1_grouped."comparison_key") FILTER (e1_grouped.${doubleQuote(
        "avg_" + name,
      )} ${desc ? "<" : ">"} e2_grouped.${doubleQuote("avg_" + name)}),
      regressions := ARRAY_AGG(DISTINCT e1_grouped."comparison_key") FILTER (e1_grouped.${doubleQuote(
        "avg_" + name,
      )} ${desc ? ">" : "<"} e2_grouped.${doubleQuote("avg_" + name)}),
      equal := ARRAY_AGG(DISTINCT e1_grouped."comparison_key") FILTER (e1_grouped.${doubleQuote(
        "avg_" + name,
      )} = e2_grouped.${doubleQuote("avg_" + name)}),
      compare_keys := ARRAY_AGG(DISTINCT e1_grouped."comparison_key") FILTER (e2_grouped.${doubleQuote(
        "avg_" + name,
      )} IS NOT NULL)
    `
    }
    ) AS ${doubleQuote(name)},
  `,
      )
      .join("")}
  FROM e1_grouped JOIN e2_grouped ON
    e1_grouped."comparison_key" = e2_grouped."comparison_key"
    -- This is a single row relation
    JOIN diffs ON e1_grouped."__bt_group_key" IS NOT DISTINCT FROM diffs."__bt_group_key"
    ${comparisonKeyFilterClause?.("e1_grouped") ?? ""}
    GROUP BY 1
  `,
      )
    : null;
}

export function makeFullSummary({
  summaryData,
  compareSummaryData,
  diffData,
}: {
  summaryData: ScoreGroupRow[];
  compareSummaryData: ScoreGroupRow[];
  diffData: ScoreGroupRow[] | null;
}) {
  const scores = processScoreGroups(summaryData);
  const compareScores = processScoreGroups(compareSummaryData);
  const diff = processScoreGroups(diffData ?? []);

  for (const key of Object.keys(scores)) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    scores[key] = mergeDicts(scores[key], diff[key] ?? {}) as ScoreSummary;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    scores[key] = mergeDicts(
      scores[key],
      compareScores[key] ?? {},
    ) as ScoreSummary;
    delete scores[key].__bt_group_key;
  }

  return scores;
}

type ScoreGroupRow = {
  __bt_group_key: string;
  [key: string]: unknown;
};

function processScoreGroups(
  data: ScoreGroupRow[],
): Record<string, ScoreSummary> {
  const scores: Record<string, ScoreSummary> = {};
  for (const r of data) {
    const { __bt_group_key, ...rest } = r;
    if (!scores[r.__bt_group_key]) {
      scores[r.__bt_group_key] = {};
    }
    Object.assign(scores[r.__bt_group_key], scoreSummarySchema.parse(rest));
  }
  return scores;
}

export function experimentGroupStructureQuery({
  hasScores,
  hasMetrics,
  experimentScan,
}: {
  hasScores: boolean;
  hasMetrics: boolean;
  experimentScan: string;
}) {
  return `
      SELECT
        1,
        ${
          hasScores
            ? `json_group_structure(json(${ScoresField})) as scores,`
            : ""
        }
        ${
          hasMetrics
            ? `json_group_structure(json(${MetricsField})) as metrics,`
            : ""
        }
      FROM (
        -- This sort ensures that we look at scores from the top-level span first,
        -- which means the "outermost" scores are the first that are displayed.
        SELECT * FROM (${experimentScan})
        ORDER BY ${SpanIdField} = ${RootSpanIdField} DESC
      ) "t"`;
}

export function parseScoresAndMetrics(row: {
  scores?: string;
  metrics?: string;
}) {
  return (["scores", "metrics"] as const).map((field) => {
    const value = row[field];
    if (!value) {
      return null;
    }
    const info = JSON.parse(value);
    if (info === "NULL" || info === "JSON" || info === null) {
      return [];
    } else {
      return unfoldNestedFields([], info).filter(
        (f) =>
          !(
            field === "metrics" &&
            (f[0] === "cached" || f[0] === SUMMARY_CACHED_METRIC_NAME)
          ),
      );
    }
  });
}

export const SUMMARY_DEFAULT_GROUP_KEY = "_bt_summary_group_key";
export const SUMMARY_CACHED_METRIC_NAME = "__bt_internal_cached";

// projectScoreSchema is the base schema that must be a strictObject to make some of our
// glue code for the API work, but therefore does not granularly discriminate the cases in the
// taggged union (i.e. a "categorical" field can only have an array of `projectScoreCategory`).
//
// We use this schema throughout the UI so that we can enforce that via the typesystem and simplify
// all of the consuming code.
const baseProjectScoreSchema = projectScoreSchema.omit({
  score_type: true,
  categories: true,
});

export const discriminatedProjectScoreSchema = z.union([
  z
    .strictObject({
      score_type: z.literal("slider"),
      categories: z.unknown().nullish(),
    })
    .merge(baseProjectScoreSchema),
  z
    .strictObject({
      score_type: z.literal("categorical"),
      categories: projectScoreCategory.array(),
    })
    .merge(baseProjectScoreSchema),
  z
    .strictObject({
      score_type: z.literal("free-form"),
      categories: z.union([z.null(), z.undefined()]),
    })
    .merge(baseProjectScoreSchema),
  z
    .strictObject({
      score_type: z.literal("weighted"),
      categories: z.record(z.number().nonnegative()),
    })
    .merge(baseProjectScoreSchema),
  z
    .strictObject({
      score_type: z.enum(["minimum", "maximum"]),
      categories: z.array(z.string()),
    })
    .merge(baseProjectScoreSchema),
  z
    .strictObject({
      score_type: z.literal("online"),
      categories: z.union([z.null(), z.undefined()]),
    })
    .merge(baseProjectScoreSchema),
  z
    .strictObject({
      score_type: z.literal("free-form"),
      categories: z.union([z.null(), z.undefined()]),
    })
    .merge(baseProjectScoreSchema),
]);

export type DiscriminatedProjectScore = z.infer<
  typeof discriminatedProjectScoreSchema
>;

function listToSum(list: ParsedExpr[]): ParsedExpr {
  if (list.length === 0) {
    return { op: "literal", value: null };
  } else {
    return list.reduce((acc, val) => ({
      op: "add",
      left: acc,
      right: val,
    }));
  }
}

export function makeWeightedScoreExprs({
  scores,
  includeList,
}: {
  scores: DiscriminatedProjectScore[];
  includeList?: string[];
}): { name: string; expr: ParsedExpr }[] {
  return scores.flatMap((score) => {
    switch (score.score_type) {
      case "categorical":
      case "slider":
      case "free-form":
      case "online":
        return [];
      case "weighted": {
        const scoreCategories = Object.entries(score.categories).filter(
          ([category]) => !includeList || includeList.includes(category),
        );

        // This formula is basically implementing:
        //  weight1*COALESCE(category1, 0) + weight2*COALESCE(category2, 0) + ...
        //  ---------------------------------------------------------------
        //  NULLIF(category1 IS NULL ? 0 : weight1, 0) + NULLIF(category2 IS NULL ? 0 : weight2, 0) + ...
        //
        // A few interesting notes:
        //  * The coalesces in the numerator protect one null category from poisoning the whole score to be NULL
        //  * In the denominator, we check if the category is null, and if so, we don't count its weight
        //  * If all the denominator weights are 0, we return NULL, so that we do not divide by 0

        const numerator = listToSum(
          scoreCategories.map(
            ([category, weight]): ParsedExpr => ({
              op: "mul",
              left: {
                op: "function",
                name: { op: "ident", name: ["coalesce"] },
                args: [
                  { op: "ident", name: [category] },
                  { op: "literal", value: 0 },
                ],
              },
              right: { op: "literal", value: weight },
            }),
          ),
        );
        const denominator: ParsedExpr = {
          op: "function",
          name: {
            op: "ident",
            name: ["nullif"],
          },
          args: [
            listToSum(
              scoreCategories.map(([category, weight]) => ({
                op: "if",
                conds: [
                  {
                    cond: {
                      op: "isnull",
                      expr: { op: "ident", name: [category] },
                    },
                    then: { op: "literal", value: 0 },
                  },
                ],
                else: { op: "literal", value: weight },
              })),
            ),
            {
              op: "literal",
              value: 0,
            },
          ],
        };
        return [
          {
            name: score.name,
            expr: { op: "div", left: numerator, right: denominator },
          },
        ];
      }
      case "minimum":
      case "maximum": {
        const scoreCategories = score.categories.filter(
          (category) => !includeList || includeList.includes(category),
        );

        return [
          {
            name: score.name,
            expr:
              scoreCategories.length === 0
                ? { op: "literal", value: null }
                : // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                  ({
                    op: "function",
                    name: {
                      op: "ident",
                      name: [
                        score.score_type === "maximum" ? "greatest" : "least",
                      ],
                    },
                    args: scoreCategories.map((category) => ({
                      op: "ident",
                      name: [category],
                    })),
                  } as ParsedExpr),
          },
        ];
      }
      default:
        const _: never = score;
        throw new Error(`Unsupported score type ${_}`);
    }
  });
}

export function isHumanReviewScore(
  scoreType: ProjectScore["score_type"],
): scoreType is HumanReviewScoreCategory {
  return ["slider", "categorical", "free-form"].includes(scoreType);
}

export function isAggregateScore(
  scoreType: ProjectScore["score_type"],
): scoreType is AggregateScoreCategory {
  return ["weighted", "minimum", "maximum"].includes(scoreType);
}

export function isOnlineScore(
  scoreType: ProjectScore["score_type"],
): scoreType is "online" {
  return scoreType === "online";
}

export type HumanReviewScoreCategory = "slider" | "categorical" | "free-form";
export type AggregateScoreCategory = "weighted" | "minimum" | "maximum";

export function sortScoreFields(
  names: string[],
  scoreConfig: DiscriminatedProjectScore[],
  preProcess?: (name: string) => string,
): string[] {
  const scoreFields = [...names];

  const configuredFields = scoreConfig.map((s) => s.name.toLowerCase());
  const augmentedScoreFields = scoreFields.map((s) => {
    const sIndex = configuredFields.indexOf(
      (preProcess ? preProcess(s) : s).toLowerCase(),
    );
    if (sIndex === -1) {
      return { bucket: 1, value: s, field: s };
    } else {
      const sIsAggregate = isAggregateScore(scoreConfig[sIndex].score_type);
      return { bucket: sIsAggregate ? 0 : 2, value: sIndex, field: s };
    }
  });
  augmentedScoreFields.sort((a, b) =>
    lexicographicalComparison(a, b, ["bucket", "value"]),
  );
  return augmentedScoreFields.map((x) => x.field);
}

export function extractLogicalSchemaItemsObject(
  field: string,
  schema?: LogicalSchema,
): JSONSchemaObject {
  const ret = jsonSchemaObjectSchema.parse(
    (schema ?? BRAINTRUST_LOGICAL_SCHEMA).properties?.[field],
  ).items;
  if (!(isObject(ret) && !Array.isArray(ret))) {
    throw new Error(
      `Expected logical schema to have type object. Got ${JSON.stringify(ret)}`,
    );
  }
  return ret;
}

export function bindWeightedScores({
  scoreConfig,
  scoreNames,
}: {
  scoreConfig: DiscriminatedProjectScore[];
  scoreNames?: string[];
}): Record<string, BoundExpr> {
  const aggregateScores = scoreConfig.filter((s) =>
    isAggregateScore(s.score_type),
  );
  const aggregateScoreExprs = makeWeightedScoreExprs({
    scores: aggregateScores,
    includeList: scoreNames,
  });
  const bindCtx: BindContext = {
    schema: jsonSchemaObjectSchema.parse(
      extractLogicalSchemaItemsObject("experiment").properties?.scores,
    ),
    scope: {},
    queryText: undefined,
    bindingMeasures: false,
  };

  return Object.fromEntries(
    aggregateScoreExprs.map(({ name, expr }) => [
      name,
      bindExpr(bindCtx, expr),
    ]),
  );
}

export function makeWeightedScoreSQL({
  scoreConfig,
  scoreNames,
  scoreToField,
}: {
  scoreConfig: DiscriminatedProjectScore[];
  scoreNames: string[];
  scoreToField?: Record<string, string>;
}): Record<string, string> {
  const boundScores = bindWeightedScores({
    scoreConfig,
    scoreNames,
  });

  return Object.fromEntries(
    Object.entries(boundScores).map(([name, boundExpr]) => {
      const plannedExpr = planExpr(
        {
          schema: DUCKDB_PHYSICAL_SCHEMA,
          table: {
            alias: null,
            schema: {
              columns: Object.fromEntries(
                scoreNames.map((s, idx) => [
                  s,
                  {
                    path: [scoreToField?.[s] ?? s],
                    type: { type: "double" },
                  },
                ]),
              ),
            },
          },
        },
        boundExpr,
      );
      const exprSQL = plannedExpr.toPlainStringQuery();
      return [name, exprSQL];
    }),
  );
}

export function bindComparisonKey({
  comparisonKey,
}: {
  comparisonKey: string | null;
}): BoundExpr {
  const exprText = comparisonKey ?? "input";
  const parsedExpr = new Parser(exprText).parseExpr();

  const bindCtx: BindContext = {
    schema: extractLogicalSchemaItemsObject("experiment"),
    scope: {},
    queryText: exprText,
    bindingMeasures: false,
  };

  return bindExpr(bindCtx, parsedExpr);
}

export function makeComparisonKeySQL({
  comparisonKey,
  alias,
}: {
  comparisonKey: string | null;
  alias?: string;
}): string {
  const boundExpr = bindComparisonKey({ comparisonKey });

  const plannedExpr = planExpr(
    {
      schema: DUCKDB_PHYSICAL_SCHEMA,
      table: {
        alias: alias ?? "base",
        schema: DUCKDB_PHYSICAL_SCHEMA.tables.experiment,
      },
    },
    boundExpr,
  );
  return plannedExpr.toPlainStringQuery();
}

export const EXCLUDE_METRICS = [SUMMARY_CACHED_METRIC_NAME, "start", "end"];

const PREVIEW_FIELDS = [
  "input",
  "output",
  "expected",
  "error",
  "context",
  "origin",
];

// Fetch just the data needed for the experimental summary
// of an object, without fetching the full content of all spans (which are
// loaded lazily upon request). This significantly speeds up the load time for
// large objects.
export function makeSummaryBtqlReplace(
  objectType: keyof BraintrustLogicalSchema,
  oldClickhouse: boolean,
): Record<string, ParsedExpr> {
  if (objectType === "dataset") {
    return {};
  }
  const schema = braintrustLogicalSchema.shape[objectType];
  return Object.fromEntries(
    Object.keys(schema.element.shape)
      .filter((f) => PREVIEW_FIELDS.includes(f))
      .map((f) => [f, { btql: `is_root ? ${f} : null` }])
      .concat(
        Object.keys(schema.element.shape).includes("metadata") && !oldClickhouse
          ? [
              [
                "metadata",
                {
                  btql: `is_root ? metadata: insert({}, 'model', metadata.model)`,
                },
              ],
            ]
          : [],
      ),
  );
}
