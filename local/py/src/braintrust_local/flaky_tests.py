import re

FLAKY_TESTS_EXACT = {
    "tests/bt_services/test_expect.py::ExpectTest::test_expect_feedback_basic_ts": dict(owner="manu"),
    "tests/bt_services/test_healthserver.py::HealthServerTest::test_healthcheck_realtime": dict(owner="manu"),
    "tests/bt_services/test_functions.py::FunctionTest::test_strict_mode": dict(owner="hurshal"),
    "tests/bt_services/test_function_hooks.py::FunctionHooksTest::test_bundled_code_hooks_1_ts": dict(owner="hurshal"),
    "tests/bt_services/test_schema_inference.py::SchemaInferenceTest::test_schema_inference": dict(
        owner="ankur", log_link="https://drive.google.com/file/d/1ao1dBntBhnnsM40IGdo3u86xvYdMmv8Z/view?usp=drive_link"
    ),
    "tests/bt_services/test_drilldown.py::DrilldownTest::test_select_many_0_brainstore": dict(
        owner="austin",
        log_link="https://drive.google.com/file/d/1LwlcUpx336wUE5WKs-uG44Uf1jofE3rj/view?usp=drive_link",
    ),
    "tests/bt_services/test_async_current_experiment.py::AsyncExperimentsTest::test_basic": dict(
        owner="manu",
        log_link="https://drive.google.com/file/d/1TDkvJJ7Vv7kmPcqUL2FWG5Qvs13yTeCi/view?usp=drive_link",
    ),
    "tests/bt_services/test_expect.py::ExpectTest::test_expect_otel_vercel_ai_sdk_ts": dict(
        owner="ankur",
        log_link="https://drive.google.com/file/d/1GhUlwcjmVgOty4wdziwmuI9ILr6NtOfG/view?usp=drive_link",
    ),
    "tests/bt_services/test_expect.py::ExpectTest::test_expect_eval_update_py": dict(
        owner="olmo",
        log_link="https://drive.google.com/file/d/1ODS_w4tpPz8Gl2uyZXlomXOg8CRm5flc/view?usp=drive_link",
    ),
    "tests/bt_services/test_btql_export.py::BtqlExportTest::test_btql_export_batch_size_one_same_transaction": dict(
        owner="hurshal",
        log_link="https://drive.google.com/file/d/1FGItF77wn9fVFji-YncrqcVrti2Wrngo/view?usp=drive_link",
    ),
    "tests/bt_services/test_distributed_tracing.py::DistributedTracingTest::test_basic": dict(
        owner="manu",
        log_link="https://drive.google.com/file/d/1fAvfDPV43a5L_wMC_JcpzoYs7XQcTqpf/view?usp=drive_link",
    ),
    "tests/bt_services/test_expect.py::ExpectTest::test_expect_simple_factuality_eval_ts": dict(
        owner="olmo",
        log_link="https://drive.google.com/file/d/1tfaubG681-b4D3lSNab5261oZSLt0NQy/view?usp=drive_link",
    ),
    "tests/bt_services/test_expect.py::ExpectTest::test_expect_app_url_py": dict(
        owner="olmo",
        log_link="https://drive.google.com/file/d/1NraFziBwO2lOxmv05yrpAE9iO7RsHGeT/view?usp=drive_link",
    ),
}

FLAKY_TESTS_REGEX = [
    (
        re.compile(r"FunctionToolsTest"),
        dict(
            owner="ankur",
            log_link="https://drive.google.com/file/d/10fxVML1oGO11y2tFkuFSJy_BiTMeuAMd/view?usp=drive_link",
        ),
    ),
    (
        re.compile(r"BundledCodeTest"),
        dict(
            owner="ankur",
            log_link="https://drive.google.com/file/d/10fxVML1oGO11y2tFkuFSJy_BiTMeuAMd/view?usp=drive_link",
        ),
    ),
    (
        re.compile(r"TestOTelClient"),
        dict(
            owner="austin",
            log_link="https://drive.google.com/file/d/1VaTC6hnaBan5etr_gbxD0YuLCTrpaKO1/view?usp=drive_link",
        ),
    ),
    (
        re.compile(r"ResourceCountTest"),
        dict(
            owner="olmo",
            log_link="https://drive.google.com/file/d/1LdHEWh7nzIqgBMytL28irauYvZGAML_W/view?usp=drive_link",
        ),
    ),
]


def print_flaky_tests_report() -> None:
    owner_to_tests = {}
    for test_name, flake_info in FLAKY_TESTS_EXACT.items():
        owner_to_tests.setdefault(flake_info["owner"], []).append(
            dict(name=test_name, log_link=flake_info.get("log_link"))
        )
    for regex, flake_info in FLAKY_TESTS_REGEX:
        owner_to_tests.setdefault(flake_info["owner"], []).append(
            dict(name=regex.pattern, log_link=flake_info.get("log_link"))
        )
    for owner, tests in owner_to_tests.items():
        print(f"- {owner}:")
        for test in tests:
            print(f"  - `{test['name']}`")
            if test.get("log_link"):
                print(f"    - [Log]({test['log_link']})")
