import time

import braintrust
import requests

LOCAL_BRAINSTORE_URL = "http://localhost:4000"


def vacuum_brainstore(object_ids):
    vacuum_index_for_testing(object_ids)
    vacuum_segment_wal_for_testing(object_ids)


def vacuum_index_for_testing(object_ids):
    args = {
        "object_ids": object_ids,
        # Use shorter defaults so we don't have to wait forever for vacuum to delete
        # stuff during tests.
        "vacuum_deletion_grace_period_seconds": 1,
        "vacuum_last_written_slop_seconds": 1,
        # Make brainstore sleep at least the deletion grace period (plus commit slop)
        # before resetting, so that vacuum will be able to delete files written before
        # this function was called.
        "wait_for_vacuum_grace_period": True,
    }
    response = requests.post(f"{LOCAL_BRAINSTORE_URL}/vacuum/index", json=args)
    response.raise_for_status()
    return response.json()


def vacuum_segment_wal_for_testing(object_ids):
    args = {
        "object_ids": object_ids,
        "vacuum_segment_wal_entry_expiration_seconds": 0,
        "vacuum_deletion_grace_period_seconds": 1,
        "vacuum_last_written_slop_seconds": 1,
        "wait_for_vacuum_grace_period": True,
    }
    response = requests.post(f"{LOCAL_BRAINSTORE_URL}/vacuum/segment_wal", json=args)
    response.raise_for_status()
    return response.json()
