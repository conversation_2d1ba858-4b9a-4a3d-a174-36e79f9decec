import { Parser } from "#/parser";
import {
  AliasExpr,
  Expr,
  Function as ParsedFunction,
  Ident,
  Loc,
  Parsed<PERSON><PERSON>y,
  SortExpr,
} from "#/parser/ast";
import {
  deriveScalarTypes,
  LogicalSchema,
  ScalarType,
  scalarTypeToLogicalSchema,
  toSchemaObject,
} from "#/schema";
import {
  JSONSchema,
  JSONSchemaObject,
  SchemaArray,
  jsonSchemaObjectSchema,
  jsonSchemaSchema,
} from "#/schema/json-schema";
import {
  BoundAlias,
  BoundExpr,
  BoundFrom,
  BoundProjection,
  BoundQuery,
  BoundSortItem,
  BoundUnpivotExpr,
  CastExpr,
  Field,
  FunctionExpr,
} from "./ast";
import {
  castToClosest,
  coerceAllExprTypes,
  coerceTypes,
  getExprSchema,
  getExprScalarType,
  literalValueToScalarType,
  stripNull,
  weakestExprType,
  weakestScalarType,
} from "./types";
import { FUNCTION_ALIAS, functionNameSchema, FUNCTIONS } from "./functions";

export type BindContext = {
  schema: LogicalSchema;
  scope: Record<string, BoundExpr>;
  queryText: string | undefined;
  bindingMeasures: boolean;
  skipFieldCasts?: boolean;
};

export const DEFAULT_SCHEMA: LogicalSchema = {
  type: "array",
};

// The binder will currently:
// 1. Validate the query against the schema, where schematic information is available. We fall back to dynamic
//    types when not.
// 2. Perform typechecking, and specifically, bind all field references to their respective types, and ensure that
//    comparisons have compatible types (adding casts where necessary).
export function bindQuery({
  query,
  schema,
  skipFieldCasts,
  queryText,
  applyComputedFields,
}: {
  query: ParsedQuery;
  schema: LogicalSchema;
  skipFieldCasts?: boolean;
  queryText?: string;
  applyComputedFields?: (args: {
    bindCtx: BindContext;
    schema: JSONSchemaObject;
  }) => void;
}): BoundQuery {
  const ctx: BindContext = {
    schema,
    queryText,
    bindingMeasures: false,
    scope: {},
    skipFieldCasts,
  };
  let from: BoundFrom | undefined = undefined;
  if (query.from) {
    from = bindFrom(ctx, query.from);
  } else if (schema.type === "array" && schema.items) {
    ctx.schema = checkValidFieldSchema(ctx, schema.items);
  } else {
    fail(ctx, "Schema must be an array", null);
  }

  if (applyComputedFields) {
    applyComputedFields({ bindCtx: ctx, schema: ctx.schema });
  }

  let customColumns: BoundAlias[] | undefined = undefined;
  if (query.custom_columns) {
    customColumns = query.custom_columns.map(({ alias, expr }) => {
      let bound: BoundExpr;
      try {
        bound = bindExpr(ctx, expr);
      } catch {
        bound = {
          op: "literal",
          value: "<Invalid column>",
          type: "string",
        };
      }
      return {
        alias,
        expr: bound,
      };
    });
  }

  ctx.scope = {
    ...ctx.scope,
    ...(customColumns ?? []).reduce(
      (acc, alias) => {
        acc[alias.alias] = alias.expr;
        return acc;
      },
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      {} as Record<string, BoundExpr>,
    ),
  };
  const filter = query.filter ? bindExpr(ctx, query.filter) : null;

  const unpivot: BoundUnpivotExpr[] = [];
  if (query.unpivot) {
    for (let i = 0; i < query.unpivot.length; i++) {
      const unpivotExpr = query.unpivot[i];
      const expr = bindExpr(ctx, unpivotExpr.expr);
      const exprType = getExprSchema(expr);
      if (exprType.type === "object" && typeof unpivotExpr.alias === "string") {
        fail(
          ctx,
          "Unpivoting an object requires specifying key and a value alias",
          unpivotExpr.expr.loc,
        );
      } else if (
        exprType.type === "array" &&
        typeof unpivotExpr.alias !== "string"
      ) {
        fail(
          ctx,
          "Unpivoting an array requires specifying a single alias",
          unpivotExpr.expr.loc,
        );
      }
      let type: "array" | "object";
      if (typeof unpivotExpr.alias === "string") {
        type = "array";
        const exprTypeItems = stripNull(exprType).items;
        if (Array.isArray(exprTypeItems)) {
          throw new Error("Cannot handle tuple form of 'items' type");
        }
        ctx.scope[unpivotExpr.alias] = {
          op: "field",
          name: [unpivotExpr.alias],
          type: toSchemaObject(exprTypeItems ?? {}),
          source: {
            unpivot: i,
            type: "element",
          },
        };
      } else {
        type = "object";

        for (
          let aliasIdx = 0;
          aliasIdx < unpivotExpr.alias.length;
          aliasIdx++
        ) {
          if (Array.isArray(exprType.items)) {
            throw new Error("Cannot handle tuple form of 'items' type");
          }
          ctx.scope[unpivotExpr.alias[aliasIdx]] = {
            op: "field",
            name: [unpivotExpr.alias[aliasIdx]],
            type:
              aliasIdx === 0
                ? { type: "string" }
                : toSchemaObject(exprType.items ?? {}),
            source: {
              unpivot: i,
              type: aliasIdx === 0 ? "key" : "value",
            },
          };
        }
      }
      unpivot.push({
        expr,
        type,
      });
    }
  }

  const numSelectItems = query.select?.length || 0;
  const numGroupByItems =
    (query.dimensions?.length || 0) + (query.measures?.length || 0);
  const numInferItems = query.infer?.length || 0;

  const numGreaterThanZero =
    (numSelectItems > 0 ? 1 : 0) +
    (numGroupByItems > 0 ? 1 : 0) +
    (numInferItems > 0 ? 1 : 0);

  let selection: BoundProjection;
  if (numGreaterThanZero > 1) {
    fail(ctx, "Must specify either select, dimensions, or infer", null);
  } else if (query.select && numSelectItems > 0) {
    if (query.select.length === 0) {
      fail(ctx, "Must have at least one select", null);
    }
    if (query.pivot && query.pivot.length > 0) {
      fail(
        ctx,
        "Pivot + select is not supported. You must use dimensions or measures instead.",
        null,
      );
    }
    selection = {
      select: query.select.flatMap((expr) =>
        "alias" in expr
          ? [bindAlias(ctx, expr)]
          : bindStar(ctx, { replace: expr.replace }),
      ),
    };
  } else if (query.infer && numInferItems > 0) {
    const boundExprs = query.infer.flatMap((field) =>
      field.op === "ident"
        ? [bindField(ctx, field)]
        : bindStar(ctx, { replace: field.replace }).map((alias) => alias.expr),
    );
    const boundFields: Field[] = [];
    for (let i = 0; i < boundExprs.length; i++) {
      const boundExpr = boundExprs[i];
      if (boundExpr.op === "field") {
        boundFields.push(boundExpr);
      } else {
        fail(ctx, "Cannot infer non-field", query.infer[i].loc);
      }
    }
    selection = {
      infer: boundFields,
      budget: query.inference_budget,
    };
  } else if (numGroupByItems > 0) {
    selection = {
      dimensions: (query.dimensions || []).map((alias) =>
        bindAlias(ctx, alias),
      ),
      pivot: (query.pivot || []).map((alias) => bindAlias(ctx, alias)),
      // TODO: Validate that these are actually aggregate expressions
      measures: (query.measures || []).map((alias) =>
        bindAlias({ ...ctx, bindingMeasures: true }, alias),
      ),
    };

    for (let i = 0; i < selection.measures.length; i++) {
      const m = selection.measures[i];
      let hasAgg = false;
      traverseExpr(m.expr, (expr) => {
        if (expr.op === "function" && FUNCTIONS[expr.name].isAgg) {
          hasAgg = true;
          return false;
        }
        return true;
      });

      if (!hasAgg) {
        fail(
          ctx,
          "Each measure must contain an aggregate function",
          query.measures?.[i].expr.loc,
        );
      }
    }
  } else {
    fail(ctx, "Must have either select or dimensions", null);
  }

  let comparisonKey: BoundExpr | undefined = undefined;
  let weightedScores: BoundAlias[] | undefined = undefined;
  let previewLength: number | undefined = undefined;

  if (query.comparison_key) {
    comparisonKey = bindExpr(ctx, query.comparison_key);
  }

  if (query.weighted_scores) {
    // @ts-ignore
    const scoresSchema = ctx.schema.properties?.scores;
    if (!scoresSchema) {
      fail(ctx, "Schema must have a 'scores' property", null);
    }
    const scoresCtx: BindContext = {
      schema: jsonSchemaObjectSchema.parse(scoresSchema),
      scope: {},
      queryText: undefined,
      bindingMeasures: false,
    };
    weightedScores = query.weighted_scores.map((alias) =>
      bindAlias(scoresCtx, alias),
    );
  }

  if (query.preview_length !== null && query.preview_length !== undefined) {
    previewLength = query.preview_length;
    if (!comparisonKey) {
      comparisonKey = bindExpr(ctx, { btql: "input" });
    }
  }

  let summary: BoundQuery["summary"] | undefined = undefined;
  if (comparisonKey || weightedScores || previewLength) {
    if (from?.shape !== "summary") {
      fail(
        ctx,
        "Summary fields (comparison_key, weighted_scores, preview_length) are only supported for traces",
        null,
      );
    }
    if (!comparisonKey) {
      fail(ctx, "comparison_key is required for summary", null);
    }
    summary = {
      comparison_key: comparisonKey,
      weighted_scores: weightedScores ?? [],
      custom_columns: customColumns ?? [],
      preview_length: previewLength,
    };
  }

  if (from?.shape !== "summary" && customColumns) {
    if (!("select" in selection)) {
      fail(ctx, "custom_columns are only supported for select queries", null);
    }
    selection.select = [...(selection.select ?? []), ...(customColumns ?? [])];
  }

  const sort = query.sort?.map((sortItem) => bindSortItem(ctx, sortItem));

  let limit: number | undefined = undefined;
  if (query.limit) {
    limit = query.limit;
    if (limit < 0) {
      fail(ctx, "Limit must be >= 0", null);
    }
  }

  return {
    from,
    unpivot,
    filter,
    sort,
    ...selection,
    limit: query.limit,
    cursor: query.cursor,
    summary,
  };
}

// Bind a SQL "from" clause.
//
// NOTE: `ctx.schema.properties` should contain the "from" table name. If bound successfully, the `ctx.schema` is
// mutated by replacing with the schema of the "from" clause.
export function bindFrom(
  ctx: BindContext,
  from: NonNullable<ParsedQuery["from"]>,
): BoundFrom {
  const nameParts = from.op === "ident" ? from.name : from.name.name;

  if (nameParts.length !== 1) {
    fail(ctx, "Invalid from clause. Must be a single part name", from.loc);
  }

  if (typeof nameParts[0] !== "string") {
    fail(ctx, "Invalid from clause. Must be a string", from.loc);
  }

  const name = nameParts[0];
  let schemaObject: JSONSchema | SchemaArray | undefined;

  const schema = ctx.schema;
  if (schema.type === "object") {
    if (!schema.properties?.[name]) {
      fail(ctx, `${name} not found in schema`, from.loc);
    }
    schemaObject = jsonSchemaObjectSchema.parse(schema.properties[name]).items;
  } else if (schema.type === "array") {
    schemaObject = schema.items;
  } else {
    fail(ctx, `Schema must be an object or array (not ${schema.type})`, null);
  }

  ctx.schema = checkValidFieldSchema(ctx, schemaObject);
  return {
    name,
    objects:
      from.op === "function"
        ? from.args.map((arg) => {
            if (arg.op !== "literal" || typeof arg.value !== "string") {
              fail(ctx, "objects filters must be string literals", arg.loc);
            }
            return arg.value;
          })
        : undefined,
    shape: from.op === "function" ? from.shape : undefined,
  };
}

export function bindSortItem(ctx: BindContext, item: SortExpr): BoundSortItem {
  const expr = parsePartialExpr(item.expr);
  if (
    expr.op === "ident" &&
    expr.name.length === 1 &&
    ctx.scope[expr.name[0]]
  ) {
    if (typeof expr.name[0] !== "string") {
      fail(ctx, "Invalid field reference", expr.loc);
    }
    return {
      alias: expr.name[0],
      dir: item.dir,
    };
  } else {
    return {
      expr: bindExpr(ctx, expr),
      dir: item.dir,
    };
  }
}

// NOTE: Each time we bind an alias, we add it to the scope so it can
// be used elsewhere.
export function bindAlias(ctx: BindContext, alias: AliasExpr): BoundAlias {
  const ret = {
    alias: alias.alias,
    expr: bindExpr(ctx, alias.expr),
  };
  ctx.scope[alias.alias] = ret.expr;
  return ret;
}

export function bindStar(
  ctx: BindContext,
  { replace }: { replace?: Record<string, Expr> },
): BoundAlias[] {
  return Object.keys(ctx.schema.properties || {}).map((name) =>
    bindAlias(ctx, {
      alias: name,
      expr:
        replace && replace[name]
          ? replace[name]
          : { op: "ident", name: [name] },
    }),
  );
}

export function bindExpr(ctx: BindContext, expr: Expr): BoundExpr {
  if ("btql" in expr) {
    // This allows users to pass raw btql snippets, so they can semi-structure queries.
    const parsedExpr = new Parser(expr.btql).parseExpr();
    return bindExpr(ctx, parsedExpr);
  }

  switch (expr.op) {
    case "literal":
      return bindLiteral(ctx, expr);
    case "interval":
      return {
        op: "interval",
        value: expr.value,
        unit: expr.unit,
      };
    case "ident":
      return bindField(ctx, expr);
    case "star":
      return fail(ctx, "Unsupported star expression", expr.loc);

    case "function":
      return bindFunction(ctx, expr);

    case "eq":
    case "is":
    case "ne":
    case "lt":
    case "le":
    case "gt":
    case "ge": {
      const left = bindExpr(ctx, expr.left);
      const right = bindExpr(ctx, expr.right);
      const [castLeft, castRight] = coerceTypes(
        getExprSchema(left),
        getExprSchema(right),
        false,
      );
      return {
        op: expr.op,
        left: applyCast(left, castLeft, ctx.skipFieldCasts),
        right: applyCast(right, castRight, ctx.skipFieldCasts),
      };
    }
    case "match": {
      const left = bindExpr(ctx, expr.left);
      const right = bindExpr(ctx, expr.right);

      if (getExprScalarType(left) === "string") {
        // Match is almost like eq, but if the LHS is a string, we want to cast the RHS to a string.
        return {
          op: expr.op,
          left,
          right: applyCast(right, "string", ctx.skipFieldCasts),
        };
      } else if (getExprScalarType(left) === "object") {
        // Or if it's an object, don't bother casting the RHS.
        return {
          op: expr.op,
          left,
          right,
        };
      }

      const [castLeft, castRight] = coerceTypes(
        getExprSchema(left),
        getExprSchema(right),
        false,
      );
      return {
        op: expr.op,
        left: applyCast(left, castLeft, ctx.skipFieldCasts),
        right: applyCast(right, castRight, ctx.skipFieldCasts),
      };
    }
    case "like":
    case "ilike": {
      const left = bindExpr(ctx, expr.left);
      const right = bindExpr(ctx, expr.right);
      return {
        op: expr.op,
        left: applyCast(left, "string", ctx.skipFieldCasts),
        right: applyCast(right, "string", ctx.skipFieldCasts),
      };
    }
    case "includes": {
      return {
        op: "includes",
        haystack: bindExpr(ctx, expr.haystack),
        needle: bindExpr(ctx, expr.needle),
      };
    }
    case "and":
    case "or":
      const children: Expr[] = [];
      if (expr.left && expr.right) {
        children.push(expr.left, expr.right);
      } else if (expr.children) {
        children.push(...expr.children);
      } else {
        fail(
          ctx,
          "Invalid boolean expression (expect left/right or children)",
          expr.loc,
        );
      }

      return {
        op: expr.op,
        children: children.map((child) =>
          applyCast(bindExpr(ctx, child), "boolean", ctx.skipFieldCasts),
        ),
      };

    case "if":
      const conds = expr.conds.map((cond) => ({
        cond: applyCast(
          bindExpr(ctx, cond.cond),
          "boolean",
          ctx.skipFieldCasts,
        ),
        then: bindExpr(ctx, cond.then),
      }));
      let elseExpr = bindExpr(ctx, expr.else);

      const finalType = weakestExprType(
        conds.map((c) => c.then).concat(elseExpr),
      );
      for (const cond of conds) {
        cond.then = applyCast(cond.then, finalType, ctx.skipFieldCasts);
      }
      elseExpr = applyCast(elseExpr, finalType, ctx.skipFieldCasts);

      return {
        op: expr.op,
        conds,
        else: elseExpr,
      };

    case "add":
    case "sub":
    case "mul":
    case "div":
    case "mod":
      // XXX TODO: Coerce both expressions to number
      return {
        op: expr.op,
        left: bindExpr(ctx, expr.left),
        right: bindExpr(ctx, expr.right),
      };

    case "neg":
      // XXX TODO: Coerce to number
      return {
        op: expr.op,
        expr: bindExpr(ctx, expr.expr),
      };

    case "not":
      // XXX TODO: Coerce to boolean
      return {
        op: expr.op,
        expr: bindExpr(ctx, expr.expr),
      };

    case "isnull":
    case "isnotnull":
      return {
        op: expr.op,
        expr: bindExpr(ctx, expr.expr),
      };
  }
}

function parsePartialExpr(expr: Expr): Expr {
  if ("btql" in expr) {
    // This allows users to pass raw btql snippets, so they can semi-structure queries.
    return new Parser(expr.btql).parseExpr();
  } else {
    return expr;
  }
}

export function bindLiteral(ctx: BindContext, expr: Expr): BoundExpr {
  if (expr.op !== "literal") {
    throw new Error("Expected literal");
  }

  const type: ScalarType | null = literalValueToScalarType(expr.value);

  if (!type) {
    fail(ctx, `Invalid literal: ${JSON.stringify(expr.value)}`, expr.loc);
  }

  return {
    op: "literal",
    value: expr.value,
    type: type,
  };
}

function checkValidFieldSchema(
  ctx: BindContext,
  schema: JSONSchema | SchemaArray | undefined,
): LogicalSchema {
  if (schema instanceof Object && !Array.isArray(schema)) {
    return schema;
  } else {
    fail(ctx, `Invalid field schema: ${JSON.stringify(schema)}`, null);
  }
}

export function bindField(ctx: BindContext, ident: Ident): BoundExpr {
  if (ident.name.length === 0) {
    fail(ctx, "Invalid empty field reference", ident.loc);
  }

  // This means if you directly reference an alias, we'll use that. However, we should
  // really improve this logic to allow you to reference a subfield of an expression (i.e.
  // something like "get_path")
  if (ctx.scope[ident.name[0]] && ident.name.length === 1) {
    return { ...ctx.scope[ident.name[0]] };
  }

  let currentSchema = ctx.schema;
  for (let i = 0; i < ident.name.length; i++) {
    checkValidFieldSchema(ctx, currentSchema);
    const part = ident.name[i];

    if (currentSchema.type === "null") {
      // Fields are always assumed to be nullable, so just skip over null types
      currentSchema = {};
      break;
    }

    if (
      typeof currentSchema !== "object" ||
      Array.isArray(currentSchema) ||
      (currentSchema.type && currentSchema.type !== "object")
    ) {
      fail(
        ctx,
        `Trying to access field "${part}" in a non-object${
          "type" in currentSchema ? " (" + currentSchema.type + ")" : ""
        } ${JSON.stringify(currentSchema)}`,
        ident.loc,
      );
    }

    if (currentSchema.type === "object") {
      let nextSchema = currentSchema.properties?.[part];
      if (!nextSchema) {
        if (currentSchema.additionalProperties) {
          nextSchema = currentSchema.additionalProperties;
        }
      }
      if (!nextSchema) {
        if (i === 0) {
          if (typeof part !== "string") {
            fail(ctx, "First part of field must be a string", ident.loc);
          }
          const lowerPart = part.toLowerCase();
          switch (lowerPart) {
            case "current_timestamp":
              return {
                op: "function",
                name: "current_timestamp",
                args: [],
              };
            case "current_date":
              return {
                op: "function",
                name: "current_date",
                args: [],
              };
          }
        }
        fail(ctx, `Field ${part} not found`, ident.loc);
      }

      if (Array.isArray(nextSchema) || typeof nextSchema !== "object") {
        fail(
          ctx,
          `Invalid field schema: ${JSON.stringify(nextSchema)}`,
          ident.loc,
        );
      }

      currentSchema = nextSchema;
    } else if (currentSchema.additionalProperties) {
      if (typeof currentSchema.additionalProperties === "object") {
        currentSchema = currentSchema.additionalProperties;
      } else {
        fail(
          ctx,
          `Invalid field schema: ${JSON.stringify(
            currentSchema.additionalProperties,
          )}`,
          ident.loc,
        );
      }
    } else if (currentSchema.anyOf || Array.isArray(currentSchema.type)) {
      const anyTypes = currentSchema.anyOf || currentSchema.type;
      const subFields = anyTypes!.map((schema) =>
        bindField(
          {
            ...ctx,
            schema:
              typeof schema === "object"
                ? schema
                : schema
                  ? {}
                  : fail(
                      ctx,
                      `Invalid field schema: ${JSON.stringify(schema)}`,
                      ident.loc,
                    ),
            scope: {},
          },
          {
            op: "ident",
            name: ident.name.slice(i),
            loc: ident.loc,
          },
        ),
      );
      currentSchema = {
        anyOf: subFields.map((field) => getExprSchema(field)),
      };
      break;
    } else if (currentSchema.oneOf) {
      fail!(ctx, `oneOf not supported yet`, ident.loc);
    } else if (currentSchema.allOf) {
      fail!(ctx, `allOf not supported yet`, ident.loc);
    } else if (currentSchema.not) {
      fail!(ctx, `not not supported yet`, ident.loc);
    } else if (currentSchema.enum) {
      fail!(ctx, `enum not supported yet`, ident.loc);
    } else if (currentSchema.const) {
      fail!(ctx, `const not supported yet`, ident.loc);
    } else if (currentSchema.$ref) {
      fail!(ctx, `$ref not supported yet`, ident.loc);
    } else if (currentSchema.$id) {
      fail!(ctx, `$id not supported yet`, ident.loc);
    } else if (currentSchema.$schema) {
      fail!(ctx, `$schema not supported yet`, ident.loc);
    } else if (currentSchema.multipleOf) {
      fail!(ctx, `multipleOf not supported yet`, ident.loc);
    } else if (currentSchema.if) {
      fail!(ctx, `if not supported yet`, ident.loc);
    } else if (currentSchema.then) {
      fail!(ctx, `then not supported yet`, ident.loc);
    } else if (currentSchema.else) {
      fail!(ctx, `else not supported yet`, ident.loc);
    } else if (currentSchema.type) {
      fail(
        ctx,
        `Trying to access field "${part}" in a non-object (${
          currentSchema.type
        }) ${JSON.stringify(currentSchema)}`,
        ident.loc,
      );
    } else {
      currentSchema = {};
    }
  }

  return {
    op: "field",
    name: ident.name,
    type: cleanupFieldTypes(currentSchema),
  };
}

function cleanupFieldTypes(schema: JSONSchema): LogicalSchema {
  if (typeof schema !== "object") {
    return {};
  } else if (schema.anyOf) {
    const pruned = schema.anyOf.map(cleanupFieldTypes).filter(
      (subSchema) =>
        subSchema !== null &&
        Object.keys(subSchema).length > 0 &&
        // zodToJsonType converts nullish() into this format, which we just don't need to handle
        // since null is perfectly fine to describe an empty field
        !(subSchema.not && Object.keys(subSchema.not).length === 0),
    );
    if (pruned.length === 0) {
      return {};
    } else if (pruned.length === 1) {
      return pruned[0];
    } else {
      return {
        anyOf: pruned,
      };
    }
  } else if (schema.type === "object") {
    return {
      ...schema,
      properties: Object.fromEntries(
        Object.entries(schema.properties || {}).map(([key, subSchema]) => [
          key,
          cleanupFieldTypes(jsonSchemaSchema.parse(subSchema)),
        ]),
      ),
      additionalProperties:
        schema.additionalProperties &&
        typeof schema.additionalProperties === "object"
          ? cleanupFieldTypes(schema.additionalProperties)
          : undefined,
    };
  } else if (schema.type === "array") {
    return {
      ...schema,
      items: Array.isArray(schema.items)
        ? schema.items.map(cleanupFieldTypes)
        : schema.items,
    };
  } else {
    return schema;
  }
}

export function bindFunction(
  ctx: BindContext,
  expr: ParsedFunction,
): FunctionExpr {
  if (expr.name.name.length !== 1) {
    fail(ctx, "Invalid function name", expr.loc);
  }

  if (typeof expr.name.name[0] !== "string") {
    fail(ctx, "First piece of ident must be a string", expr.loc);
  }

  let fname = expr.name.name[0].toLowerCase();
  if (FUNCTION_ALIAS[fname]) {
    fname = FUNCTION_ALIAS[fname];
  }
  const nameParsed = functionNameSchema.safeParse(fname);
  if (!nameParsed.success) {
    fail(ctx, `Unknown function: ${expr.name.name[0]}`, expr.loc);
  }

  const name = nameParsed.data;
  const functionDef = FUNCTIONS[name];

  if (!ctx.bindingMeasures && functionDef.isAgg) {
    fail(ctx, `Cannot use aggregate function in non-measure context`, expr.loc);
  }

  // For now, we don't support named arguments, so just "zip" the names and values together,
  // using null for missing values.
  const numVariadic = functionDef.args.variadic?.length ?? 0;
  if (
    (numVariadic > 0 &&
      expr.args.length < Object.keys(functionDef.args.named).length) ||
    (numVariadic === 0 &&
      expr.args.length !== Object.keys(functionDef.args.named).length)
  ) {
    fail(
      ctx,
      `Expected ${Object.keys(functionDef.args).length} arguments (received ${
        expr.args.length
      }) for function: ${name}`,
      expr.loc,
    );
  }

  const namedArgs = Object.values(functionDef.args.named).map((type, idx) => ({
    type,
    expr:
      idx < expr.args.length
        ? expr.args[idx]
        : // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          ({
            op: "literal",
            value: null,
          } as Expr),
  }));

  const remainingArgs = expr.args.slice(namedArgs.length);
  if (remainingArgs.length > 0 && !functionDef.args.variadic) {
    fail(ctx, `Function ${name} does not accept variadic arguments`, expr.loc);
  }

  const variadicArgs = remainingArgs.map((arg, idx) => ({
    type: [functionDef.args.variadic![idx % numVariadic]],
    expr: arg,
  }));

  // Functions that are explicit casts (e.g. to_string, to_number) are converted to a cast operation,
  // which is technically not a function. They take the type what we are casted to, which we extract
  // from the function def.
  if (fname.startsWith("to_")) {
    return {
      op: "cast",
      expr: bindExpr(ctx, expr.args[0]),
      type: weakestScalarType(functionDef.type(Object.fromEntries([]), [])),
    };
  }

  const args = namedArgs.concat(variadicArgs).map(({ type, expr }, idx) => {
    let value = bindExpr(ctx, expr);
    const simpleType = deriveScalarTypes(getExprSchema(value));
    const maybeCast = castToClosest(simpleType, deriveScalarTypes(type));
    if (maybeCast) {
      value = {
        op: "cast",
        expr: value,
        type: maybeCast,
      };
    }
    return value;
  });

  const boundNamedArgs = args.slice(0, namedArgs.length);
  const boundVariadicArgs = args.slice(boundNamedArgs.length);
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const variadicArgSets = variadicArgs.map((arg) => [] as BoundExpr[]);
  for (let i = 0; i < boundVariadicArgs.length; i++) {
    variadicArgSets[i % numVariadic].push(boundVariadicArgs[i]);
  }
  const variadicArgTypes = variadicArgSets.map((args) =>
    coerceAllExprTypes(args, true),
  );
  for (let i = 0; i < boundVariadicArgs.length; i++) {
    boundVariadicArgs[i] = applyCast(
      boundVariadicArgs[i],
      variadicArgTypes[i % numVariadic],
      ctx.skipFieldCasts,
    );
  }

  return {
    op: "function",
    name,
    args: boundNamedArgs.concat(boundVariadicArgs),
  };
}

function fail(
  ctx: BindContext,
  prefix: string,
  loc: Loc | null | undefined,
): never {
  throw new BindError(prefix, loc, ctx.queryText);
}

export class BindError extends Error {
  constructor(
    public prefix: string,
    public readonly loc: Loc | null | undefined,
    public readonly query?: string,
  ) {
    // Find the substring corresponding to the location
    const lines = (query || "").split("\n");

    const errorText =
      loc && query
        ? lines
            .slice(loc.start.line - 1, loc.end.line - loc.start.line + 1)
            .map((line, idx) => {
              const startCol = idx === 0 ? loc.start.col - 1 : 0;
              const endCol =
                idx === lines.length - 1 ? loc.end.col : line.length;

              const marker =
                " ".repeat(startCol) + "^".repeat(endCol - startCol - 1);

              // right pad by 4 spaces
              const idxText = `${loc.start.line + idx}:`.padEnd(4, " ");

              return `${idxText}${line}\n    ${marker}`;
            })
            .join("\n")
        : "";

    super(
      `${prefix}${
        loc && errorText
          ? ` ... at line ${loc.start.line}, column ${loc.start.col}:\n` +
            errorText
          : ""
      }`,
    );
  }
}

export function traverseQuery(
  query: BoundQuery,
  // Returns true if we should continue traversing
  handleFn: (expr: BoundExpr) => boolean,
) {
  if (query.filter) {
    traverseExpr(query.filter, handleFn);
  }
  if (query.sort) {
    query.sort.forEach((sort) =>
      "expr" in sort ? traverseExpr(sort.expr, handleFn) : null,
    );
  }
  if ("select" in query) {
    query.select.forEach((alias) => traverseExpr(alias.expr, handleFn));
  }
  if ("dimensions" in query) {
    query.dimensions.forEach((alias) => traverseExpr(alias.expr, handleFn));
  }
  if ("pivot" in query) {
    query.pivot.forEach((alias) => traverseExpr(alias.expr, handleFn));
  }
  if ("unpivot" in query) {
    query.unpivot.forEach((expr) => traverseExpr(expr.expr, handleFn));
  }
  if ("measures" in query) {
    query.measures.forEach((alias) => traverseExpr(alias.expr, handleFn));
  }
}

export function traverseExpr(
  expr: BoundExpr,
  // Returns true if we should continue traversing
  handleFn: (expr: BoundExpr) => boolean,
) {
  if (!handleFn(expr)) {
    return;
  }
  switch (expr.op) {
    case "literal":
      break;
    case "interval":
      break;
    case "field":
      break;
    case "function":
      expr.args.forEach((arg) => traverseExpr(arg, handleFn));
      break;
    case "cast":
      traverseExpr(expr.expr, handleFn);
      break;
    case "eq":
    case "is":
    case "ne":
    case "lt":
    case "le":
    case "gt":
    case "ge":
    case "add":
    case "sub":
    case "mul":
    case "div":
    case "mod":
    case "like":
    case "ilike":
    case "match":
      traverseExpr(expr.left, handleFn);
      traverseExpr(expr.right, handleFn);
      break;
    case "and":
    case "or":
      expr.children?.forEach((child) => traverseExpr(child, handleFn));
      break;
    case "includes":
      traverseExpr(expr.haystack, handleFn);
      traverseExpr(expr.needle, handleFn);
      break;
    case "neg":
    case "not":
    case "isnull":
    case "isnotnull":
      traverseExpr(expr.expr, handleFn);
      break;
    case "if":
      expr.conds.forEach((cond) => {
        traverseExpr(cond.cond, handleFn);
        traverseExpr(cond.then, handleFn);
      });
      traverseExpr(expr.else, handleFn);
      break;
    default:
      const _: never = expr;
  }
}

// NOTE(austin): In Brainstore, we skip field casts because applying (and
// compressing) a cast to a field expr overwrites the type of the field with
// the cast type, causing us to lose field type information that we need for
// fast fields. In general, a lot of these casts are unnecessary due to dynamic
// typing, so we can remove more after we fork the binder.
//
// Note that it's not sufficient to just check whether `a` is a field at the
// start of `applyCast` because compressCasts is recursive -- if we encounter a
// field nested inside of multiple casts, we still want to skip the cast even
// though the expr inside the top-level cast wasn't a field.
export function applyCast(
  a: BoundExpr,
  t: ScalarType | null,
  skipFieldCasts?: boolean,
): BoundExpr {
  if (t === null) {
    return a;
  }

  const lowest = getExprScalarType(a);
  if (lowest === t) {
    return a;
  } else {
    return compressCast({ op: "cast", expr: a, type: t }, skipFieldCasts);
  }
}

// When casting field access, especially through JSON, it's important
// that we instruct the SQL system to extract the value into the correct
// type. For example, if you run a query like:
//   `output = '1'`
// it will be converted into
//  `CAST(output, 'string')='1'`
// but the type of `output` is JSON, so the normal method of:
//  `(data->'output')::text='1'`
// will not work. Instead, we actually want to help the planner do:
//  `data->>'output'='1'`
//
// NOTE: Ideally this is done in an optimizer or post-binding pass, rather
// than everywhere we apply cast nodes, but since we somewhat arbitrarily
// called bindExpr() in various places, it's more convenient to do it as we go.
function compressCast(cast: CastExpr, skipFieldCasts?: boolean): BoundExpr {
  const expr =
    cast.expr.op === "cast"
      ? compressCast(cast.expr, skipFieldCasts)
      : cast.expr;
  if (expr.op === "field") {
    if (skipFieldCasts) {
      return expr;
    } else {
      return {
        ...expr,
        type: scalarTypeToLogicalSchema(cast.type),
      };
    }
  } else {
    return cast;
  }
}

export function stripCast(cast: BoundExpr): BoundExpr {
  if (cast.op === "cast") {
    return stripCast(cast.expr);
  } else {
    return cast;
  }
}
