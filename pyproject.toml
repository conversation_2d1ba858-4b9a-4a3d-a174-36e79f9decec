[tool.black]
line-length = 119

[tool.ruff]
line-length = 119

[tool.ruff.lint]
select = [
    "I",    # isort
    # NOTE: disabling F401 because we individually process code snippets in docs
    # and this would remove imports used in later snippets which makes the examples
    # not useful.
    #"F401", # unused imports
]
[tool.ruff.lint.isort]
known-third-party = ["braintrust_core", "braintrust_local", "autoevals"]

[tool.pytest.ini_options]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "function"
