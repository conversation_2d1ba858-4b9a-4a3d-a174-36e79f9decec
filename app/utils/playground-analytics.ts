"use server";

import { trackServerEvent } from "#/utils/server-analytics";
import type { AuthLookup } from "#/utils/server-util";
import type { z } from "zod";
import type { playgroundRunEventSchema } from "#/utils/server-analytics";

export type PlaygroundRunEvent = z.infer<typeof playgroundRunEventSchema>["properties"];

/**
 * Track a playground run event
 * This function can be called from server actions or API routes
 */
export async function trackPlaygroundRun(
  event: PlaygroundRunEvent,
  authLookupRaw?: AuthLookup,
): Promise<{ success: true }> {
  await trackServerEvent("playgroundRun", event, {
    authLookup: authLookupRaw,
  });
  return { success: true };
}

/**
 * Track a successful playground run
 */
export async function trackPlaygroundRunSuccess(
  event: Omit<PlaygroundRunEvent, "outcome">,
  authLookupRaw?: AuthLookup,
): Promise<void> {
  await trackPlaygroundRun({ ...event, outcome: "success" }, authLookupRaw);
}

/**
 * Track a failed playground run
 */
export async function trackPlaygroundRunError(
  event: Omit<PlaygroundRunEvent, "outcome"> & { errorMessage: string },
  authLookupRaw?: AuthLookup,
): Promise<void> {
  await trackPlaygroundRun({ ...event, outcome: "error" }, authLookupRaw);
}

/**
 * Track a cancelled playground run
 */
export async function trackPlaygroundRunCancelled(
  event: Omit<PlaygroundRunEvent, "outcome">,
  authLookupRaw?: AuthLookup,
): Promise<void> {
  await trackPlaygroundRun({ ...event, outcome: "cancelled" }, authLookupRaw);
}
