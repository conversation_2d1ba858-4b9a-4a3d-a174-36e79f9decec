"use client";

import * as duckdb from "@duckdb/duckdb-wasm";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { DataType, type Schema, type Table, type TypeMap } from "apache-arrow";
import {
  isEmpty,
  isObject,
  mapObject,
  normalizeArrowForJSON,
  notEmpty,
  strMax,
} from "#/utils/object";
import { useOrg, useUser } from "./user";
import {
  type APIVersion,
  type DataObjectSearch,
  type DataObjectType,
  type VersionedData,
  buildSearchKey,
  getIdAndParams,
  useDataObject,
} from "./btapi/btapi";
import { apiFetchGet, apiPostObject } from "./btapi/fetch";
import { loadBtCacheDB } from "./btapi/load-bt-cache-db";
import {
  type DuckDBJSONStruct,
  type DuckDBJSONType,
  DuckDBTypeHints,
  JSONType,
  getDuckDBTypeChild,
} from "./schema";
import { Mutex } from "async-mutex";
import {
  RealtimeChannel,
  type Roster,
  type SetRosterFn,
} from "./realtime-data";
import { type OrgContextT, type UserContextT } from "./user-types";
import {
  IS_MERGE_FIELD,
  type TransactionId,
  mapAt,
  mapSetDefault,
} from "@braintrust/core";
import { doubleQuote, ident, singleQuote } from "./sql-utils";
import isEqual from "lodash.isequal";
import { sha1 } from "./hash";
import {
  IdField,
  SpanIdField,
  RootSpanIdField,
  TransactionIdField,
  UpdateNonceField,
  ObjectDeleteField,
  CreatedField,
  UserIdField,
  AuditSourceField,
  AuditMetadataField,
  OutputField,
  ExpectedField,
} from "@braintrust/local/query";
import { createArrowTableFromRecords } from "./arrow";
import { type OpenedRow } from "@braintrust/realtime/types";
import { useFeatureFlags } from "#/lib/feature-flags";
import { toast } from "sonner";
import {
  type LoadedBtSessionToken,
  useSessionToken,
} from "./auth/session-token";
export type { TransactionId } from "@braintrust/core";
import { filterObject } from "@braintrust/local/query";
import {
  globalSpecHashSubscriptions,
  useUpdateLogSubscriptions,
} from "./update-log-subscriptions";
import { type Shape } from "@braintrust/btql/parser";
import { atom, useSetAtom } from "jotai";

export {
  IdField,
  SpanIdField,
  RootSpanIdField,
  TransactionIdField,
  UpdateNonceField,
  ObjectDeleteField,
  CreatedField,
  UserIdField,
  OrgIdField,
  LogIdField,
  ProjectIdField,
  ExperimentIdField,
  DatasetIdField,
  PromptSessionIdField,
  ScoresField,
  MetricsField,
  MetadataField,
  SpanAttributesField,
  AuditSourceField,
  AuditMetadataField,
  OutputField,
  ExpectedField,
  TagsField,
  OriginField,
} from "@braintrust/local/query";

// const fetchBundles = async (): Promise<duckdb.DuckDBBundles> => {
//   await fetch("/wasm/duckdb-mvp.wasm");
//   await fetch("/wasm/duckdb-eh.wasm");

//   return {
//     mvp: {
//       mainModule: "/wasm/duckdb-mvp.wasm",
//       mainWorker: new URL(
//         "@duckdb/duckdb-wasm/dist/duckdb-browser-mvp.worker.js",
//         import.meta.url,
//       ).toString(),
//     },
//     eh: {
//       mainModule: "/wasm/duckdb-eh.wasm",
//       mainWorker: new URL(
//         "@duckdb/duckdb-wasm/dist/duckdb-browser-eh.worker.js",
//         import.meta.url,
//       ).toString(),
//     },
//   };
// };

const JSDELIVR_BUNDLES = duckdb.getJsDelivrBundles();

// If you change this, make sure to adjust any of the subsequent insertion
// limits (each tied to different operations) appropriately.
const DuckDBMemoryLimitStr = "8GB";

const duckDBInsertionCategories = [
  "index_db_initial_records",
  "realtime",
  "optimistic_update",
  "model_costs",
] as const;
export type DuckDBInsertionCategory =
  (typeof duckDBInsertionCategories)[number];

// Insertion limits for different types of inserts going into a DuckDB database.
// These limits are independent of each other, meaning for example that we can
// fill up the `index_db_initial_records` limit without affecting the `realtime`
// limit.
const DuckDBInsertionLimits: Record<DuckDBInsertionCategory, number> = {
  index_db_initial_records: 100 * 1000 * 1000,
  realtime: 100 * 1000 * 1000,
  optimistic_update: 100 * 1000 * 1000,
  model_costs: 100 * 1000 * 1000,
};

// Keep track of the number of bytes inserted for each insertion category, for
// each instance of the DB created by `initializeDuckDB`.
const DuckDBInstanceToBytesInserted = new Map<
  duckdb.AsyncDuckDB,
  Record<DuckDBInsertionCategory, number>
>();

function makeDuckDBInsertionCategoryMap<V>(
  makeV: () => V,
): Record<DuckDBInsertionCategory, V> {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  return Object.fromEntries(
    duckDBInsertionCategories.map((x) => [x, makeV()]),
  ) as Record<DuckDBInsertionCategory, V>;
}

export async function initializeDuckDB() {
  // Select a bundle based on browser checks
  const bundle = await duckdb.selectBundle(JSDELIVR_BUNDLES);

  const worker_url = URL.createObjectURL(
    new Blob([`importScripts("${bundle.mainWorker!}");`], {
      type: "text/javascript",
    }),
  );

  // Instantiate the asynchronous version of DuckDB-wasm
  // https://github.com/vercel/next.js/issues/74621#issuecomment-2585760180
  const worker = new window.Worker(worker_url);
  const logger = new duckdb.VoidLogger();
  const db = new duckdb.AsyncDuckDB(logger, worker);
  await db.instantiate(bundle.mainModule, bundle.pthreadWorker);
  URL.revokeObjectURL(worker_url);

  // Configure a memory limit.
  const conn = await db.connect();
  try {
    await conn.query(
      // The ICU extension lets us use timezones.
      // https://github.com/duckdb/duckdb/issues/11944 explains in more detail
      `SET memory_limit=${singleQuote(
        DuckDBMemoryLimitStr,
      )}; INSTALL icu; LOAD icu;`,
    );
  } finally {
    await conn.close();
  }

  // Initialize an entry in DuckDBInstanceToBytesInserted for this new instance.
  DuckDBInstanceToBytesInserted.set(
    db,
    makeDuckDBInsertionCategoryMap(() => 0),
  );

  // for debugging
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  (window as any).db = db;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  (window as any).debugRunQuery = async (query: string) => {
    const start = Date.now();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const conn = await (window as any).db.connect();
    const result = (await conn.query(query))
      .toArray()
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      .map((r: any) => r.toJSON());
    console.log("debugRunQuery", Date.now() - start, "ms");
    return result;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  (window as any).dbBytesInserted = mapAt(DuckDBInstanceToBytesInserted, db);

  return db;
}

export const DuckDBContext = createContext<duckdb.AsyncDuckDB | null>(null);
export type DuckDBContextType = duckdb.AsyncDuckDB | null;
export const duckDBAtom = atom<duckdb.AsyncDuckDB | null>(null);

export function DuckDBProvider({ children }: { children: React.ReactNode }) {
  const [db, setDB] = useState<duckdb.AsyncDuckDB | null>(null);
  const initialized = useRef(false);
  const setDuckDB = useSetAtom(duckDBAtom);
  useEffect(() => {
    const connect = async () => {
      if (!initialized.current) {
        // This ref only allows DuckDB to be initialized once. It's very important that we set it
        // like this _before_ calling await. We should probably make this a bit more robust (e.g.
        // if there's a connection error, allow re-initializing).
        initialized.current = true;
        const d = await initializeDuckDB();
        setDB(d);
        setDuckDB(d);

        // (window as any).duck = d;
      }
    };
    if (!setDuckDB) {
      return;
    }
    connect();
  }, [initialized, setDuckDB]);

  useEffect(() => {
    return () => {
      for (const name of Object.keys(globalChannels)) {
        delete globalChannels[name];
      }
    };
  }, []);

  return <DuckDBContext.Provider value={db}>{children}</DuckDBContext.Provider>;
}

export function useDuckDB() {
  if (!DuckDBContext) {
    throw new Error("useDuckDB must be used within a DuckDBProvider");
  }
  return useContext(DuckDBContext);
}

export function useDuckConn() {
  // NOTE: We may want to make this pattern more pervasive. It seems like it can take
  // 50-100ms to acquire a duckdb connection.
  const [conn, setConn] = useState<duckdb.AsyncDuckDBConnection | null>(null);
  const duck = useDuckDB();
  useEffect(() => {
    (async () => {
      if (duck) {
        setConn(await duck.connect());
      }
    })();
  }, [duck]);

  return { conn };
}

export async function createTableFromArrow<T extends TypeMap>(
  conn: duckdb.AsyncDuckDBConnection,
  name: string,
  table: Table<T>,
) {
  let active = false;
  while (true) {
    try {
      await conn.query("begin");
      active = true;

      await conn.query(`drop table if exists "${name}"`);
      await conn.insertArrowTable(table, { name });

      await conn.query("commit");
      active = false;
      break;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    } catch (e: any) {
      if (e.message.includes("Catalog write-write conflict on")) {
        // Disabling this warning because it's noisy, and may happen often now that we load experiments
        // and datasets concurrently
        // console.warn("Transient failure", e.message);
        console.warn(
          "NOTE: If you see a 'Catalog write-write conflict' warning, you can ignore it",
        );
        continue;
      }
      throw e;
    } finally {
      if (active) {
        await conn.query("rollback");
      }
    }
  }
}

export async function createTableFromArrowSchema<T extends TypeMap>(
  conn: duckdb.AsyncDuckDBConnection,
  name: string,
  schema: Schema<T>,
  hints: DuckDBJSONStruct,
) {
  const spec = Object.fromEntries(
    schema.fields.map((f) => [
      f.name,
      getDuckDBTypeChild(hints, f.name) === JSONType
        ? JSONType
        : arrowTypeToDuckDBSpec(f.type),
    ]),
  );

  while (true) {
    try {
      await conn.query(`create or replace table ${doubleQuote(name)} AS
        select a.* FROM (select json_transform('null', ${singleQuote(
          JSON.stringify(spec),
        )}) AS a) limit 0`);
      break;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    } catch (e: any) {
      if (e.message.includes("Catalog write-write conflict on")) {
        // Disabling this warning because it's noisy, and may happen often now that we load experiments
        // and datasets concurrently
        // console.warn("Transient failure", e.message);
        continue;
      }
      throw e;
    }
  }
}

export interface MaterializedArrowState {
  refreshed: number;
  schema: Schema | null;
  hasLoaded: boolean;
}

async function deriveTableSchema(
  conn: duckdb.AsyncDuckDBConnection,
  tableName: string,
): Promise<Schema> {
  const result = await conn.query(`SELECT * FROM "${tableName}" LIMIT 0`);
  return result.schema;
}

export function useMaterializedArrow<T extends TypeMap>(
  name: string | null,
  table: Table<T> | null,
  searchField?: string,
): MaterializedArrowState & { name: string | null } {
  const duck = useDuckDB();

  const [state, setState] = useState<MaterializedArrowState>({
    refreshed: 0,
    schema: null,
    hasLoaded: false,
  });

  useEffect(() => {
    const reload = async () => {
      if (state.refreshed == 0 && duck && name !== null) {
        // NOTE: Creating a separate connection here because I'm assuming two connections can't
        // be in two concurrent async methods
        const c = await duck.connect();
        const results = await c.query(
          `SELECT 1 FROM information_schema.tables WHERE table_name = ${singleQuote(
            name,
          )}`,
        );
        if (results.numRows > 0) {
          const schema = await deriveTableSchema(c, name);
          // Table already exists, so increment the refreshed counter
          setState((prev) => ({
            refreshed: prev.refreshed + 1,
            schema,
            hasLoaded: true,
          }));
        }
      }
    };
    reload();
  }, [duck, name, state.refreshed]);

  useEffect(() => {
    const reload = async () => {
      if (table !== null && name !== null && duck) {
        const conn = await duck.connect();
        await createTableFromArrow(conn, name, table);
        if (searchField) {
          await conn.query(
            `PRAGMA create_fts_index("${name}", id, ${searchField}, lower=1, overwrite=1)`,
          );
        }
        const schema = await deriveTableSchema(conn, name);
        setState((prev) => ({
          refreshed: prev.refreshed + 1,
          schema,
          hasLoaded: true,
        }));
      }
    };
    reload();
  }, [table, duck, name, searchField]);

  return { ...state, name };
}

export function useMaterializedRecords(
  name: string | null,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  records: any[] | null,
  schema?: Schema,
  searchField?: string,
) {
  const [arrowTable, setArrowTable] = useState<Table | null>(null);
  useEffect(() => {
    setArrowTable(createArrowTableFromRecords(records, schema));
  }, [records, schema]);
  return useMaterializedArrow(name, arrowTable, searchField);
}

// When we save parquet files, we don't update the catalog, because we do not want
// to "fix" the schema. That allows us to scan a union of parquet files and merge their
// schemas together on the fly.
export function useParquetView<T extends TypeMap>({
  objectType,
  search,
  disableCache,
  disableRealtime,
}: {
  objectType: DataObjectType;
  search:
    | string
    | DataObjectSearch
    | { key: string; search: DataObjectSearch }
    | undefined;
  disableCache?: boolean;
  disableRealtime?: boolean;
}): {
  refreshed: number;
  fname: string | null;
  schema: Schema | null;
  scan: string | null;
  channel: () => UpdateLog | null;
  roster: Roster;
  error: Error | undefined;
  isDirty: boolean | undefined;
  isLoadingInitial: boolean;
} {
  const {
    data: buf,
    refreshed: ready,
    error,
    isDirty,
    isLoadingInitial,
  } = useDataObject({
    objectType,
    search: search ?? null,
    disableCache,
  });

  // TODO(manu): Properly propagate undefined objectId through the function.
  const name = buildSearchKey(search);
  const { id } = useMemo(
    () => (search ? getIdAndParams(search) : { id: null }),
    [search],
  );

  // Use the ready signal explicitly to determine whether we need to update
  // the buffer map.
  // eslint-disable-next-line react-compiler/react-compiler
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const bufMap = useMemo(() => (buf ? { [name]: buf } : {}), [name, ready]);

  const readyMap = useMemo(
    () => (ready ? { [name]: ready } : {}),
    [name, ready],
  );
  const channelSpecMap = useMemo(() => {
    const s = isObject(search) && "search" in search ? search.search : search;
    if (id && !(isObject(s) && !isEmpty(s.version))) {
      return {
        [name]: {
          objectType,
          id,
          shape: (s instanceof Object ? s.shape : undefined) ?? "traces",
          audit_log: (s instanceof Object && s.audit_log) ?? false,
        },
      };
    }

    return {};
  }, [id, name, objectType, search]);
  const {
    refreshed: multiRefreshed,
    fnames,
    schema: multiSchema,
    scans: multiScans,
    filenameToChannel,
  } = useParquetViews<T>({
    files: bufMap,
    ready: readyMap,
    channelSpecs: channelSpecMap,
    disableCache,
    disableRealtime,
  });

  const channel = useCallback(
    () => filenameToChannel(name)?.channel ?? null,
    [filenameToChannel, name],
  );
  const roster = useMemo(
    () => filenameToChannel(name)?.roster ?? [],
    [filenameToChannel, name],
  );

  return {
    refreshed: multiRefreshed[name] || 0,
    fname: fnames[name] || null,
    schema: multiSchema[name] || null,
    scan: multiScans[name] || null,
    channel,
    roster,
    error,
    isDirty,
    isLoadingInitial,
  };
}

function defaultFieldValue(
  fieldName: string,
  applyLegacyDatasetMutation: boolean,
) {
  switch (fieldName) {
    // DEPRECATION_NOTICE: Do the coalescing at the backend DB level. Once we
    // have upgraded everyone's cloud formations, we can remove this from the
    // webapp.
    case RootSpanIdField:
    case SpanIdField:
      return `COALESCE(${ident(fieldName)}, ${ident(IdField)}) AS ${ident(
        fieldName,
      )}`;
    case ExpectedField:
      const parquetField = applyLegacyDatasetMutation ? OutputField : fieldName;
      return `${ident(parquetField)} AS ${ident(fieldName)}`;
    // DEPRECATION_NOTICE: Remove this once we have upgraded everyone's cloud
    // formations to return TEXT for the transaction ID field.
    case TransactionIdField:
      return `CAST(${ident(fieldName)} AS VARCHAR) AS ${ident(fieldName)}`;
    default:
      return ident(fieldName);
  }
}

export const DEFAULT_SHAPE: Shape = "traces";
export interface ChannelSpec {
  objectType: DataObjectType;
  id: string;
  shape: Shape;
  audit_log: boolean;
}

function hashChannelSpec(spec: ChannelSpec) {
  return sha1(JSON.stringify(spec));
}

const MaxXactId = "9223372036854775807"; // 2^63-1

function emptyResultScan(
  targetFname: string,
  applyLegacyDatasetMutation: boolean,
) {
  const excludeExpr = applyLegacyDatasetMutation && `${doubleQuote("output")}`;
  const addExpr =
    applyLegacyDatasetMutation &&
    `${doubleQuote("output")} as ${doubleQuote("expected")}`;
  return `
    SELECT
      * ${excludeExpr ? `EXCLUDE (${excludeExpr})` : ""}
      ${addExpr ? `${addExpr}` : ""}
      REPLACE (CAST(${ident(TransactionIdField)} AS VARCHAR) AS ${ident(
        TransactionIdField,
      )}),
      false AS ${ObjectDeleteField},
      NULL::bigint AS ${UpdateNonceField}
    FROM parquet_scan('${targetFname}') LIMIT 0`;
}

function applyLegacyDatasetMutation(
  objectType: DataObjectType,
  api_version: APIVersion,
): boolean {
  return objectType === "dataset" && api_version === 1;
}

function channelSpecTableName(specHash: string) {
  return `${specHash}_log`;
}

const globalChannels: Record<string, UpdateLog> = {};

async function initializeChannel({
  specHash,
  spec,
  emptyResult,
  conn,
  triggeringParquetFileName,
  updateRefreshed,
  initUpdateLogArgs,
  setRoster,
  disableCache,
}: {
  specHash: string;
  spec: ChannelSpec;
  // Schema of the table.
  emptyResult: Table;
  conn: duckdb.AsyncDuckDBConnection;
  // The name of the parquet file that triggered this channel initialization.
  triggeringParquetFileName: string;
  // Bump the refreshed counter for the given spec hash.
  updateRefreshed: (specHash: string) => void;
  initUpdateLogArgs: {
    apiUrl: string;
    sessionToken: LoadedBtSessionToken;
    user: UserContextT["user"];
    org: OrgContextT;
  };
  setRoster: SetRosterFn;
  disableCache: boolean;
}): Promise<void> {
  if (globalChannels[specHash]) {
    return;
  }
  const logTableName = channelSpecTableName(specHash);

  await createTableFromArrowSchema(
    conn,
    logTableName,
    emptyResult.schema,
    DuckDBTypeHints[spec.objectType],
  );

  const pingSubscribers = () => {
    const existingSubscriptions = globalSpecHashSubscriptions.get(specHash);
    if (!existingSubscriptions) {
      return;
    }
    for (const subscription of existingSubscriptions.values()) {
      subscription();
    }
  };

  // Backfilling from indexDB does not need to block initializing the
  // channel, so we fire-and-forget this.
  (async () => {
    if (disableCache) {
      return;
    }
    // Use 0+0 so that it works in ORDER BY
    const xactIdField =
      emptyResult.schema.fields.find((f) => f.name === TransactionIdField)
        ?.name || "(0+0)";
    const maxXactIdResult = await conn.query(`
      SELECT MAX(${xactIdField}) m FROM parquet_scan(${singleQuote(
        triggeringParquetFileName,
      )})
    `);
    const maxXactId = maxXactIdResult.toArray()[0].toJSON().m;

    const btCachedDB = await loadBtCacheDB();

    // Load records in a loop, batched by a max size to avoid loading too
    // many records into memory in case indexDB is enormous.
    const MaxIndexDBBatchSize = 1000;
    let nextCursorLowerBoundKey: string[] | null = [logTableName];
    let totalRecordsInserted = 0;
    while (nextCursorLowerBoundKey) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      const initialRecords: any[] = [];
      const objectStore =
        btCachedDB &&
        btCachedDB
          .transaction([`${spec.objectType}_log`], "readwrite")
          .objectStore(`${spec.objectType}_log`);
      if (!objectStore) break;
      const request = objectStore.openCursor(
        IDBKeyRange.bound(nextCursorLowerBoundKey, [logTableName, MaxXactId]),
      );
      nextCursorLowerBoundKey = await new Promise((resolve, reject) => {
        request.onsuccess = function (event) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          const cursor = (event.target as any)?.result;
          // Check if there are no (more) cursor items to iterate through
          if (!cursor) {
            resolve(null);
            return;
          } else if (initialRecords.length >= MaxIndexDBBatchSize) {
            resolve(cursor.key);
            return;
          }

          if (cursor.value[TransactionIdField] < maxXactId) {
            objectStore.delete(cursor.primaryKey);
          } else {
            initialRecords.push(cursor.value);
          }
          cursor.continue();
        };
        request.onerror = function (e) {
          console.warn(
            `Failed to read log entries from local cache for ${logTableName}`,
          );
          resolve(null);
        };
      });

      if (initialRecords.length === 0) {
        break;
      }
      const res = await insertRecords(
        conn,
        emptyResult.schema,
        DuckDBTypeHints[spec.objectType],
        logTableName,
        initialRecords,
        "index_db_initial_records",
      );
      if (res === InsertRecordsResult.FAILURE_EXCEEDS_MAX_BYTES) {
        console.warn(
          `Aborting indexDB refresh after inserting ${totalRecordsInserted} records due to memory limits`,
        );
        break;
      }
      totalRecordsInserted += initialRecords.length;
    }
  })()
    .then(() => {
      // Once we've finished populating the log, make sure to let data subscribers know about that, so that
      // their "refreshed" counter increments, and they run queries with the new data.
      pingSubscribers();
    })
    .catch(console.error);

  const log = new UpdateLog(
    conn,
    spec.objectType,
    logTableName,
    emptyResult,
    pingSubscribers,
    disableCache,
  );

  if (globalChannels[specHash]) {
    return;
  }
  globalChannels[specHash] = log;

  // Do not await this, because we don't want to block the UI
  (async () => {
    try {
      const { apiUrl, sessionToken, user, org } = initUpdateLogArgs;
      const fetchChannelUrl =
        `${apiUrl}/broadcast-key?` +
        new URLSearchParams({
          object_type: spec.objectType,
          id: spec.id,
          audit_log: spec.audit_log ? "1" : "0",
        });
      await log.init({ fetchChannelUrl, sessionToken, user, org, setRoster });
    } catch (e) {
      console.warn(
        `Failed to initialize ${
          spec.audit_log ? "audit log " : ""
        }channel for ${spec.objectType}:${
          spec.id
        }. Real-time updates will not propagate`,
      );
      console.error(e);
      return;
    }
  })();
}

type ParquetViewsState<T extends TypeMap> = {
  refreshed: Record<string, number>;
  schema: Record<string, Schema<T>>;
  fnames: Record<string, string>;
  loadedVersions: Record<string, number>;
  hasRealtimeWrite: Record<string, boolean>;
};

export function useParquetViews<T extends TypeMap>({
  files,
  ready,
  channelSpecs,
  disableCache: disableCacheProp,
  disableRealtime: disableRealtimeProp,
}: {
  files: Record<string, VersionedData>;
  ready: Record<string, number>;
  channelSpecs: Record<string, ChannelSpec>;
  disableCache?: boolean;
  disableRealtime?: boolean;
}) {
  const duck = useDuckDB();
  const [state, setState] = useState<ParquetViewsState<T>>({
    refreshed: {},
    schema: {},
    fnames: {},
    loadedVersions: {},
    hasRealtimeWrite: {},
  });

  const {
    flags: { disableRealtime: disableRealtimeFlag, disableObjectCache },
  } = useFeatureFlags();

  const disableCache = disableObjectCache || disableCacheProp;

  // This does not need to be updated atomically with the rest of the state, and
  // it might be updated more frequently.
  const [rosters, setRosters] = useState<Record<string, Roster>>({});

  // Mapping from filename to ChannelSpec hash (one channel per unique
  // ChannelSpec).
  const inputFileToChannelsKey = useMemo(
    () => mapObject(channelSpecs, (_k, v) => hashChannelSpec(v)),
    [channelSpecs],
  );

  // Mapping from channel spec hash to all matching filenames. We store this as a
  // reference so that the channel can use the most up-to-date set of filenames
  // as this evolves over time.
  const channelSpecHashToFnames = useRef<Record<string, string[]>>({});
  useEffect(() => {
    const ret: Record<string, string[]> = {};
    for (const [fname, channelSpecHash] of Object.entries(
      inputFileToChannelsKey,
    )) {
      if (channelSpecHash in ret) {
        ret[channelSpecHash].push(fname);
      } else {
        ret[channelSpecHash] = [fname];
      }
    }
    channelSpecHashToFnames.current = ret;
  }, [inputFileToChannelsKey]);

  const updateRefreshed = useCallback(
    (specHash: string) => {
      const fnames = channelSpecHashToFnames.current[specHash] ?? [];
      setState((state) => ({
        ...state,
        refreshed: {
          ...state.refreshed,
          ...Object.fromEntries(
            fnames.map((f) => [f, (state.refreshed[f] || 0) + 1]),
          ),
        },
        hasRealtimeWrite: {
          ...state.hasRealtimeWrite,
          ...Object.fromEntries(fnames.map((f) => [f, true])),
        },
      }));
    },
    [setState],
  );

  const { subscribe: addSubscription } = useUpdateLogSubscriptions({
    updateRefreshed,
  });
  const subscribe = useCallback(
    (specHash: string, onHasRealtimeWrite: () => void) => {
      addSubscription(specHash);
      if (
        globalChannels[specHash] &&
        globalChannels[specHash].hasRealtimeWrite
      ) {
        onHasRealtimeWrite();
      }
    },
    [addSubscription],
  );

  const makeBufName = (name: string) => `${name}_buffer`;

  const { user } = useUser();
  const org = useOrg();

  // Since channel initialization is run in a fire-and-forget fashion
  // (asynchronously from the render loop), it is possible for re-renders to
  // kick off redundant channel initialization for the same object, which slows
  // things down significantly. Thus we enforce one channel initialization at a
  // time across renders using a mutex.
  const initializeChannelMutex = useRef<Mutex>(new Mutex());
  const apiUrl = org.api_url;
  const { getOrRefreshToken } = useSessionToken();

  const disableRealtime = disableRealtimeFlag || disableRealtimeProp;

  useEffect(() => {
    (async () => {
      if (!apiUrl) {
        return;
      }

      const refreshedUpdates: Record<string, number> = {};
      const fnameUpdates: Record<string, string> = {};
      const schemaUpdates: Record<string, Schema<T>> = {};
      const hasRealtimeWriteUpdates: Record<string, boolean> = {};

      for (const fname of Object.keys(files)) {
        if ((!state.refreshed[fname] || state.refreshed[fname] === 0) && duck) {
          const targetFname = makeBufName(fname);
          const { api_version, objectType } = files[fname];
          // NOTE: Creating a separate connection here because I'm assuming two connections can't
          // be in two concurrent async methods
          const conn = await duck.connect();
          try {
            const fileInfo = await duck.globFiles(targetFname);
            const emptyResult = await conn.query(
              emptyResultScan(
                targetFname,
                applyLegacyDatasetMutation(objectType, api_version),
              ),
            );

            if (fileInfo.length === 0) {
              continue;
            }
            const channelSpecHash = inputFileToChannelsKey[fname];
            let initializingChannel = false;
            if (channelSpecs[fname] && !disableRealtime) {
              if (!globalChannels[channelSpecHash]) {
                initializingChannel = true;
                try {
                  await initializeChannelMutex.current.runExclusive(
                    async () => {
                      const sessionToken = await getOrRefreshToken();
                      await initializeChannel({
                        specHash: channelSpecHash,
                        spec: channelSpecs[fname],
                        emptyResult,
                        conn,
                        triggeringParquetFileName: targetFname,
                        updateRefreshed,
                        initUpdateLogArgs: {
                          apiUrl,
                          sessionToken,
                          user,
                          org,
                        },
                        disableCache: !!disableCache,
                        setRoster: (roster) =>
                          setRosters(
                            (rosters) =>
                              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                              ({
                                ...rosters,
                                [channelSpecHash]: roster,
                              }) as Record<string, Roster>,
                          ),
                      });
                    },
                  );
                } catch (e) {
                  console.warn(
                    `Failed to initialize channel for ${objectType}:${channelSpecs[fname].id}. Real-time updates will not propagate`,
                    e,
                  );
                  throw e;
                }
              }
              subscribe(channelSpecHash, () => {
                hasRealtimeWriteUpdates[fname] = true;
              });
            }

            fnameUpdates[fname] = targetFname;
            schemaUpdates[fname] = emptyResult.schema;

            // If we initialized a channel, then we might be replaying some records from the cached indexeddb,
            // so we should not mark ourselves as refreshed/ready until that happens.
            if (!initializingChannel) {
              refreshedUpdates[fname] = 1;
            }
            // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
          } catch (e: any) {
            // It's ok for the fileInfo request to fail. That simply means the file is not ready!
            console.warn(
              "Intermittent error loading file (you can ignore this)",
              e,
            );
          }
        }
      }
      if (Object.keys(refreshedUpdates).length > 0) {
        setState((state) => ({
          ...state,
          schema: {
            ...state.schema,
            ...schemaUpdates,
          },
          fnames: {
            ...state.fnames,
            ...fnameUpdates,
          },
          refreshed: {
            ...state.refreshed,
            ...refreshedUpdates,
          },
          hasRealtimeWrite: {
            ...state.hasRealtimeWrite,
            ...hasRealtimeWriteUpdates,
          },
        }));
      }
    })();
  }, [
    apiUrl,
    channelSpecs,
    duck,
    files,
    inputFileToChannelsKey,
    org,
    state.refreshed,
    updateRefreshed,
    user,
    disableCache,
    subscribe,
    getOrRefreshToken,
    state.hasRealtimeWrite,
    disableRealtime,
  ]);

  useEffect(() => {
    (async () => {
      if (!duck || !apiUrl) {
        return;
      }

      const conn = await duck.connect();
      const schemaUpdates: Record<string, Schema<T>> = {};
      const refreshedUpdates: Record<string, number> = {};
      const loadedVersionsUpdates: Record<string, number> = {};
      const fnameUpdates: Record<string, string> = {};
      const hasRealtimeWriteUpdates: Record<string, boolean> = {};
      for (const fname of Object.keys(files)) {
        if (state.loadedVersions[fname] === ready[fname] || !files[fname]) {
          continue;
        }

        const { buf, api_version, objectType } = files[fname];
        const targetFname = makeBufName(fname);

        // Check `buf` because it may be undefined if a stale cache entry from before we
        // started versioning responses was loaded.
        if (buf && buf.byteLength > 0) {
          // The buffer may have been consumed during the `await` above, and if so, we don't want to
          // re-run this code (on an empty buffer).
          await duck.registerFileBuffer(targetFname, new Uint8Array(buf));
        }

        try {
          const emptyResult = await conn.query(
            emptyResultScan(
              targetFname,
              applyLegacyDatasetMutation(objectType, api_version),
            ),
          );

          const channelSpecHash = inputFileToChannelsKey[fname];
          let initializingChannel = false;
          if (channelSpecs[fname] && !disableRealtime) {
            if (!globalChannels[channelSpecHash]) {
              initializingChannel = true;
              const sessionToken = await getOrRefreshToken();
              await initializeChannelMutex.current.runExclusive(async () => {
                await initializeChannel({
                  specHash: channelSpecHash,
                  spec: channelSpecs[fname],
                  emptyResult,
                  conn,
                  triggeringParquetFileName: targetFname,
                  updateRefreshed,
                  initUpdateLogArgs: {
                    apiUrl,
                    sessionToken,
                    user,
                    org,
                  },
                  disableCache: !!disableCache,
                  setRoster: (roster) =>
                    setRosters(
                      (rosters) =>
                        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                        ({
                          ...rosters,
                          [channelSpecHash]: roster,
                        }) as Record<string, Roster>,
                    ),
                });
              });
            }

            subscribe(channelSpecHash, () => {
              hasRealtimeWriteUpdates[fname] = true;
            });
          }

          schemaUpdates[fname] = emptyResult.schema;
          fnameUpdates[fname] = targetFname;
          loadedVersionsUpdates[fname] = ready[fname];
          if (!initializingChannel) {
            refreshedUpdates[fname] = (state.refreshed[fname] || 0) + 1;
          }
          // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        } catch (e: any) {
          // If we did not set the buffer up yet, then the file may not be ready.
          console.warn(
            "Intermittent error loading file (you can ignore this)",
            e,
          );
        }
      }

      if (Object.keys(schemaUpdates).length > 0) {
        setState((state) => ({
          ...state,
          schema: { ...state.schema, ...schemaUpdates },
          fnames: { ...state.fnames, ...fnameUpdates },
          loadedVersions: { ...state.loadedVersions, ...loadedVersionsUpdates },
          refreshed: { ...state.refreshed, ...refreshedUpdates },
          hasRealtimeWrite: {
            ...state.hasRealtimeWrite,
            ...hasRealtimeWriteUpdates,
          },
        }));
      }
    })();
  }, [
    apiUrl,
    channelSpecs,
    duck,
    files,
    inputFileToChannelsKey,
    org,
    ready,
    state.loadedVersions,
    state.refreshed,
    updateRefreshed,
    user,
    disableCache,
    subscribe,
    getOrRefreshToken,
    disableRealtime,
  ]);

  const scans = useMemo(() => {
    const scans: Record<string, string> = {};
    for (const fname of Object.keys(files)) {
      const s = state.schema[fname];
      if (!s) {
        continue;
      }
      const { api_version, objectType } = files[fname];
      scans[fname] = buildScanQueryInternal({
        s,
        fnames: [state.fnames[fname]],
        logName:
          !inputFileToChannelsKey[fname] || !state.hasRealtimeWrite[fname]
            ? undefined
            : channelSpecTableName(inputFileToChannelsKey[fname]),
        applyLegacyDatasetMutation: applyLegacyDatasetMutation(
          objectType,
          api_version,
        ),
        disableRealtime,
      });
    }
    return scans;
  }, [
    disableRealtime,
    files,
    inputFileToChannelsKey,
    state.fnames,
    state.hasRealtimeWrite,
    state.schema,
  ]);

  const filenameToChannel = useCallback(
    (
      fname: string,
    ): { channel: UpdateLog | undefined; roster: Roster | undefined } => ({
      channel: globalChannels[inputFileToChannelsKey[fname]],
      roster: rosters[inputFileToChannelsKey[fname]],
    }),
    [inputFileToChannelsKey, rosters],
  );

  // Filter out stale filenames, e.g. from old searches. Technically this isn't necessary
  // because we also apply search filters on the frontend, but it's better not to keep
  // extraneous rows around.
  return useMemo(() => {
    const keyFilter = (k: string) => k in files;
    return {
      refreshed: filterObject(state.refreshed, keyFilter),
      fnames: filterObject(state.fnames, keyFilter),
      schema: filterObject(state.schema, keyFilter),
      scans,
      filenameToChannel,
    };
  }, [files, state, scans, filenameToChannel]);
}

export function buildScanQuery({
  s,
  fnames,
  logName,
  disableRealtime,
  freezeResultSet,
}: {
  s: Schema;
  fnames: string[];
  logName: string | undefined;
  disableRealtime?: boolean;
  freezeResultSet?: boolean;
}) {
  return buildScanQueryInternal({
    s,
    fnames,
    logName,
    disableRealtime,
    freezeResultSet,
  });
}

function buildScanQueryInternal({
  s,
  fnames,
  logName,
  applyLegacyDatasetMutation,
  disableRealtime,
  freezeResultSet,
}: {
  s: Schema;
  fnames: string[];
  logName: string | undefined;
  applyLegacyDatasetMutation?: boolean;
  disableRealtime?: boolean;
  // Similar to disableRealtime, but instead of actually disabling realtime values, this snapshots
  // the set of matching rows based on the parquet scan, and then filters the rows based on the
  // set of matching root spans.
  freezeResultSet?: boolean;
}) {
  const parquetFields = s.fields
    .filter(
      (f) =>
        f.name !== UpdateNonceField &&
        f.name !== ObjectDeleteField &&
        // DEPRECATION_NOTICE: remove once everyone is on version 0.0.44
        // We let these fields bleed out of the btql endpoint accidentally,
        // and although we've fixed it in the API, we don't want to force an
        // API upgrade to resolve it.
        f.name !== "pagination_cursor_xact_id" &&
        f.name !== "pagination_cursor_root_span_id" &&
        // DEPRECATION_NOTICE: remove once everyone is on version 0.0.61
        f.name !== "dataset_record_id",
    )
    .map((f) => defaultFieldValue(f.name, !!applyLegacyDatasetMutation));

  // Some views, like audit_log, don't have the root_span_id
  const matchingRootSpanId = s.fields.find((f) => f.name === "root_span_id")
    ? "root_span_id"
    : "id";

  // TODO(austin): This is a hacky fix for is_root which is not set in realtime.
  const replaceFields: string[][] = [
    [
      "is_root",
      s.fields.find((f) => f.name === "span_parents")
        ? "JSON_ARRAY_LENGTH(span_parents) = 0 OR span_parents IS NULL"
        : "true",
    ],
  ];
  const addFields: string[][] = [];

  const replaceExpr = replaceFields
    .filter(([name, _]) => s.fields.find((f) => f.name === name))
    .map(
      ([name, expr]) =>
        `COALESCE(${doubleQuote(name)}, ${expr}) as ${doubleQuote(name)}`,
    )
    .join(",\n");
  const addExpr = addFields
    .map(([name, expr]) => `${expr} as ${doubleQuote(name)},\n`)
    .join("");

  return `
${
  freezeResultSet
    ? `WITH relevant_root_spans AS MATERIALIZED (
  SELECT DISTINCT ${matchingRootSpanId} FROM parquet_scan([${fnames.map(singleQuote).join(", ")}])
)
`
    : ""
}
SELECT * EXCLUDE ("${UpdateNonceField}")
FROM (
  SELECT ${!isEmpty(logName) ? `DISTINCT ON (${IdField})` : ``}
    * ${replaceExpr ? `REPLACE (${replaceExpr})` : ""},
    ${addExpr}
  FROM (
      (
        SELECT
          ${parquetFields}, NULL as _update_nonce
        FROM parquet_scan([${fnames.map(singleQuote).join(", ")}])
        /*RE_PUSHDOWN_FILTER_SUB*/
      )
      ${
        !isEmpty(logName) && !disableRealtime
          ? `
          UNION ALL BY NAME
          SELECT * FROM ${doubleQuote(logName)}
          /*RE_PUSHDOWN_FILTER_SUB*/
          `
          : ``
      }
  )
  ${
    !isEmpty(logName)
      ? `
  -- This order by together with the DISTINCT ON above implements the equivalent of "row number" and picks
  -- the first row for each id, much more efficiently than a window function.
  ORDER BY id, ${UpdateNonceField} DESC NULLS LAST, ${TransactionIdField} DESC NULLS LAST
  `
      : ``
  }
)
  WHERE
  TRUE
  ${
    !isEmpty(logName) && !disableRealtime
      ? ` AND ("${ObjectDeleteField}" IS NULL OR NOT "${ObjectDeleteField}")`
      : ""
  }
  ${
    freezeResultSet
      ? // Although we technically only need to apply this on the log, there is a bug in DuckDB that incorrectly
        // combines the filters (https://github.com/duckdb/duckdb/issues/15295), so we apply it to the entire query.
        `AND (${matchingRootSpanId} IN (SELECT ${matchingRootSpanId} FROM relevant_root_spans))`
      : ""
  }
`;
}

function msUnixTimestampToISO(ms: unknown) {
  if (typeof ms !== "number") {
    return ms;
  }
  // https://stackoverflow.com/questions/59394911/get-isostring-in-microseconds-from-unix-timestamp
  return new Date(ms)
    .toISOString()
    .replace(/\d+Z$/, String(Math.round(ms * 1000)).slice(-6) + "Z");
}

// Sort of the reverse of `msUnixTimestampToISO` but accepts inputs with
// varying levels of precision. Code is mostly AI-generated.
export function isoToMsUnixTimestamp(isoString: string): number {
  // Parse the ISO string to a Date object (ignores sub-seconds)
  const baseDate = new Date(isoString.split(".")[0] + "Z");

  // If no sub-second part exists, return the base date in milliseconds
  if (!isoString.includes(".")) {
    return baseDate.getTime();
  }

  // Extract the sub-second part (microseconds, milliseconds, or other precision)
  const subSeconds = isoString.split(".")[1].slice(0, -1); // remove "Z" at the end

  let additionalMs = 0;
  if (subSeconds.length === 3) {
    additionalMs = parseInt(subSeconds, 10);
  } else if (subSeconds.length === 6) {
    additionalMs = parseInt(subSeconds, 10) / 1000;
  } else {
    throw new Error(`Unsupported sub-second precision: ${subSeconds}`);
  }

  // Add the calculated milliseconds to the base date
  return baseDate.getTime() + additionalMs;
}

let _updateNonce = 0;

// Returns the confirmed transaction id from the server.
//
// - values is an array of objects to upsert, and must be included, as it is used to make an optimistic
//   update to the local database.
// - pathUpdates is an optional array of objects which is a more targeted update, e.g. if updating a score
//   named `foo`, it would be something like {scores: {foo: 0.5}}. pathUpdates are "merged" on the backend,
//   which (a) reduces the byte size of the request and (b) more importantly, allows the backend to compute
//   more accurate audit data which includes exactly which field was updated. Each pathUpdate must correspond
//   to a value in the values array.
export async function performUpsert(
  log: UpdateLog | null,
  apiUrl: string,
  sessionToken: LoadedBtSessionToken,
  userId: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  values: any[],
  pathUpdates: unknown[] | undefined = undefined,
  onOptimisticUpdate?: () => void,
): Promise<TransactionId | null> {
  if (!isEmpty(pathUpdates) && values.length != pathUpdates.length) {
    throw new Error(
      `Mismatch in number of values (${values.length}) and path updates (${pathUpdates.length})`,
    );
  }

  if (values.length == 0) {
    return null;
  }

  // First, prepare the payload to insert into our local cache. This has the added benefit
  // of normalizing the types so that we do not break the schema in the process.
  //
  // - Include the value
  // - Reset the system fields (except ObjectDelete and Created). NOTE: We do not set the created field
  //   because we use it to sort records, and do not want to move the records around while editing them.
  // - Set a nonce which takes precedence over "real" rows that may be inserted concurrently.
  const nonce = ++_updateNonce;
  const logPayload = values
    .map((value) => {
      const { [TransactionIdField]: _, ...rest } = value;
      return {
        ...rest,
        [UserIdField]: userId,
        [UpdateNonceField]: nonce,
        [CreatedField]: value[CreatedField]
          ? msUnixTimestampToISO(value[CreatedField])
          : new Date().toISOString(),
      };
    })
    .map((value) => {
      // This mirrors the backfill logic in the backend. We do it here so that optimistic updates
      // are formatted correctly.
      const missingSpanId = !value[IS_MERGE_FIELD] && !value[SpanIdField];
      return {
        ...value,
        [SpanIdField]: missingSpanId ? value[IdField] : value[SpanIdField],
        [RootSpanIdField]: missingSpanId
          ? value[IdField]
          : value[RootSpanIdField],
      };
    });

  let localInsertPromise = Promise.resolve();
  if (log) {
    localInsertPromise = log.insertCachedRows(
      logPayload.map((payload) => {
        if (!payload.id) {
          throw new Error(
            `Missing id in payload: ${JSON.stringify(
              payload,
            )}. Cannot update log`,
          );
        }
        return {
          [TransactionIdField]: "0",
          [ObjectDeleteField]: false,
          ...payload,
        };
      }),
      "optimistic_update",
    );
  }
  onOptimisticUpdate?.();

  // Be extra careful and remove any fields we might have added
  for (const payload of logPayload) {
    delete payload[UpdateNonceField];
    delete payload["row_number"];
  }

  const responses = await apiPostObject({
    apiUrl,
    sessionToken,
    data: (pathUpdates ?? logPayload).map((r) => ({
      [IS_MERGE_FIELD]: true,
      // Allow the records to clarify that they are not merges
      ...r,
      [AuditSourceField]: "app",
      [AuditMetadataField]: {
        user_id: userId,
      },
    })),
  });

  const results = await Promise.all(responses.map((r) => r.json()));
  for (let i = 0; i < results.length; i++) {
    // Ideally we can also fail all the writes in this case.
    if (!responses[i].ok) {
      if (log) {
        await log.deleteNonce(logPayload, nonce);
      }
      const result = results[i];
      throw new Error(`${result.Code}: ${result.Message}`);
    }
  }

  const candidateXactIds = results.map((result) => {
    let newXactId: string | null = null;
    if ("xact_id" in result) {
      newXactId = result.xact_id;
    } else {
      // We should have just one unique transaction ID for the whole batch.
      //
      // TODO(manu): delete once everybody has upgraded their lambda functions.
      for (const xactId of result.all_xact_ids ?? []) {
        if (newXactId === null) {
          newXactId = xactId;
        } else if (newXactId != xactId) {
          throw new Error(
            `Got multiple transaction IDs from one upsert batch: ${xactId} and ${newXactId}`,
          );
        }
      }
    }
    if (newXactId === null) {
      throw new Error(`Missing transaction ID from logs2 operation`);
    }
    return newXactId;
  });

  const newXactId = candidateXactIds.reduce((a, b) => strMax(a, b), "");

  if (newXactId === "") {
    if (log) {
      await localInsertPromise;
      await log.deleteNonce(logPayload, nonce);
    }
    throw new Error(
      `Missing transaction ID from logs operation. Write did not complete.`,
    );
  }

  // Technically, we could synchronize some kind of delete/update dance, but
  // I think this is fine, because we'll just have a duplicate row in the log table
  if (log) {
    await localInsertPromise;
    await log.swapNonce(logPayload, nonce, newXactId);
  }

  return newXactId;
}

export enum InsertRecordsResult {
  SUCCESS = 1,
  FAILURE_EXCEEDS_MAX_BYTES = 2,
}

export async function insertRecords(
  conn: duckdb.AsyncDuckDBConnection,
  schema: Schema,
  hints: DuckDBJSONType,
  tableName: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  payloads: any[],
  insertionCategory: DuckDBInsertionCategory,
): Promise<InsertRecordsResult> {
  const df = notEmpty(
    createArrowTableFromRecords(
      payloads.map((p) =>
        Object.fromEntries(
          schema.fields.map((f) => {
            const key = f.name;
            const value = p[key];

            return [
              f.name,
              !isEmpty(value) && getDuckDBTypeChild(hints, f.name) === JSONType
                ? JSON.stringify(value, normalizeArrowForJSON)
                : (value ?? null),
            ];
          }),
        ),
      ),
      schema,
    ),
  );
  const valuesSize = df.data.reduce((acc, d) => acc + d.byteLength, 0);
  try {
    const dbBytesInserted = mapSetDefault(
      DuckDBInstanceToBytesInserted,
      conn.bindings,
      {
        index_db_initial_records: 0,
        realtime: 0,
        optimistic_update: 0,
        model_costs: 0,
      },
    );
    if (
      dbBytesInserted[insertionCategory] + valuesSize >
      DuckDBInsertionLimits[insertionCategory]
    ) {
      return InsertRecordsResult.FAILURE_EXCEEDS_MAX_BYTES;
    }
    dbBytesInserted[insertionCategory] += valuesSize;
  } catch (e) {
    console.warn(`Failed to update bytes inserted for ${insertionCategory}`, e);
  }

  await conn.insertArrowTable(df, {
    name: tableName,
    create: false,
  });
  return InsertRecordsResult.SUCCESS;
}

// We attempt to flush the update log in batches, varying the batching delay by
// how many items were in the last batch-to-flush.
const UpdateLogMinFlushTimeout = 0; // ms
const UpdateLogMaxFlushTimeout = 10000; // ms
// Saturate the flush delay if we have more than this number of records in the
// queue.
const UpdateLogMaxFlushNumRecords = 100;

type UpdateLogInsertionCategoryQueue = {
  queue: unknown[];
  flushPromise: Promise<void> | null;
  lastQueueSize: number;
};

export class UpdateLog {
  public objectType: DataObjectType;
  public tableName: string;
  public emptyTable: Table;
  public insertRecordsError: InsertRecordsResult = InsertRecordsResult.SUCCESS;
  public hasRejoined: boolean;
  private _hasRealtimeWrite: boolean = false;
  channel: RealtimeChannel | null = null;
  refresh: () => void;
  conn: duckdb.AsyncDuckDBConnection;
  deferredOpenRow: OpenedRow | null = null;

  private insertionCategoryQueues: Record<
    DuckDBInsertionCategory,
    UpdateLogInsertionCategoryQueue
  > = makeDuckDBInsertionCategoryMap(() => ({
    queue: [],
    flushPromise: null,
    lastQueueSize: 0,
  }));

  constructor(
    conn: duckdb.AsyncDuckDBConnection,
    objectType: DataObjectType,
    tableName: string,
    emptyTable: Table,
    refresh: () => void,
    private disableCache: boolean,
  ) {
    this.conn = conn;
    this.objectType = objectType;
    this.tableName = tableName;
    this.emptyTable = emptyTable;
    this.refresh = refresh;
    this.hasRejoined = false;
  }

  async insertCachedRows(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    payloads: any[],
    insertionCategory: DuckDBInsertionCategory,
  ): Promise<void> {
    // IndexDB operations can be done in the background.
    (async () => {
      if (this.disableCache) {
        return;
      }
      if (payloads.length > 0) {
        this._hasRealtimeWrite = true;
      }
      const btCachedDB = await loadBtCacheDB();
      for (const payload of payloads) {
        const objectStore =
          btCachedDB &&
          btCachedDB
            .transaction([`${this.objectType}_log`], "readwrite")
            .objectStore(`${this.objectType}_log`);

        if (objectStore) {
          objectStore.put(payload, [
            this.tableName,
            payload[TransactionIdField] || 0,
            payload.id,
          ]);
        }
      }
    })();
    this.insertionCategoryQueues[insertionCategory].queue.push(...payloads);
    return this.flushQueue(insertionCategory);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  async deleteNonce(payloads: any[], nonceId: number) {
    const stmt = await this.conn.prepare(
      `DELETE FROM "${this.tableName}" WHERE "${UpdateNonceField}" = ?`,
    );
    try {
      await stmt.query(nonceId);
    } finally {
      await stmt.close();
      this.refresh();
    }

    if (this.disableCache) {
      return;
    }

    // We should delete the nonced rows from the indexeddb as well
    const btCachedDB = await loadBtCacheDB();
    const objectStore =
      btCachedDB &&
      btCachedDB
        .transaction([`${this.objectType}_log`], "readwrite")
        .objectStore(`${this.objectType}_log`);

    if (objectStore) {
      payloads.map((payload) => {
        objectStore.delete([this.tableName, 0 /* xact id */, payload.id]);
      });
    }
  }

  public get hasRealtimeWrite() {
    return this._hasRealtimeWrite;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  async swapNonce(payloads: any[], nonceId: number, xactId: string) {
    const stmt = await this.conn.prepare(
      `UPDATE ${doubleQuote(
        this.tableName,
      )} SET "${TransactionIdField}" = ?, "${UpdateNonceField}" = NULL WHERE "${UpdateNonceField}" = ?`,
    );
    try {
      await stmt.query(xactId, nonceId);
    } finally {
      await stmt.close();
      this.refresh();
    }

    if (this.disableCache) {
      return;
    }

    // We insert nonced values in indexeddb while saving cached rows, so that we don't lose changes on refresh
    // (if the network/broadcast calls haven't completed). When we get a confirmed transaction id from the backend
    // we therefore need to remove these nonced rows (which have _xact_id 0) and replace them with the confirmed
    // transaction id.
    const btCachedDB = await loadBtCacheDB();
    const objectStore =
      btCachedDB &&
      btCachedDB
        .transaction([`${this.objectType}_log`], "readwrite")
        .objectStore(`${this.objectType}_log`);

    if (objectStore) {
      payloads.map((payload) => {
        objectStore.put(
          {
            ...payload,
            [TransactionIdField]: xactId,
          },
          [this.tableName, xactId, payload.id],
        );
        objectStore.delete([this.tableName, 0 /* xact id */, payload.id]);
      });
    }
  }

  async init({
    fetchChannelUrl,
    sessionToken,
    user,
    org,
    setRoster,
  }: {
    fetchChannelUrl: string;
    sessionToken: LoadedBtSessionToken;
    user: UserContextT["user"];
    org: OrgContextT;
    setRoster: SetRosterFn;
  }) {
    const channelName = await (
      await apiFetchGet(fetchChannelUrl, sessionToken)
    ).json();

    const deferredOpenRow = this.deferredOpenRow;
    this.deferredOpenRow = null;

    const channel = await new Promise<RealtimeChannel>((resolve, reject) => {
      const channel = new RealtimeChannel(
        channelName.channel,
        channelName.token,
        user
          ? {
              user_id: user.id,
              email: user.email,
              avatar_url: user.avatar_url,
              ...(deferredOpenRow ? { row: deferredOpenRow } : {}),
            }
          : null,
        org,
        () => {
          resolve(channel);
          return true;
        },
        (payload) => {
          this.insertCachedRows([payload], "realtime");
        },
        setRoster,
        () => {
          this.hasRejoined = true;
        },
      );
    });

    this.channel = channel;
  }

  public openRow(row: OpenedRow) {
    if (this.channel) {
      this.channel.openRow(row);
    } else {
      this.deferredOpenRow = row;
    }
  }

  private flushQueue(
    insertionCategory: DuckDBInsertionCategory,
  ): Promise<void> {
    const queueData = this.insertionCategoryQueues[insertionCategory];

    // If there is already an active flushPromise, do nothing. Otherwise, spawn
    // a new one.
    if (queueData.flushPromise === null) {
      queueData.flushPromise = (async () => {
        const queueFlushInterpR = Math.min(
          Math.max(queueData.lastQueueSize / UpdateLogMaxFlushNumRecords, 0),
          1,
        );
        const queueFlushDelay =
          (1 - queueFlushInterpR) * UpdateLogMinFlushTimeout +
          queueFlushInterpR * UpdateLogMaxFlushTimeout;
        await new Promise((resolve) => setTimeout(resolve, queueFlushDelay));
        const payloads = queueData.queue;
        queueData.queue = [];
        queueData.flushPromise = null;
        queueData.lastQueueSize = payloads.length;
        if (payloads.length === 0) {
          return;
        }
        await this.insertRecordsSync(payloads, insertionCategory);
      })();
    }
    return queueData.flushPromise;
  }

  private async insertRecordsSync(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    payloads: any[],
    insertionCategory: DuckDBInsertionCategory,
  ) {
    const res = await insertRecords(
      this.conn,
      this.emptyTable.schema,
      DuckDBTypeHints[this.objectType],
      this.tableName,
      payloads,
      insertionCategory,
    );
    if (res !== InsertRecordsResult.SUCCESS) {
      this.insertRecordsError = res;
    }
    this.refresh();
  }
}

export function signalsToReadyCounter(signals: number[]) {
  return {
    readyAll: signals.reduce((a, b) => a && b > 0, true),
    readyIdx: signals.reduce((a, b) => a + b, 0),
  };
}

type DBQueryState<T extends TypeMap, Multi extends boolean = false> = {
  data: (Multi extends true ? Table<T>[] : Table<T>) | null;
  errorInternal: string | null;
  loading: boolean;
  hasLoaded: boolean;
  loadedTag: string | undefined;
  executions: number;
};

const emptyDBQueryState = {
  data: null,
  errorInternal: null,
  loading: false,
  // tracks first load so we don't flicker on subsequent loads
  hasLoaded: false,
  loadedTag: undefined,
  executions: 0,
};

type DbQueryOptions = {
  prelude?: string;
  setError?: (error: string | null) => void;
  timed?: boolean;
  // Sometimes callers need to know that a specific *version* of the query has
  // loaded. For this, they can specify a `loadingTag`, which will be
  // reflected in the output variable `loadedTag` when that query has
  // completed. It is guaranteed that the `loadedTag` and `data` in the
  // returned object will be updated atomically.
  loadingTag?: string;
  skipTicketing?: boolean;
};

// These overloads allow us to enforce that string inputs return a single output,
// and an array returns an array of outputs.
export function useDBQuery<T extends TypeMap>(
  query:
    | string
    | ((
        conn: duckdb.AsyncDuckDBConnection,
        abortSignal: AbortSignal,
        options?: DbQueryOptions,
      ) => Promise<Table<T> | null>)
    | null,
  ready: number[],
  options?: DbQueryOptions,
): DBQueryState<T, false>;

export function useDBQuery<T extends TypeMap>(
  query: string[] | null,
  ready: number[],
  options?: DbQueryOptions,
): DBQueryState<T, true>;

export function useDBQuery<T extends TypeMap, Multi extends boolean = false>(
  queryArg:
    | (Multi extends true
        ? string[]
        :
            | string
            | ((
                conn: duckdb.AsyncDuckDBConnection,
                abortSignal: AbortSignal,
                options?: DbQueryOptions,
              ) => Promise<Table<T> | null>))
    | null,
  ready: number[],
  options?: DbQueryOptions,
) {
  const { prelude, setError, timed, loadingTag, skipTicketing } = options ?? {};

  const query = useQueryMemo(queryArg);

  const [state, setState] = useState<DBQueryState<T, Multi>>({
    ...emptyDBQueryState,
    loading: !!query,
  });

  const duck = useDuckDB();

  // The array itself changes every time we re-run this expression, so instead
  // we need to validate that (a) each dependency is ready and (b) keep a counter
  // so that if any of the dependencies change, we re-run the query.
  const { readyAll, readyIdx } = signalsToReadyCounter(ready);

  const ticket = useRef(0);

  // Reuse the connection across queries.
  const { conn } = useDuckConn();

  useEffect(() => {
    // All of ready is greater than 0
    (async () => {
      // Because this is an async effect, it can run multiple times as the query changes
      // and the results change. This index serves as a ticket and allows us to bail if
      // the query changes before we finish running it.
      //
      // It's important to increment this before nulling out the query, because that can invalidate
      // concurrently running queries.
      const excIdx = skipTicketing ? undefined : ++ticket.current;

      if (!query || !conn) {
        setState(emptyDBQueryState);
        return;
      }

      if (duck && readyAll && readyIdx > 0 && query) {
        setState((state) => ({ ...state, loading: true }));
        try {
          if (prelude) {
            await conn.query(prelude);
          }

          const abort = new AbortController();
          if (excIdx !== undefined && excIdx !== ticket.current) {
            abort.abort("stale query");
          }

          const result = await (Array.isArray(query)
            ? Promise.all(
                query.map((q) =>
                  dbQuery(conn, abort.signal, q, {
                    timed,
                    logError: !setError,
                  }),
                ),
              )
            : typeof query === "function"
              ? query(conn, abort.signal)
              : dbQuery(conn, abort.signal, query, {
                  timed,
                  logError: !setError,
                }));

          if (excIdx !== undefined && excIdx !== ticket.current) {
            return;
          }

          setState((state) => ({
            ...state,
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            data: result as (Multi extends true ? Table<T>[] : Table<T>) | null,
            errorInternal: null,
            loading: false,
            hasLoaded: true,
            loadedTag: loadingTag,
            executions: state.executions + 1,
          }));
        } catch (e: unknown) {
          setState({ ...emptyDBQueryState, errorInternal: `${e}` });
        }
      }
    })();
  }, [
    duck,
    query,
    readyIdx,
    readyAll,
    prelude,
    timed,
    loadingTag,
    skipTicketing,
    conn,
    setError,
  ]);

  useEffect(() => {
    if (setError) {
      setError(state.errorInternal);
    } else if (state.errorInternal) {
      toast.error("Error", { description: state.errorInternal });
    }
  }, [setError, state.errorInternal]);

  return {
    data: state.data,
    loading: state.loading,
    hasLoaded: state.hasLoaded,
    loadedTag: state.loadedTag,
    executions: state.executions,
  };
}

// This hook allows us to accept an array of queries that may not be memoized
// outside this function.
export function useQueryMemo<T>(value: T): T {
  const ref = useRef<T>(value);

  // Perform a deep comparison. If they are not equal, update the ref.
  // eslint-disable-next-line react-compiler/react-compiler
  if (!isEqual(value, ref.current)) {
    // eslint-disable-next-line react-compiler/react-compiler
    ref.current = value;
  }

  // eslint-disable-next-line react-compiler/react-compiler
  return ref.current;
}

export async function dbQuery<T extends TypeMap>(
  conn: duckdb.AsyncDuckDBConnection,
  abortSignal: AbortSignal,
  query: string | null,
  options?: {
    timed?: boolean;
    logError?: boolean;
  },
): Promise<Table<T> | null> {
  const { timed, logError } = options ?? {};

  if (abortSignal.aborted || !query) {
    return null;
  }

  const start = performance.now();
  let data;
  try {
    data = await conn.query(query);
  } catch (e: unknown) {
    //console.warn({ query });
    if (logError) {
      console.error("Failed to run duckdb query", query);
      console.error(e);
    }
    throw e;
  } finally {
    if (timed) {
      const end = performance.now();
      console.log(query);
      console.log(`Query took ${end - start}ms`);
    }
  }

  return data;
}

// Arrow types:
// /** @nocollapse */ static isNull(x: any): x is Null { return x?.typeId === Type.Null; }
// /** @nocollapse */ static isInt(x: any): x is Int_ { return x?.typeId === Type.Int; }
// /** @nocollapse */ static isFloat(x: any): x is Float { return x?.typeId === Type.Float; }
// /** @nocollapse */ static isBinary(x: any): x is Binary { return x?.typeId === Type.Binary; }
// /** @nocollapse */ static isUtf8(x: any): x is Utf8 { return x?.typeId === Type.Utf8; }
// /** @nocollapse */ static isBool(x: any): x is Bool { return x?.typeId === Type.Bool; }
// /** @nocollapse */ static isDecimal(x: any): x is Decimal { return x?.typeId === Type.Decimal; }
// /** @nocollapse */ static isDate(x: any): x is Date_ { return x?.typeId === Type.Date; }
// /** @nocollapse */ static isTime(x: any): x is Time_ { return x?.typeId === Type.Time; }
// /** @nocollapse */ static isTimestamp(x: any): x is Timestamp_ { return x?.typeId === Type.Timestamp; }
// /** @nocollapse */ static isInterval(x: any): x is Interval_ { return x?.typeId === Type.Interval; }
// /** @nocollapse */ static isList(x: any): x is List { return x?.typeId === Type.List; }
// /** @nocollapse */ static isStruct(x: any): x is Struct { return x?.typeId === Type.Struct; }
// /** @nocollapse */ static isUnion(x: any): x is Union_ { return x?.typeId === Type.Union; }
// /** @nocollapse */ static isFixedSizeBinary(x: any): x is FixedSizeBinary { return x?.typeId === Type.FixedSizeBinary; }
// /** @nocollapse */ static isFixedSizeList(x: any): x is FixedSizeList { return x?.typeId === Type.FixedSizeList; }
// /** @nocollapse */ static isMap(x: any): x is Map_ { return x?.typeId === Type.Map; }
// /** @nocollapse */ static isDictionary(x: any): x is Dictionary { return x?.typeId === Type.Dictionary; }

// DuckDB types:
// BIGINT	INT8, LONG	signed eight-byte integer
// BIT	BITSTRING	string of 1’s and 0’s
// BOOLEAN	BOOL, LOGICAL	logical boolean (true/false)
// BLOB	BYTEA, BINARY, VARBINARY	variable-length binary data
// DATE	 	calendar date (year, month day)
// DOUBLE	FLOAT8, NUMERIC, DECIMAL	double precision floating-point number (8 bytes)
// DECIMAL(prec, scale)	 	fixed-precision number with the given width (precision) and scale
// HUGEINT	 	signed sixteen-byte integer
// INTEGER	INT4, INT, SIGNED	signed four-byte integer
// INTERVAL	 	date / time delta
// REAL	FLOAT4, FLOAT	single precision floating-point number (4 bytes)
// SMALLINT	INT2, SHORT	signed two-byte integer
// TIME	 	time of day (no time zone)
// TIMESTAMP	DATETIME	combination of time and date
// TIMESTAMP WITH TIME ZONE	TIMESTAMPTZ	combination of time and date that uses the current time zone
// TINYINT	INT1	signed one-byte integer
// UBIGINT	 	unsigned eight-byte integer
// UINTEGER	 	unsigned four-byte integer
// USMALLINT	 	unsigned two-byte integer
// UTINYINT	 	unsigned one-byte integer
// UUID	 	UUID data type
// VARCHAR	CHAR, BPCHAR, TEXT, STRING	variable-length character string

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function arrowTypeToDuckDBSpec(type: DataType): any {
  if (
    DataType.isStruct(type) ||
    DataType.isList(type) ||
    DataType.isFixedSizeList(type)
  ) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    const spec = type.children.reduce((acc: Record<string, any>, f) => {
      acc[f.name] = arrowTypeToDuckDBSpec(f.type);
      return acc;
    }, {});
    if (DataType.isList(type) || DataType.isFixedSizeList(type)) {
      const intrinsicField = Object.keys(spec)[0];
      console.assert(
        intrinsicField === "l",
        `Expected list field ${intrinsicField} to be 'l'`,
      );
      return [spec[intrinsicField]];
    } else {
      return spec;
    }
  } else if (DataType.isNull(type)) {
    return "NULL";
  } else if (DataType.isInt(type)) {
    return "BIGINT";
  } else if (DataType.isFloat(type)) {
    return "DOUBLE";
  } else if (DataType.isBinary(type)) {
    return "BLOB";
  } else if (DataType.isUtf8(type)) {
    return "VARCHAR";
  } else if (DataType.isBool(type)) {
    return "BOOLEAN";
  } else if (DataType.isDecimal(type)) {
    return "DECIMAL";
  } else if (DataType.isTime(type)) {
    return "TIME";
  } else if (DataType.isTimestamp(type)) {
    return "TIMESTAMP WITH TIME ZONE";
  } else if (DataType.isInterval(type)) {
    return "INTERVAL";
  } else if (DataType.isFixedSizeBinary(type)) {
    return "BLOB";
  } else if (DataType.isMap(type)) {
    return "JSON";
  } else if (DataType.isDictionary(type)) {
    return "JSON";
  } else if (
    DataType.isUnion(type) ||
    DataType.isDenseUnion(type) ||
    DataType.isSparseUnion(type)
  ) {
    throw new Error("Unsupported type: Union");
  }
}

export function makeChannelUrl({
  apiUrl,
  spec,
}: {
  apiUrl: string;
  spec: ChannelSpec;
}) {
  return (
    `${apiUrl}/broadcast-key?` +
    new URLSearchParams({
      object_type: spec.objectType,
      id: spec.id,
      audit_log: spec.audit_log ? "1" : "0",
    })
  );
}
