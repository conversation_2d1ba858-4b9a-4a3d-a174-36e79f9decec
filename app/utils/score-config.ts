import { type PositionDescriptions } from "#/app/app/[org]/p/[project]/configuration/configuration-actions";
import {
  projectAutomationSchema,
  projectTagSchema,
  spanIframeSchema,
} from "@braintrust/core/typespecs";
import {
  discriminatedProjectScoreSchema,
  type DiscriminatedProjectScore,
} from "@braintrust/local/query";
import { metricDefinitionSchema } from "@braintrust/local/api-schema";
import { LexoRank } from "lexorank";
import { z } from "zod";
export type { DiscriminatedProjectScore } from "@braintrust/local/query";

export const projectConfigSchema = z.object({
  automations: projectAutomationSchema.array(),
  scores: discriminatedProjectScoreSchema.array(),
  tags: projectTagSchema.array(),
  span_iframes: spanIframeSchema.array(),
  metricDefinitions: metricDefinitionSchema.array(),
});

export type ProjectConfig = z.infer<typeof projectConfigSchema>;

export function getPositionDescriptionsForAllScores(
  scores: DiscriminatedProjectScore[],
): PositionDescriptions {
  const ranks: Record<string, string> = {};

  let currentRank = LexoRank.middle();
  scores.forEach((score) => {
    ranks[score.id] = currentRank.toString();
    currentRank = currentRank.genNext();
  });

  return ranks;
}

export function getPositionDescription({
  prevScore,
  score,
  nextScore,
}: {
  prevScore?: DiscriminatedProjectScore;
  score: DiscriminatedProjectScore;
  nextScore?: DiscriminatedProjectScore;
}): PositionDescriptions | null {
  if (
    !score.position ||
    (prevScore && !prevScore.position) ||
    (nextScore && !nextScore.position)
  ) {
    return null;
  }

  // We need position values for any defined scores to calculate the new rank
  const newLexoRank = getNewLexoRankPosition({
    current: score.position,
    prev: prevScore?.position,
    next: nextScore?.position,
  });

  if (!newLexoRank) {
    return null;
  }

  return { [score.id]: newLexoRank.toString() };
}

export function getNewLexoRankPosition({
  current,
  prev,
  next,
}: {
  current: string;
  prev?: string | null;
  next?: string | null;
}): string | null {
  let newLexoRank: LexoRank;
  if (!prev && next) {
    newLexoRank = LexoRank.parse(next).genPrev();
  } else if (!next && prev) {
    newLexoRank = LexoRank.parse(prev).genNext();
  } else if (prev && next) {
    newLexoRank = LexoRank.parse(next).between(LexoRank.parse(prev));
  } else {
    newLexoRank = LexoRank.parse(current).genNext();
  }

  return newLexoRank.toString();
}
