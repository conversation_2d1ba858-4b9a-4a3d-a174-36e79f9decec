import "server-only";

export async function sendClayWebhookMessage({
  name,
  email,
  source,
  orgId,
  comment,
}: {
  name: string;
  email: string;
  source: string;
  orgId?: string;
  comment?: string;
}) {
  if (process.env.CLAY_CONTACT_US_WEBHOOK_URL) {
    const claybody = JSON.stringify({
      name: name,
      email: email,
      orgId: orgId ?? "",
      comment: comment ?? "",
      source,
    });

    try {
      await fetch(process.env.CLAY_CONTACT_US_WEBHOOK_URL, {
        method: "POST",
        body: claybody,
        headers: {
          "Content-Type": "application/json",
        },
      });
    } catch (error) {
      console.error("Error sending contact us info to Clay: ", error);
    }
  }
}
