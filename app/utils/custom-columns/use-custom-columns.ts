import { z } from "zod";
import {
  type CustomColumnVariant,
  customColumnSchema as dbCustomColumnSchema,
} from "@braintrust/core/typespecs";
import { useOrg } from "#/utils/user";
import {
  type BtSessionToken,
  useSessionToken,
} from "#/utils/auth/session-token";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  apiDelete,
  apiFetchGetCors,
  apiPatch,
  apiPostCors,
} from "#/utils/btapi/fetch";
import { useCallback, useMemo } from "react";
import { type CustomColumnDef } from "#/ui/table/custom-column-form";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  inferSchema,
  parseLogicalSchema,
  type JSONSchemaObject,
} from "@braintrust/btql/schema";
import { Parser } from "@braintrust/btql/parser";
import { bindBTQLExpr, type BTQLTableDefinition } from "#/utils/search-btql";
import { dbQuery, useDuckDB } from "#/utils/duckdb";
import { doubleQuote } from "@braintrust/local/query";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import { type DataObjectType } from "#/utils/btapi/btapi";
import {
  type DuckDBTableName,
  type CustomColumnScope,
} from "@braintrust/local/api-schema";
import { BT_ASSIGNMENTS, BT_ASSIGNMENTS_META_FIELD } from "#/utils/assign";

const assignmentsColumn = {
  id: `__built_in_${BT_ASSIGNMENTS}`,
  name: BT_ASSIGNMENTS,
  expr: `metadata.${doubleQuote(BT_ASSIGNMENTS_META_FIELD)}`,
  builtIn: true,
};

export const BUILT_IN_CUSTOM_COLUMNS: Partial<
  Record<DataObjectType, CustomColumnDefinition[]>
> = {
  project_logs: [assignmentsColumn],
  dataset: [assignmentsColumn],
  experiment: [assignmentsColumn],
};

const customColumnSchema = dbCustomColumnSchema
  .pick({
    id: true,
    name: true,
    expr: true,
  })
  .merge(
    z.object({
      builtIn: z.boolean().nullish(),
    }),
  );
export type CustomColumnDefinition = z.infer<typeof customColumnSchema>;

export type CustomColumn = CustomColumnDefinition & {
  sql: string;
};

const emptyArray: CustomColumn[] = [];

const v1Variants: CustomColumnVariant[] = [
  "experiment",
  "dataset",
  "project",
  "project_log",
];

const getTableFromVariant = (
  variant: CustomColumnVariant,
): DuckDBTableName | undefined => {
  switch (variant) {
    case "experiment":
    case "dataset":
      return variant;
    case "project_log":
      return "project_logs";
    case "experiment_list":
    default:
      return undefined;
  }
};

async function fetchCustomColumns({
  apiUrl,
  sessionToken,
  scope,
  areCustomColumnsV2Enabled,
}: {
  apiUrl: string;
  sessionToken: BtSessionToken;
  scope?: CustomColumnScope;
  areCustomColumnsV2Enabled: boolean;
}): Promise<CustomColumnDefinition[]> {
  if (
    sessionToken === "loading" ||
    sessionToken === "unauthenticated" ||
    !scope
  ) {
    return [];
  }

  const { object_id, object_type, subtype, variant } = scope;

  const params = new URLSearchParams();
  params.append("object_type", object_type);
  params.append("object_id", object_id);
  if (areCustomColumnsV2Enabled) {
    params.append("variant", variant);
    if (!subtype) {
      params.append("subtype", "");
    }
  }
  if (subtype) {
    params.append("subtype", subtype);
  }

  const resp = await apiFetchGetCors(
    `${apiUrl}/v1/column?${params.toString()}`,
    sessionToken,
  );
  if (!resp.ok) {
    throw new Error(await resp.text());
  }

  const { objects } = await resp.json();
  return z.array(customColumnSchema).parse(objects);
}

export function useCustomColumns({
  scope,
  projectedColumns,
}: {
  scope?: CustomColumnScope;
  projectedColumns?: CustomColumnDefinition[];
}) {
  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const queryClient = useQueryClient();
  const {
    flags: {
      customColumns: areCustomColumnsEnabled,
      customColumnsV2: areCustomColumnsV2Enabled,
    },
  } = useFeatureFlags();

  const customColumnsEnabled = scope
    ? v1Variants.includes(scope.variant)
      ? areCustomColumnsEnabled
      : areCustomColumnsV2Enabled
    : false;

  const {
    data: customColumns,
    refetch,
    isError,
    isLoading,
  } = useQuery(
    {
      queryKey: ["useCustomColumns", scope],
      queryFn: async () => {
        const sessionToken = await getOrRefreshToken();
        return fetchCustomColumns({
          apiUrl,
          sessionToken,
          scope,
          areCustomColumnsV2Enabled,
        });
      },
      enabled: customColumnsEnabled && !!apiUrl && !!scope,
      meta: {
        // Temporary disabling toast:
        // Public pages throw an error toast when try to load custom columns.
        // Disable this for now while we figure out why custom columns aren't loading
        disableGlobalErrorToast: true,
      },
    },
    queryClient,
  );

  const createCustomColumn = useCallback(
    async ({ name, expr }: CustomColumnDef) => {
      if (!scope) {
        return;
      }
      const { object_id, object_type, subtype, variant } = scope;

      const sessionToken = await getOrRefreshToken();

      const resp = await apiPostCors({
        url: `${apiUrl}/v1/column`,
        sessionToken,
        payload: {
          object_id,
          object_type,
          subtype: areCustomColumnsV2Enabled ? subtype : subtype || null,
          name,
          expr,
          ...(areCustomColumnsV2Enabled ? { variant } : {}),
        },
        alreadySerialized: false,
      });
      if (!resp.ok) {
        throw new Error(await resp.text());
      }

      refetch();
    },
    [apiUrl, scope, refetch, getOrRefreshToken, areCustomColumnsV2Enabled],
  );

  const deleteCustomColumn = useCallback(
    async (customColumnId: string) => {
      const sessionToken = await getOrRefreshToken();
      const resp = await apiDelete({
        url: `${apiUrl}/v1/column/${customColumnId}`,
        sessionToken,
        payload: {},
        alreadySerialized: false,
      });
      if (!resp.ok) {
        throw new Error(await resp.text());
      }
      refetch();
    },
    [apiUrl, refetch, getOrRefreshToken],
  );

  const updateCustomColumn = useCallback(
    async ({
      columnId,
      columnData,
    }: {
      columnId: string;
      columnData: CustomColumnDef;
    }) => {
      const sessionToken = await getOrRefreshToken();
      const resp = await apiPatch({
        url: `${apiUrl}/v1/column/${columnId}`,
        sessionToken,
        payload: {
          name: columnData.name,
          expr: columnData.expr,
        },
        alreadySerialized: false,
      });
      if (!resp.ok) {
        throw new Error(await resp.text());
      }
      refetch();
    },
    [apiUrl, refetch, getOrRefreshToken],
  );

  const customColumnDefinitions = useMemo(() => {
    if (!customColumns) {
      return undefined;
    }

    return customColumns.concat(projectedColumns ?? []);
  }, [customColumns, projectedColumns]);

  return useMemo(
    () => ({
      customColumnDefinitions,
      customColumnsEnabled,
      createCustomColumn,
      deleteCustomColumn,
      updateCustomColumn,
      isError,
      isLoading,
    }),
    [
      customColumnDefinitions,
      customColumnsEnabled,
      createCustomColumn,
      deleteCustomColumn,
      updateCustomColumn,
      isError,
      isLoading,
    ],
  );
}

function getSqlString({
  column,
  table,
  btqlFields,
  tableAlias,
}: {
  column: CustomColumnDefinition;
  table: DuckDBTableName | BTQLTableDefinition;
  btqlFields?: {
    field: string;
    btql: string;
    typeOverride?: JSONSchemaObject;
  }[];
  tableAlias: string;
}) {
  const parser = new Parser(column.name);
  const expr = parser.parseExpr();
  const c = bindBTQLExpr({
    table,
    expr,
    topLevelFields: [],
    btqlFields,
    tableAlias,
  });
  return c.sql.toPlainStringQuery();
}

type ParseCustomColumnsSchemaParams = {
  conn: AsyncDuckDBConnection;
  abort: AbortSignal;
  customColumns?: CustomColumnDefinition[];
  variant: CustomColumnVariant;
  rowScan: string | null;
  tableAlias?: string;
  tableDef?: BTQLTableDefinition | null;
};

export async function parseCustomColumnsSchema({
  conn,
  abort,
  customColumns,
  variant,
  rowScan,
  tableAlias = "t",
  tableDef,
}: ParseCustomColumnsSchemaParams) {
  const inferenceDepth =
    customColumns &&
    Math.max(...customColumns.map((c) => c.expr.split(".").length));

  const invalidColumns = new Set<string>();

  const schemaQueries: { query: string; columnName: string }[] | undefined =
    (() => {
      const table = tableDef ?? getTableFromVariant(variant);
      if (!customColumns || !table) {
        return undefined;
      }
      if (customColumns.length === 0) {
        return [];
      }
      const btqlFields = customColumns.map(({ name, expr }) => ({
        field: name,
        btql: expr,
      }));
      return customColumns.reduce<{ query: string; columnName: string }[]>(
        (acc, column) => {
          try {
            const sql = getSqlString({ column, table, btqlFields, tableAlias });
            if (sql) {
              acc.push({
                query: `${sql} AS ${doubleQuote(column.name)}`,
                columnName: column.name,
              });
            }
          } catch (err) {
            invalidColumns.add(column.name);
            console.error(err);
          }
          return acc;
        },
        [],
      );
    })();

  const queries =
    rowScan && schemaQueries && schemaQueries.length
      ? schemaQueries.map(
          ({ query }) => `
    SELECT
      ${query}
    FROM (${rowScan}) ${tableAlias}`,
        )
      : null;

  const result =
    queries &&
    (await Promise.allSettled(
      queries.map((query) => dbQuery(conn, abort, query)),
    ));
  const customColumnsSchema = (() => {
    if (!inferenceDepth) {
      return undefined;
    }
    let schema: JSONSchemaObject = {};
    result?.forEach((res, i) => {
      if (res.status === "fulfilled") {
        res?.value?.toArray().forEach((r) => {
          const value = r.toJSON();
          for (const [k, v] of Object.entries(value)) {
            if (typeof v === "string") {
              try {
                value[k] = JSON.parse(v);
              } catch {}
            }
          }
          schema = inferSchema({ schema, value, depth: inferenceDepth });
        });
      } else {
        if (schemaQueries?.[i]?.columnName) {
          invalidColumns.add(schemaQueries?.[i]?.columnName);
        }
        console.error(res.reason);
      }
    });
    return schema;
  })();

  const withClauses = (() => {
    const table = tableDef ?? getTableFromVariant(variant);
    if (!customColumns || !table || !customColumnsSchema) {
      return undefined;
    }
    if (customColumns.length === 0) {
      return emptyArray;
    }
    const btqlFields = customColumns.map(({ name, expr }) => {
      try {
        const fieldSchema = parseLogicalSchema(
          customColumnsSchema.properties?.[name],
        );
        return {
          field: name,
          btql: expr,
          typeOverride: fieldSchema,
        };
      } catch {
        return {
          field: name,
          btql: expr,
        };
      }
    });
    return customColumns.reduce<CustomColumn[]>((acc, column) => {
      if (invalidColumns.has(column.name)) {
        acc.push({
          ...column,
          sql: `'<Invalid column>' AS ${doubleQuote(column.name)}`,
        });
      } else {
        try {
          const sql = getSqlString({ column, table, btqlFields, tableAlias });
          if (sql) {
            acc.push({
              ...column,
              sql: `${sql} AS ${doubleQuote(column.name)}`,
            });
          }
        } catch (err) {}
      }

      return acc;
    }, []);
  })();

  return {
    customColumns: withClauses,
    customColumnsSchema: customColumnsSchema,
  };
}

export function getCustomColumnScope(
  args:
    | {
        objectType: "dataset";
        objectId: string | null;
      }
    | {
        objectType?: Exclude<DataObjectType, "dataset">;
        projectId: string | null;
      },
): CustomColumnScope | undefined {
  if (args.objectType === "dataset") {
    if (!args.objectId) {
      return;
    }
    return {
      object_type: args.objectType,
      object_id: args.objectId,
      subtype: "",
      variant: args.objectType,
    };
  }

  if (!args.projectId) {
    return;
  }
  switch (args.objectType) {
    case "experiment":
      return {
        object_type: "project",
        object_id: args.projectId,
        subtype: args.objectType,
        variant: args.objectType,
      };
    case "project_logs":
      return {
        object_type: "project",
        object_id: args.projectId,
        subtype: "project_log",
        variant: "project_log",
      };
    default:
      return;
  }
}

export function useParseCustomColumnsSchema(
  params: Omit<ParseCustomColumnsSchemaParams, "conn" | "abort">,
  signals: number[],
) {
  const { customColumns, variant, rowScan, tableAlias, tableDef } = params;
  const duck = useDuckDB();
  return useQuery({
    queryKey: ["customColumns", duck?.connect, customColumns, rowScan],
    queryFn: async ({ signal }) => {
      const conn = await duck!.connect();

      const { customColumns: customColumnsTyped, customColumnsSchema } =
        await parseCustomColumnsSchema({
          conn,
          abort: signal,
          customColumns,
          variant,
          rowScan,
          tableAlias,
          tableDef,
        });

      return { customColumns: customColumnsTyped, customColumnsSchema };
    },
    throwOnError: false,
    enabled:
      !!duck &&
      !!rowScan &&
      customColumns !== undefined &&
      signals.every((s) => s > 0) &&
      !!tableDef,
  });
}
