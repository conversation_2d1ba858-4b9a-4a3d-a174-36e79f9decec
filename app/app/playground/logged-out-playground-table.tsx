"use client";

import {
  useMemo,
  useState,
  useCallback,
  type SetStateAction,
  type Dispatch,
} from "react";
import { type SyncedPlaygroundBlock } from "#/ui/prompts/schema";
import Footer from "#/ui/landing/footer";
import { Table } from "#/ui/arrow-table";
import { useViewStates } from "#/utils/view/use-view";
import { usePlaygroundFullscreenTaskIndex } from "#/ui/query-parameters";
import { type ModelSpec } from "@braintrust/proxy/schema";
import { LayoutTypeControl } from "#/ui/table/layout-type-control";
import { TableRowHeightToggle } from "#/ui/table-row-height-toggle";
import { LoggedOutPlaygroundTraceSheet } from "./logged-out-playground-trace-sheet";
import { Button } from "#/ui/button";
import { Blend, Plus, Trash } from "lucide-react";
import {
  CancelSelectionButton,
  SelectionBarButton,
} from "#/ui/table/selection-bar";
import { BasicTooltip } from "#/ui/tooltip";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "#/utils/search-btql";
import { type PlaygroundSettings } from "../app/[org]/p/[project]/playgrounds/settings";
import { produce } from "immer";
import { PlaygroundControls } from "../app/[org]/p/[project]/playgrounds/[playground]/playground-controls";
import { AppliedScorers } from "../app/[org]/p/[project]/playgrounds/[playground]/applied-scorers";
import { type SavedScorer } from "#/utils/scorers";
import { type UIFunction } from "#/ui/prompts/schema";
import {
  type DatasetRow,
  NEVER_VISIBLE_COLUMNS,
  LIST_INITIALLY_VISIBLE_COLUMNS,
  GRID_INITIALLY_VISIBLE_COLUMNS,
  TYPE_HINTS,
  STREAMING_CONTENT_PROPS,
  generateSummaryBreakdownData,
  createSchema,
  createDisplayPaths,
  createMockData,
  createExperimentName,
  createComparisonExperiments,
  createMultilineRow,
  DATASETS,
  FORMATTERS,
  extractMetadataPaths,
} from "./logged-out-playground-table-data";
import { newId } from "braintrust";
import { GroupBySelect } from "#/ui/table/grouping/controls";
import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import { BT_IS_GROUP } from "#/ui/table/grouping/queries";

export type LoggedOutPlaygroundData = {
  playgroundBlocks: SyncedPlaygroundBlock[];
  datasetRows: DatasetRow[];
  playgroundSettings: PlaygroundSettings;
  savedScorers: SavedScorer[];
  scorerFunctions: Record<string, UIFunction>;
};

type LoggedOutPlaygroundTableProps = {
  promptBlocks: SyncedPlaygroundBlock[];
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  allAvailableModels: Record<string, ModelSpec>;
  setLoggedOutPlaygroundData: Dispatch<SetStateAction<LoggedOutPlaygroundData>>;
  openUpsellDialog: VoidFunction;
  scrollMarginRef: React.RefObject<HTMLDivElement | null>;
} & Omit<LoggedOutPlaygroundData, "playgroundBlocks">;

export function LoggedOutPlaygroundTable({
  promptBlocks,
  scrollContainerRef,
  allAvailableModels,
  datasetRows,
  playgroundSettings,
  savedScorers,
  scorerFunctions,
  setLoggedOutPlaygroundData,
  openUpsellDialog,
  scrollMarginRef,
}: LoggedOutPlaygroundTableProps) {
  const { clauseChecker } = useClauseChecker("prompt_session");
  const viewProps = useViewStates({
    pageIdentifier: "logged_out_playground",
    viewParams: {
      viewType: "playground",
      objectType: "prompt_session",
      objectId: "logged_out_playground",
    },
    clauseChecker,
  });

  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [tableGrouping, setTableGrouping] = useState(GROUP_BY_NONE_VALUE);

  const tableGroupingOptions = useMemo(() => {
    const metadataPaths = extractMetadataPaths(datasetRows);
    return metadataPaths.map((path) => ({
      label: path,
      value: JSON.stringify(path.split(".")),
    }));
  }, [datasetRows]);

  const setDatasetRows = useCallback(
    (rows: SetStateAction<DatasetRow[]>) => {
      setLoggedOutPlaygroundData((data) => ({
        ...data,
        datasetRows: typeof rows === "function" ? rows(data.datasetRows) : rows,
      }));
    },
    [setLoggedOutPlaygroundData],
  );
  const setPlaygroundSettings = useCallback(
    (settings: SetStateAction<PlaygroundSettings>) => {
      setLoggedOutPlaygroundData((data) => ({
        ...data,
        playgroundSettings:
          typeof settings === "function"
            ? settings(data.playgroundSettings)
            : settings,
      }));
    },
    [setLoggedOutPlaygroundData],
  );

  const selectedRowData = useMemo(() => {
    return (
      datasetRows.find((row) => row.id === selectedRowId) || datasetRows[0]
    );
  }, [datasetRows, selectedRowId]);

  const handleAddRow = useCallback(() => {
    const newRow = {
      id: newId(),
      input: null,
      expected: null,
      metadata: null,
    };
    setDatasetRows((prev) => [...prev, newRow]);
    setSelectedRowId(newRow.id);
  }, [setDatasetRows]);

  const handlePrevRow = useCallback(() => {
    if (!selectedRowId) {
      return;
    }
    const currentIndex = datasetRows.findIndex(
      (row) => row.id === selectedRowId,
    );
    if (currentIndex > 0) {
      const newRowId = datasetRows[currentIndex - 1].id;
      setSelectedRowId(newRowId);
    }
  }, [selectedRowId, datasetRows]);

  const handleNextRow = useCallback(() => {
    if (!selectedRowId) {
      return;
    }
    const currentIndex = datasetRows.findIndex(
      (row) => row.id === selectedRowId,
    );
    if (currentIndex < datasetRows.length - 1) {
      const newRowId = datasetRows[currentIndex + 1].id;
      setSelectedRowId(newRowId);
    }
  }, [selectedRowId, datasetRows]);

  const hasPrevRow =
    selectedRowId && datasetRows.length > 1
      ? datasetRows.findIndex((row) => row.id === selectedRowId) > 0
      : false;
  const hasNextRow =
    selectedRowId && datasetRows.length > 1
      ? datasetRows.findIndex((row) => row.id === selectedRowId) <
        datasetRows.length - 1
      : false;

  const currentRowIndex = selectedRowId
    ? datasetRows.findIndex((row) => row.id === selectedRowId) + 1
    : 1;
  const headerText = `Row ${currentRowIndex} of ${datasetRows.length}`;

  const handleDatasetUpdate = (field: string, value: unknown) => {
    if (!selectedRowId) return;

    setDatasetRows((prev) =>
      prev.map((row) =>
        row.id === selectedRowId ? { ...row, [field]: value } : row,
      ),
    );
  };

  const schema = useMemo(() => {
    return createSchema(promptBlocks, viewProps.layout);
  }, [promptBlocks, viewProps.layout]);

  const displayPaths = useMemo(() => {
    return createDisplayPaths(viewProps.layout, schema);
  }, [viewProps.layout, schema]);

  const mockData = useMemo(() => {
    return createMockData(
      promptBlocks,
      viewProps.layout,
      schema,
      datasetRows,
      tableGrouping,
    );
  }, [promptBlocks, viewProps.layout, schema, datasetRows, tableGrouping]);

  const rowGroupingData = useMemo(() => {
    if (!tableGrouping || tableGrouping === GROUP_BY_NONE_VALUE) {
      return null;
    }

    const groupRows: Record<string, (typeof mockData)[0][]> = {};

    mockData.forEach((row) => {
      const groupKey = row.__bt_group_key || "null";

      if (!groupRows[groupKey]) {
        groupRows[groupKey] = [];
      }
      groupRows[groupKey].push(row);
    });

    return {
      groupRows: Object.entries(groupRows).map(([groupKey, rows]) => ({
        __bt_group_key: groupKey,
        __bt_subrows: rows,
        [BT_IS_GROUP]: true,
        span_type_info: groupKey,
      })),
      groupBy: tableGrouping,
      initialCollapseGroups: false,
    };
  }, [mockData, tableGrouping]);

  const experimentName = useMemo(() => {
    return createExperimentName(promptBlocks, allAvailableModels);
  }, [promptBlocks, allAvailableModels]);

  const comparisonExperiments = useMemo(() => {
    return createComparisonExperiments(promptBlocks, allAvailableModels);
  }, [promptBlocks, allAvailableModels]);

  const summaryBreakdownData = generateSummaryBreakdownData(
    promptBlocks,
    allAvailableModels,
  );

  const multilineRow = useMemo(() => {
    return createMultilineRow(
      viewProps.layout,
      viewProps.rowHeight ?? "compact",
      promptBlocks.length,
    );
  }, [viewProps.layout, viewProps.rowHeight, promptBlocks.length]);

  const [isCreatingScorer, setIsCreatingScorer] = useState(false);
  const [selectedScorerId, setSelectedScorerId] = useState<string | null>(null);
  const setSavedScorers = useCallback(
    (scorers: SetStateAction<SavedScorer[]>) => {
      setLoggedOutPlaygroundData((data) => ({
        ...data,
        savedScorers:
          typeof scorers === "function" ? scorers(data.savedScorers) : scorers,
      }));
    },
    [setLoggedOutPlaygroundData],
  );
  const setScorerFunctions = useCallback(
    (functions: SetStateAction<Record<string, UIFunction>>) => {
      setLoggedOutPlaygroundData((data) => ({
        ...data,
        scorerFunctions:
          typeof functions === "function"
            ? functions(data.scorerFunctions)
            : functions,
      }));
    },
    [setLoggedOutPlaygroundData],
  );

  const extraRightControls = useMemo(
    () => (
      <>
        <LayoutTypeControl
          viewProps={viewProps}
          defaultLayout="grid"
          disabledLayouts={["summary"]}
        />
        <GroupBySelect
          options={tableGroupingOptions}
          tableGrouping={tableGrouping}
          onTableGrouping={setTableGrouping}
        />
        {viewProps.layout === "grid" && (
          <TableRowHeightToggle
            tableRowHeight={viewProps.rowHeight ?? "compact"}
            onSetRowHeight={viewProps.setRowHeight}
          />
        )}
        <Button Icon={Plus} size="xs" variant="ghost" onClick={handleAddRow}>
          Row
        </Button>
        <PlaygroundControls
          projectId="logged_out_playground"
          isReadOnly={false}
          projectName=""
          updateDataset={openUpsellDialog}
          setIsNewDatasetDialogOpen={openUpsellDialog}
          openUploadDatasetDialog={openUpsellDialog}
          savedScorers={savedScorers}
          updateSavedScorers={async (scorers) => {
            setSavedScorers(scorers);
            return null;
          }}
          datasets={DATASETS}
          datasetId={DATASETS[0].id}
          numDatasetRecords={datasetRows.length}
          showOpenDatasetOption={false}
          datasetCountLoading={false}
          maxConcurrency={playgroundSettings.maxConcurrency ?? 10}
          setMaxConcurrency={(maxConcurrency) => {
            setPlaygroundSettings(
              produce(playgroundSettings, (draft) => {
                draft.maxConcurrency = maxConcurrency;
              }),
            );
          }}
          strict={playgroundSettings.strict ?? undefined}
          setStrict={(strict) => {
            setPlaygroundSettings(
              produce(playgroundSettings, (draft) => {
                draft.strict = strict;
              }),
            );
          }}
          extraMessages={playgroundSettings.extraMessages ?? undefined}
          setExtraMessages={(extraMessages) => {
            setPlaygroundSettings(
              produce(playgroundSettings, (draft) => {
                draft.extraMessages = extraMessages;
              }),
            );
          }}
          isCreatingScorer={isCreatingScorer}
          setIsCreatingScorer={setIsCreatingScorer}
          selectedScorerId={selectedScorerId}
          setSelectedScorerId={setSelectedScorerId}
          numScorers={savedScorers.length}
          numTasks={promptBlocks.length}
          scorerFunctionOverride={scorerFunctions}
          createPromptOverride={async (prompt) => {
            setScorerFunctions((prev) => ({ ...prev, [prompt.id]: prompt }));
            return null;
          }}
        />
      </>
    ),
    [
      viewProps,
      handleAddRow,
      savedScorers,
      datasetRows.length,
      playgroundSettings,
      isCreatingScorer,
      selectedScorerId,
      promptBlocks.length,
      scorerFunctions,
      setSavedScorers,
      setPlaygroundSettings,
      setScorerFunctions,
      openUpsellDialog,
      tableGroupingOptions,
      tableGrouping,
    ],
  );

  const numSelectedRows = Object.keys(rowSelection).length;
  const toolBarSlot = useMemo(
    () => (
      <>
        <CancelSelectionButton
          onCancelSelection={() => setRowSelection({})}
          selectedRowsNumber={numSelectedRows}
        />
        <Button size="xs" onClick={openUpsellDialog}>
          Add to dataset
        </Button>
        <SelectionBarButton isDropdown onClick={openUpsellDialog}>
          Download
        </SelectionBarButton>
        <Button
          size="xs"
          variant="border"
          Icon={Blend}
          onClick={openUpsellDialog}
        />
        {datasetRows.length > 1 && (
          <BasicTooltip
            tooltipContent={`Delete selected rows from current playground`}
          >
            <SelectionBarButton
              onClick={() => {
                setDatasetRows(
                  datasetRows.filter((row) => !rowSelection[row.id]),
                );
                setRowSelection({});
              }}
              Icon={Trash}
            />
          </BasicTooltip>
        )}
      </>
    ),
    [
      numSelectedRows,
      openUpsellDialog,
      datasetRows,
      setDatasetRows,
      rowSelection,
    ],
  );

  const afterToolbarSlot = useMemo(
    () =>
      savedScorers.length > 0 ? (
        <div className="ml-auto">
          <AppliedScorers
            savedScorers={savedScorers}
            scorerFunctions={scorerFunctions}
            updateSavedScorers={setSavedScorers}
            setSelectedScorerId={setSelectedScorerId}
          />
        </div>
      ) : null,
    [savedScorers, scorerFunctions, setSavedScorers],
  );

  const rowEvents = useMemo(
    () => ({
      onClick: (row: { id: string }) => () => {
        setSelectedRowId(row.id);
      },
    }),
    [],
  );

  const [fullscreenTaskIndex] = usePlaygroundFullscreenTaskIndex();

  if (fullscreenTaskIndex !== null || promptBlocks.length === 0) {
    return null;
  }

  return (
    <>
      <Table
        data={mockData}
        fields={schema.fields}
        viewProps={viewProps}
        formatters={FORMATTERS}
        isPlayground={true}
        neverVisibleColumns={NEVER_VISIBLE_COLUMNS}
        initiallyVisibleColumns={
          viewProps.layout === "list"
            ? LIST_INITIALLY_VISIBLE_COLUMNS
            : GRID_INITIALLY_VISIBLE_COLUMNS
        }
        displayPaths={displayPaths}
        experimentName={experimentName}
        comparisonExperiments={comparisonExperiments}
        scrollContainerRef={scrollContainerRef}
        extraRightControls={
          numSelectedRows === 0 ? extraRightControls : undefined
        }
        toolbarSlot={numSelectedRows > 0 ? toolBarSlot : undefined}
        afterToolbarSlot={afterToolbarSlot}
        multilineRow={multilineRow}
        typeHints={TYPE_HINTS}
        streamingContentProps={STREAMING_CONTENT_PROPS}
        rowEvents={rowEvents}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        showRowNumber={true}
        summaryBreakdownData={summaryBreakdownData}
        rowGroupingData={rowGroupingData}
        runAISearch={async () => {
          openUpsellDialog();
          return { match: false };
        }}
        scrollMarginRef={scrollMarginRef}
        className="pt-3"
      />
      <div className="grow" />
      <Footer inApp className="sticky left-0 w-full pb-4 sm:pb-4 lg:pb-4" />
      <LoggedOutPlaygroundTraceSheet
        isOpen={selectedRowId !== null}
        onClose={() => {
          setSelectedRowId(null);
        }}
        datasetData={selectedRowData}
        onDatasetUpdate={handleDatasetUpdate}
        onAddRow={handleAddRow}
        onPrevRow={handlePrevRow}
        onNextRow={handleNextRow}
        hasPrevRow={hasPrevRow}
        hasNextRow={hasNextRow}
        headerText={headerText}
        onRunRow={openUpsellDialog}
      />
    </>
  );
}
