"use client";

import { type AdminProjectContext } from "#/app/admin/actions";
import {
  ProjectContext,
  type ProjectContextT,
} from "#/app/app/[org]/p/[project]/projectContext";

export default function ClientLayout({
  children,
  orgName,
  projectName,
  adminProjectContext,
}: {
  children: React.ReactNode;
  orgName: string;
  projectName: string;
  adminProjectContext: AdminProjectContext;
}) {
  const projectContext: ProjectContextT = {
    orgName,
    projectId: adminProjectContext.id,
    projectName,
    projectSettings: null,
    projectPermissions: [],
    mutateProject: async () => {},
    experimentsReady: 0,
    isExperimentsLoading: false,
    experimentsTable: null,
    experiments: [],
    mutateExperiments: async () => {},
    datasetsReady: 0,
    datasetsTable: null,
    orgDatasets: [],
    mutateDatasets: async () => {},
    config: {
      automations: [],
      scores: [],
      tags: [],
      span_iframes: [],
      metricDefinitions: [],
    },
    isConfigLoading: false,
    mutateConfig: async () => {},
    projectDatasets: [],
    mutateProjectDatasets: async () => {},
  };
  return (
    <ProjectContext.Provider value={projectContext}>
      {children}
    </ProjectContext.Provider>
  );
}
