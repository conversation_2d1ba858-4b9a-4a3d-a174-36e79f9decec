import { getMetricsLabel } from "./utils";
import { isNumber } from "#/utils/object";

// no group internal processing value - hope to avoid any collisions with customer data
export const NO_GROUP_INTERNAL_NAME = "__no_group";
const NO_GROUP_DISPLAY_NAME = "None"; // what we show to customers

type ScoresRecord = Record<
  string,
  { avg: number | null; last_updated: number; count: bigint }
>;

type ToolRecord = Record<
  string,
  {
    count: bigint;
    error_rate: number;
    p50_duration: number | null;
  }
>;
export type ToolMetric = NonNullable<keyof ToolRecord[string]>;

export type MetricsData = {
  time: number;
  count: bigint;
  group?: string | null;
  scores?: ScoresRecord;
  tools?: ToolRecord;
  [key: string]:
    | number
    | bigint
    | string
    | null
    | undefined
    | ScoresRecord
    | ToolRecord;
};

type TimeGroups = Record<string, { groups: Record<string, MetricsData> }>;
type ColorMap = Record<
  string,
  Record<"metadata" | "none" | "score" | "metric", number>
>;

export function getSeriesColorMap(allSeries: string[]): ColorMap {
  const map: ColorMap = {};
  allSeries.forEach((series, i) => {
    map[series] = { metadata: i, none: i, score: i, metric: i };
  });
  return map;
}

export function getGroupNames(data: TimeGroups): string[] {
  const groups = new Set<string>();
  Object.values(data).forEach((timeData) => {
    Object.keys(timeData.groups).forEach((group) => groups.add(group));
  });
  return Array.from(groups).sort();
}

export function getGroupedSeriesNames({
  groupNames,
  selectedSeries,
  metricToDisplayName,
}: {
  groupNames: string[];
  selectedSeries: string[];
  metricToDisplayName?: Map<string, string>;
}) {
  const groupedSeriesNames: string[] = [];
  const groupValues: string[] = [];
  groupNames.forEach((groupName) => {
    selectedSeries.forEach((series) => {
      const metric =
        metricToDisplayName?.get(series) ?? getMetricsLabel(series);
      const group =
        groupName === NO_GROUP_INTERNAL_NAME
          ? NO_GROUP_DISPLAY_NAME
          : groupName;
      groupedSeriesNames.push(`${metric} (${group})`);
      groupValues.push(groupName);
    });
  });
  return { names: groupedSeriesNames, groupValues };
}

export function getDataSeries({
  data,
  selectedSeries,
  toolMetric = "count",
}: {
  data: MetricsData[];
  selectedSeries: string[];
  toolMetric?: ToolMetric;
}) {
  return data.map((d, i) => ({
    x: d.time,
    y: selectedSeries.map((series) => {
      if (d.scores) {
        if (isNumber(d.scores[series]?.avg)) {
          return { value: d.scores[series].avg };
        }
        return null;
      } else if (d.tools) {
        if (isNumber(d.tools[series]?.[toolMetric])) {
          return { value: Number(d.tools[series][toolMetric]) };
        }
        return null;
      } else if (isNumber(d[series])) {
        return { value: Number(d[series]) };
      }

      return null;
    }),
    metadata: {
      time: new Date(d.time).toISOString(),
      count: BigInt(d.count ?? 0),
    },
  }));
}

export function getGroupedDataSeries({
  groupedData,
  groupNames,
  selectedSeries,
  toolMetric = "count",
}: {
  groupedData: TimeGroups;
  groupNames: string[];
  selectedSeries: string[];
  toolMetric?: ToolMetric;
}) {
  return Object.entries(groupedData).map(([time, data], index) => {
    let totalGroupCount: bigint = BigInt(0);
    const seriesValues: ({ value: number } | null)[] = [];

    groupNames.forEach((groupName) => {
      const groupData = data.groups[groupName];

      if (groupData) {
        totalGroupCount += groupData.count;

        selectedSeries.forEach((series) => {
          if (groupData.scores) {
            const value = groupData.scores[series];
            seriesValues.push(
              value && isNumber(value.avg) ? { value: value.avg } : null,
            );
          } else if (groupData.tools) {
            const value = groupData.tools[series];
            seriesValues.push(
              value && isNumber(value[toolMetric])
                ? { value: Number(value[toolMetric]) }
                : null,
            );
          } else {
            const value = groupData[series];
            seriesValues.push(
              isNumber(value) ? { value: Number(value) } : null,
            );
          }
        });
      } else {
        selectedSeries.forEach(() => seriesValues.push(null));
      }
    });
    return {
      x: Number(time),
      y: seriesValues,
      metadata: {
        time: new Date(Number(time)).toISOString(),
        count: totalGroupCount,
      },
    };
  });
}

export function isEmptyMetricsData(data: MetricsData): boolean {
  return Object.entries(data).every(([key, value]) => {
    // Ignore time and count fields
    if (key === "time" || key === "count") {
      return true;
    }
    return value === null;
  });
}

export function groupData(data: MetricsData[]): TimeGroups {
  return data.reduce((acc: TimeGroups, item) => {
    const time = item.time;
    if (!acc[time]) {
      acc[time] = { groups: {} };
    }

    // Use NO_GROUP_INTERNAL_NAME for null group
    const groupName = item.group ?? NO_GROUP_INTERNAL_NAME;

    // If the metric is all nulls, don't add it to a group
    // This prevents empty series from being shown
    if (!isEmptyMetricsData(item)) {
      acc[time].groups[groupName] = item;
    }

    return acc;
  }, {});
}
