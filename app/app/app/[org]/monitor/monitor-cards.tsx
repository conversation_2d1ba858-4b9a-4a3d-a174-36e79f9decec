import { useFullscreenMonitorCardState as useFullscreenMonitorCardState } from "#/ui/query-parameters";
import { useEffect, useMemo, useState } from "react";
import { type ChartTimeFrame } from "./time-controls/time-range";
import { MONITOR_CARDS } from "./card-config/monitor-cards.constants";
import { EmptyMonitorState } from "./empty-monitor-state";
import { cn } from "#/utils/classnames";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { MonitorCard } from "./card/monitor-card";
import { useHotkeys } from "react-hotkeys-hook";
import { type Search } from "#/utils/search/search";
import { getTextSearchInputFields } from "#/utils/get-text-search-input-fields";
import { type Expr } from "@braintrust/btql/parser";

export interface CardState {
  isLoading: boolean;
  hasData: boolean;
  hasError: boolean;
  error?: Error;
}

function MonitorCards({
  chartTimeFrame,
  projectIds,
  search,
  groupBy,
  from,
  experimentIds,
  onBrush,
  isFirstLoad,
  tzUTC,
}: {
  chartTimeFrame: ChartTimeFrame;
  projectIds: string[];
  search?: Search | null;
  groupBy?: string;
  from: "project_logs" | "experiment";
  experimentIds: string[];
  onBrush: (v: [number, number] | null) => void;
  isFirstLoad?: boolean;
  tzUTC?: boolean;
}) {
  const [fullscreenCardState, setFullscreenCardState] =
    useFullscreenMonitorCardState();
  const [cardStates, setCardStates] = useState<CardState[]>([]);

  useHotkeys(
    ["Escape"],
    () => {
      setFullscreenCardState(null);
    },
    [setFullscreenCardState],
    { description: "Exit fullscreen", enabled: Boolean(fullscreenCardState) },
  );

  const additionalFilters = useMemo(() => {
    const filters: Expr[] = [];

    // conversion of search filters to Expr
    if (search?.filter) {
      const filtersBtql = search.filter.map((f) => ({ btql: f.text }));
      filtersBtql.forEach((f) => filters.push(f));
    }

    // search text
    const searchFilter = search?.match?.[0]?.text;
    if (searchFilter) {
      // search filter is true is when any field matches the searchFilter
      const fieldsToSearch = getTextSearchInputFields(from);
      const searchFilterExpressions: Expr[] = fieldsToSearch.map((f) => ({
        left: {
          name: [f],
          op: "ident",
        },
        op: "match",
        right: {
          op: "literal",
          value: searchFilter,
        },
      }));

      // use OR for any field match
      const searchFilterOr: Expr = {
        children: searchFilterExpressions,
        op: "or",
      };

      // then appended to filters (which is all AND)
      filters.push(searchFilterOr);
    }

    return filters;
  }, [search, from]);

  const commonProps = useMemo(() => {
    return {
      projectIds,
      experimentIds,
      chartTimeFrame,
      filters: additionalFilters,
      groupBy,
      from,
      tzUTC,
      onBrush,
    };
  }, [
    projectIds,
    experimentIds,
    chartTimeFrame,
    additionalFilters,
    groupBy,
    from,
    tzUTC,
    onBrush,
  ]);

  // only show nodata message once
  const [shownCount, setShownCount] = useState<number>(0);
  const allLoadedNoData = useMemo(() => {
    return (
      isFirstLoad &&
      !!cardStates.length &&
      cardStates.every((c) => !c.isLoading && !c.hasData)
    );
  }, [cardStates, isFirstLoad]);

  useEffect(() => {
    if (allLoadedNoData) {
      setShownCount((p) => p + 1);
    }
  }, [allLoadedNoData]);

  const monitorCards = useMemo(
    () =>
      MONITOR_CARDS.map((config, i) => {
        const setCardState = (state: CardState) => {
          setCardStates((p) => {
            const copy = [...p];
            copy[i] = state;
            return copy;
          });
        };
        return {
          component: (
            <MonitorCard
              key={`${from}-${config.name}`}
              {...commonProps}
              cardConfig={config}
              setCardState={setCardState}
              standaloneLayoutMode={config.name === fullscreenCardState}
            />
          ),
          config,
        };
      }),
    [commonProps, from, fullscreenCardState],
  );

  const hasToolsData = useMemo(() => {
    return cardStates.some((c, i) => {
      if (!monitorCards[i]) {
        return false;
      }
      return Boolean(monitorCards[i].config.toolMetric) && c.hasData;
    });
  }, [cardStates, monitorCards]);

  const numRows = Math.ceil(MONITOR_CARDS.length / 2);

  const chartContainerClassName = "flex h-72 md:h-96 md:flex-1 flex-col";
  const fullscreenClassName = "flex-1 h-auto md:h-auto p-2";
  const rows = useMemo(() => {
    return new Array(numRows).fill(0).map((_, i) => {
      const firstCard = monitorCards[2 * i];
      const secondCard = monitorCards[2 * i + 1];

      // if both are tools and no tools data, hide row
      const firstIsTool = Boolean(firstCard?.config.toolMetric);
      const secondIsTool = Boolean(secondCard?.config.toolMetric);
      const allTools =
        (!firstCard || firstIsTool) && (!secondCard || secondIsTool);
      const hideRow = !hasToolsData && allTools;

      const isFirstFullscreen = firstCard?.config.name === fullscreenCardState;
      const isSecondFullscreen =
        secondCard?.config.name === fullscreenCardState;
      const hasAFullscreen = isFirstFullscreen || isSecondFullscreen;

      return (
        <div
          className={cn("flex flex-none flex-col gap-4 md:flex-row", {
            hidden: hideRow,
            "flex-1 flex-row": hasAFullscreen,
          })}
          key={`monitor-row-${i}`}
        >
          <div
            className={cn(chartContainerClassName, {
              [fullscreenClassName]: isFirstFullscreen,
              hidden: fullscreenCardState && !isFirstFullscreen,
            })}
          >
            {firstCard?.component ?? ""}
          </div>
          <div
            className={cn(chartContainerClassName, {
              [fullscreenClassName]: isSecondFullscreen,
              hidden: fullscreenCardState && !isSecondFullscreen,
            })}
          >
            {secondCard?.component ?? ""}
          </div>
        </div>
      );
    });
  }, [monitorCards, numRows, hasToolsData, fullscreenCardState]);

  const unavailable = useMemo(() => {
    return cardStates.every(
      (c) => c.error && c.error.message.includes("too costly"),
    );
  }, [cardStates]);

  return (
    <>
      {shownCount <= 1 &&
        allLoadedNoData &&
        (!projectIds.length ? (
          <TableEmptyState label="Create a project to get started with monitoring" />
        ) : (
          <EmptyMonitorState
            className="mb-2"
            rowType={from === "experiment" ? "experiment" : "logs"}
          />
        ))}
      {unavailable && (
        <TableEmptyState label="The monitor page is currently unavailable when running a self-hosted setup with Postgres-only" />
      )}
      <div
        className={cn("flex flex-1 flex-col gap-4", {
          "gap-0": fullscreenCardState,
        })}
      >
        {rows}
      </div>
    </>
  );
}

export { MonitorCards };
