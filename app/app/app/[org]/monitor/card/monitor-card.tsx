import { useHighlightState } from "#/ui/charts/highlight";
import { Skeleton } from "#/ui/skeleton";
import { useState, useMemo, useCallback, useEffect } from "react";
import { useOrg } from "#/utils/user";
import {
  getDataSeries,
  getGroupedDataSeries,
  getGroupedSeriesNames,
  getGroupNames,
  groupData,
  NO_GROUP_INTERNAL_NAME,
} from "../groups";
import { getTracesLink } from "../getMonitorLink";
import { useRouter } from "next/navigation";
import { type ChartTimeFrame } from "../time-controls/time-range";
import { TimeseriesChart } from "#/ui/charts/timeseries/timeseries-chart";
import { metricsDataToTimeseriesData } from "#/ui/charts/timeseries-data/metrics-data-to-timeseries-data";
import {
  formatValueForSelectionType,
  type SelectionType,
} from "#/ui/charts/selectionTypes";
import { sortedTimeseriesData } from "#/ui/charts/timeseries-data/sorted-timeseries-data";
import { timeBucketToDuration } from "#/ui/charts/time-bucket";
import { type MonitorCardConfig } from "./monitor-card-config.types";
import { type CardState } from "../monitor-cards";
import { useCardConfigQuery } from "./use-card-config-query";
import { MonitorCardHeader } from "./monitor-card-header";
import {
  parseAsJsonEncoded,
  useFullscreenMonitorCardState,
} from "#/ui/query-parameters";
import { cn } from "#/utils/classnames";
import { type Expr } from "@braintrust/btql/parser";
import { useQueryState } from "nuqs";
import { urlSearchSchema } from "#/utils/search/search";
import { type ResponsiveFeatures } from "#/ui/charts/padding/get-responsive-features";
import { getDurationBucket } from "../time-controls/get-duration-bucket";

interface SeriesMetadataExtension {
  name: string;
  selectionType: SelectionType;
  groupByKey?: string;
  groupByValue?: string;
}

interface MonitorCardProps {
  /** The serializable monitor card config */
  cardConfig: MonitorCardConfig;

  /** Callback so that the card can communicate some state upwards */
  setCardState: (state: CardState) => void;

  /** Timeframe to query over */
  chartTimeFrame: ChartTimeFrame;

  /** Where we're querying from */
  from: "experiment" | "project_logs";
  projectIds: string[];
  experimentIds: string[];

  /** Optional group by to append to query */
  groupBy?: string;

  /** Callback when brush interaction ends */
  onBrush: (v: [number, number] | null) => void;

  /** If we should use UTC alignment instead of local */
  tzUTC?: boolean;

  /** Additional filters to apply to the query */
  filters?: Expr[];

  /** Turn off mouse click interaction */
  disableClick?: boolean;

  /** Click message shown on tooltip */
  clickMessage?: string;

  /** click event to use instead of the default url redirect */
  onTimeBucketClick?: (timeBucket: { start: number; end: number }) => void;

  /** instead of using chart timeframe, query for all time and base timeframe on data */
  allTimeQuery?: boolean;

  /**
   * Use data to dynamically alter the layout of the graph.
   * This mode prioritizes individual aesthetic over any grid alignment.
   * */
  standaloneLayoutMode?: boolean;

  /**
   * Optional overrides to any of the responsive features
   * - false forces off
   * - true forces on
   * - else uses default responsive behavior
   */
  overrides?: Partial<ResponsiveFeatures>;

  /** Class name for the card */
  className?: string;
}

function MonitorCard({
  groupBy,
  chartTimeFrame,
  from,
  onBrush,
  cardConfig,
  setCardState,
  projectIds,
  experimentIds,
  tzUTC,
  filters,
  disableClick,
  clickMessage = "(Click to view traces)",
  overrides,
  standaloneLayoutMode,
  className,
  allTimeQuery,
  onTimeBucketClick,
}: MonitorCardProps) {
  const org = useOrg();
  const orgName = org.name;

  const router = useRouter();
  const highlightState = useHighlightState();

  const [selectedMeasures, setSelectedMeasures] = useState<string[] | null>();

  const toolMetric = cardConfig.toolMetric;
  const vizType = cardConfig.vizType;

  const [vizWidth, setVizWidth] = useState<number | null>(null);

  const timeBucket = useMemo(() => {
    const { start, end } = chartTimeFrame;
    return getDurationBucket(end - start, vizType, vizWidth ?? undefined);
  }, [chartTimeFrame, vizType, vizWidth]);

  const { data, loading, error, lastTimeBucket } = useCardConfigQuery({
    cardConfig,
    projectIds,
    chartTimeFrame,
    timeBucket,
    groupBy,
    experimentIds,
    from,
    filters,
    tzUTC,
    allTimeQuery,
  });

  const { allMeasureNames, metricToDisplayName } = useMemo(() => {
    if (!data) {
      return { allMeasureNames: [] };
    }

    if (cardConfig.pivot) {
      const pivotKey = cardConfig.idName;
      const uniqueKeys = new Set<string>();
      for (const item of data) {
        if (item[pivotKey]) {
          Object.keys(item[pivotKey]).forEach((key) => uniqueKeys.add(key));
        }
      }
      return { allMeasureNames: Array.from(uniqueKeys) };
    }

    const availableMeasures = cardConfig.measures.filter(
      (n) => n.alias !== "count",
    );
    const map = new Map<string, string>();
    availableMeasures.forEach((m) => {
      map.set(m.alias, m.displayName ?? m.alias);
    });

    // in order of config
    const allMeasureNames = availableMeasures.map((m) => m.alias);
    return {
      allMeasureNames,
      metricToDisplayName: map,
    };
  }, [cardConfig, data]);

  // set to all measures selected if not initialized
  useEffect(() => {
    if (!selectedMeasures && allMeasureNames.length > 0) {
      setSelectedMeasures(allMeasureNames);
    }
  }, [allMeasureNames, selectedMeasures]);

  const aggregateFormatter = useMemo(() => {
    const { unitType } = cardConfig;
    if (unitType === "duration") {
      return (value: number) =>
        formatValueForSelectionType(value, {
          value: "duration",
          type: "metric",
        });
    }

    if (unitType === "cost") {
      return (v: number) =>
        formatValueForSelectionType(v, { value: "cost", type: "metric" });
    }

    if (unitType === "percent") {
      return (value: number) =>
        value.toLocaleString(undefined, {
          style: "percent",
          maximumFractionDigits: 0,
        });
    }

    return (value: number) =>
      value.toLocaleString(undefined, {
        notation: "standard",
        maximumFractionDigits: 1,
      });
  }, [cardConfig]);

  const tickFormatter = useMemo(() => {
    const { unitType } = cardConfig;

    // for count we use a shorter version than the aggregator
    if (unitType === "count") {
      return (value: number) =>
        value.toLocaleString(undefined, {
          notation: "compact",
          compactDisplay: "short",
          maximumFractionDigits: 1,
        });
    }

    // otherwise use same as aggr
    return aggregateFormatter;
  }, [cardConfig, aggregateFormatter]);

  useEffect(() => {
    setCardState({
      isLoading: loading,
      hasError: Boolean(error),
      hasData: Boolean(data && data.length > 0),
      error,
    });
  }, [loading, data, error, setCardState]);

  // try to base on what the data is tied to
  const timeBucketDuration = useMemo(() => {
    return timeBucketToDuration(lastTimeBucket);
  }, [lastTimeBucket]);

  const { seriesData, allSeriesNames, allSeriesGroupValues } = useMemo(() => {
    if (!data) {
      return {
        seriesData: [],
        allSeriesNames: [],
        allSeriesGroupValues: [],
      };
    }

    const groupedData = groupBy ? groupData(data) : {};
    const allGroupNames = getGroupNames(groupedData);

    const seriesData = groupBy
      ? getGroupedDataSeries({
          groupedData,
          groupNames: allGroupNames,
          selectedSeries: selectedMeasures ?? allMeasureNames,
          toolMetric,
        })
      : getDataSeries({
          data,
          selectedSeries: selectedMeasures ?? allMeasureNames,
          toolMetric,
        });

    // remove time buckets where all values are 'empty'
    // both lines and bars require finite not null
    // bars also require positive
    const isEmpty =
      cardConfig.vizType === "bars"
        ? (v: number) => Number.isFinite(v) && v > 0
        : (v: number) => Number.isFinite(v);
    const filteredSeriesData = seriesData.filter((d) =>
      d.y.some((y) => y !== null && isEmpty(y.value)),
    );

    if (groupBy) {
      const { names: allSeriesNames, groupValues: allSeriesGroupValues } =
        getGroupedSeriesNames({
          groupNames: allGroupNames,
          selectedSeries: selectedMeasures ?? allMeasureNames,
          metricToDisplayName,
        });

      return {
        seriesData: filteredSeriesData,
        allSeriesNames,
        allSeriesGroupValues,
      };
    }

    // no group by case
    const allSeriesNames = (selectedMeasures ?? allMeasureNames).map(
      (n) => metricToDisplayName?.get(n) ?? n,
    );
    const allSeriesGroupValues = allSeriesNames.map((_) => undefined);
    return {
      seriesData: filteredSeriesData,
      allSeriesNames,
      allSeriesGroupValues,
    };
  }, [
    groupBy,
    data,
    selectedMeasures,
    toolMetric,
    allMeasureNames,
    metricToDisplayName,
    cardConfig.vizType,
  ]);

  const timeseriesData = useMemo(() => {
    const seriesMeta: SeriesMetadataExtension[] = allSeriesNames.map(
      (v, i) => ({
        name: v,
        groupByKey: `metadata.${groupBy}`,
        groupByValue: allSeriesGroupValues[i],
        selectionType: {
          value: v,
          type: "score",
        },
      }),
    );

    const unsortedTimeseries =
      metricsDataToTimeseriesData<SeriesMetadataExtension>(
        seriesData,
        seriesMeta,
        timeBucketDuration,
      );

    return sortedTimeseriesData(unsortedTimeseries, vizType);
  }, [
    seriesData,
    allSeriesNames,
    vizType,
    timeBucketDuration,
    groupBy,
    allSeriesGroupValues,
  ]);

  // all time queries can alter the chart timeframe
  const timeseriesTimeFrame = useMemo(() => {
    if (!allTimeQuery) {
      return chartTimeFrame;
    }

    // for all time, take union of data extents and chart timeframe
    const { timestamps, timeBucketDuration } = timeseriesData;
    if (timestamps.length <= 0) {
      return chartTimeFrame;
    }

    return {
      start: Math.min(timestamps[0], chartTimeFrame.start),
      end: Math.max(
        timestamps[timestamps.length - 1] + timeBucketDuration,
        chartTimeFrame.end,
      ),
    };
  }, [chartTimeFrame, allTimeQuery, timeseriesData]);

  // the project id if there is only one
  const singleProjectId = useMemo(() => {
    if (projectIds && projectIds.length === 1) {
      return projectIds[0];
    }
  }, [projectIds]);

  const [existingSearch] = useQueryState(
    "search",
    parseAsJsonEncoded(urlSearchSchema.parse).withDefault({}),
  );

  const onChartClick = useCallback(
    async (
      event: React.MouseEvent,
      timestamp: number,
      groupByKey?: string,
      groupByValue?: string,
    ) => {
      if (!singleProjectId) return;

      // use provided click if provided
      if (onTimeBucketClick) {
        onTimeBucketClick({
          start: timestamp,
          end: timestamp + timeBucketDuration,
        });
        return;
      }

      let extraFilter;
      if (groupByKey && groupByValue) {
        if (groupByValue === NO_GROUP_INTERNAL_NAME) {
          extraFilter = `${groupByKey} IS NULL`;
        } else {
          extraFilter = `${groupByKey} = "${groupByValue}"`;
        }
      }

      const url = await getTracesLink({
        orgName: orgName,
        projectId: singleProjectId,
        from,
        timeBucket,
        time: new Date(timestamp).toISOString(),
        existingSearch,
        extraFilter,
      });

      if (event.metaKey || event.ctrlKey || event.button === 1) {
        window.open(url, "_blank");
      } else {
        router.push(url);
      }
    },
    [
      orgName,
      singleProjectId,
      from,
      router,
      timeBucket,
      existingSearch,
      onTimeBucketClick,
      timeBucketDuration,
    ],
  );

  const [fullscreenCard] = useFullscreenMonitorCardState();
  const isFullscreen = fullscreenCard === cardConfig.name;

  const onSeriesSelectionChange = useCallback(
    (seriesName: string) => {
      if (!selectedMeasures) {
        return;
      }
      if (selectedMeasures.includes(seriesName)) {
        setSelectedMeasures(selectedMeasures.filter((s) => s !== seriesName));
        return;
      }
      // Keep same order as allMeasureNames by filtering allMeasureNames
      setSelectedMeasures(
        allMeasureNames.filter((s) =>
          [...selectedMeasures, seriesName].includes(s),
        ),
      );
    },
    [selectedMeasures, allMeasureNames],
  );

  const showReset = useMemo(() => {
    const measureSelection =
      selectedMeasures && selectedMeasures.length !== allMeasureNames.length;
    const seriesSelection = highlightState.state.selected.length > 0;
    return measureSelection || seriesSelection;
  }, [selectedMeasures, allMeasureNames, highlightState]);

  const onReset = useCallback(() => {
    highlightState.clearSelected();
    setSelectedMeasures(allMeasureNames);
  }, [highlightState, allMeasureNames]);

  const onZoomOut = useCallback(() => {
    const { start, end } = chartTimeFrame;
    const duration = end - start;
    const maxLookback = 5 * 365 * 86_400_000; // 5 years

    // expand by 2x duration on each side
    // clamp between now and a max lookback
    const newEnd = Math.min(new Date().getTime(), end + 2 * duration);
    const newStart = Math.max(
      new Date().getTime() - maxLookback,
      start - 2 * duration,
    );

    onBrush([newStart, newEnd]);
  }, [chartTimeFrame, onBrush]);

  if (!data && loading) {
    return <Skeleton className="min-h-48 flex-1" />;
  }

  return (
    <div
      className={cn(
        "group flex flex-1 flex-col rounded-lg p-2 bg-primary-50 border-primary-100",
        {
          "p-0 bg-transparent": isFullscreen,
        },
        className,
      )}
    >
      {cardConfig.header && (
        <div className="mb-5 flex items-center gap-2">
          <MonitorCardHeader
            title={cardConfig.header.title}
            cardName={cardConfig.name}
            iconName={cardConfig.header.iconName}
            showReset={showReset}
            onReset={onReset}
            showSeriesSelect={Boolean(data && data.length > 0)}
            selectedSeries={selectedMeasures ?? allMeasureNames}
            availableSeries={allMeasureNames}
            onSelectionChange={onSeriesSelectionChange}
            metricToDisplayName={metricToDisplayName}
          />
        </div>
      )}
      <TimeseriesChart
        timeseriesData={timeseriesData}
        chartTimeFrame={timeseriesTimeFrame}
        highlightState={highlightState}
        aggregateFormatter={aggregateFormatter}
        tickFormatter={tickFormatter}
        onClick={!disableClick && singleProjectId ? onChartClick : undefined}
        onDoubleClick={onZoomOut}
        clickMessage={clickMessage}
        onBrush={onBrush}
        isFetchingData={loading}
        hasError={Boolean(error)}
        vizType={vizType}
        tzUTC={tzUTC}
        standaloneLayoutMode={standaloneLayoutMode}
        overrides={overrides}
        setVizWidth={setVizWidth}
      />
    </div>
  );
}

export { MonitorCard };
