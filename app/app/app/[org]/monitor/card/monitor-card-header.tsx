import { <PERSON><PERSON> } from "#/ui/button";
import { useFullscreenMonitorCardState } from "#/ui/query-parameters";
import { Expand, Shrink } from "lucide-react";
import { type CardIconName } from "./monitor-card-config.types";
import { MonitorCardIcon } from "./monitor-card-icon";
import { SeriesSelect } from "../series-select";
import { cn } from "#/utils/classnames";

interface MonitorCardHeaderProps {
  title: string;
  iconName: CardIconName;
  cardName: string;
  showSeriesSelect: boolean;
  selectedSeries: string[];
  availableSeries: string[];
  metricToDisplayName?: Map<string, string>;
  showReset: boolean;
  onReset: () => void;
  onSelectionChange?: (seriesName: string) => void;
  size?: "sm" | "md" | "lg";
}

export const MonitorCardHeader = (props: MonitorCardHeaderProps) => {
  const {
    title,
    iconName,
    cardName,
    showReset,
    onReset,
    showSeriesSelect,
    selectedSeries,
    availableSeries,
    onSelectionChange,
    metricToDisplayName,
    size,
  } = props;

  const [fullscreenCard, setFullscreenCard] = useFullscreenMonitorCardState();
  const isFullscreen = fullscreenCard === cardName;

  const Icon = MonitorCardIcon(iconName);

  return (
    <>
      <div
        className={cn(
          "flex flex-1 items-center gap-2 text-sm text-primary-800",
          {
            "text-xs": size === "sm",
            "text-base": size === "lg",
          },
        )}
      >
        {iconName && (
          <Icon
            className={cn("size-4", {
              "size-3": size === "sm",
            })}
          />
        )}
        {title}
      </div>
      {showReset && (
        <Button
          size="xs"
          variant="ghost"
          className={
            isFullscreen
              ? undefined
              : "opacity-0 transition-opacity text-primary-500 group-hover:opacity-100"
          }
          onClick={onReset}
        >
          Reset
        </Button>
      )}
      <Button
        variant={isFullscreen ? "inverted" : "ghost"}
        size="xs"
        className={
          isFullscreen
            ? undefined
            : "opacity-0 transition-opacity text-primary-500 group-hover:opacity-100"
        }
        onClick={() => {
          setFullscreenCard(isFullscreen ? null : cardName);
        }}
        Icon={isFullscreen ? Shrink : Expand}
      >
        {isFullscreen ? "Exit fullscreen" : null}
      </Button>
      {showSeriesSelect && (
        <SeriesSelect
          contentClassName="w-32"
          buttonClassName={
            isFullscreen
              ? undefined
              : "opacity-0 group-hover:opacity-100 focus-within:opacity-100 transition-opacity"
          }
          selectedSeries={selectedSeries}
          seriesNames={availableSeries}
          onChange={onSelectionChange ?? (() => {})}
          metricToDisplayName={metricToDisplayName}
        />
      )}
    </>
  );
};
