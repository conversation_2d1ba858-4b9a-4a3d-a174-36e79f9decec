import { HeaderNavLink } from "#/ui/layout/header";
import { getProjectHref, HeaderMenu } from "#/ui/layout/header-menu";
import { useEffect, useMemo, useState } from "react";
import { useOrg } from "#/utils/user";
import { decodeURIComponentPatched } from "#/utils/url";
import { useParams, usePathname } from "next/navigation";
import {
  Activity,
  Asterisk,
  Beaker,
  Bolt,
  ChartNoAxesColumn,
  ChevronRight,
  Clock,
  Database,
  Home,
  MessageCircle,
  MoreHorizontal,
  PanelLeft,
  Percent,
  Route,
  Settings2,
  Shapes,
  SlidersHorizontal,
} from "lucide-react";
import { Button, buttonVariants } from "#/ui/button";
import { getOrgLink, getOrgSettingsLink } from "./getOrgLink";
import { useAtom } from "jotai";
import { cn } from "#/utils/classnames";
import { getProjectConfigurationLink } from "./p/[project]/getProjectLink";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { useHotkeys } from "react-hotkeys-hook";
import { sidenavStateAtom, useToggleSidenavState } from "./sidenav-state";
import Link from "next/link";
import { getPlaygroundsLink } from "./prompt/[prompt]/getPromptLink";

import { getExperimentsLink } from "./p/[project]/experiments/[experiment]/getExperimentLink";
import { getDatasetsLink } from "./p/[project]/datasets/[dataset]/getDatasetLink";
import { getProjectLink } from "./p/[project]/getProjectLink";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  isBTQLSandboxPage,
  isLogsPage,
  isMonitorPage,
  isPlaygroundPage,
  useActivePage,
} from "./pathname-checker";
import { isDatasetPage } from "./pathname-checker";
import { isExperimentPage } from "./pathname-checker";
import { type getProjectSummary } from "./org-actions";
import { useQueryFunc } from "#/utils/react-query";
import { getProjectLogsLink } from "./p/[project]/logs/getProjectLogsLink";
import {
  BasicTooltip,
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from "#/ui/tooltip";
import { renderHotkey } from "#/utils/hotkeys";
import { useOrgPlan } from "./settings/billing/plans";
import { FreePlanUsageChart } from "./free-plan-usage-chart";

const isPageWithWiderMinimumWidth = (pathname: string) => {
  return Boolean(
    isExperimentPage(pathname) ||
      isPlaygroundPage(pathname) ||
      isDatasetPage(pathname) ||
      isLogsPage(pathname),
  );
};

export const Sidenav = () => {
  const org = useOrg();
  const orgPlan = useOrgPlan();
  const pathname = usePathname();
  const params = useParams<{ project: string; org?: string }>();
  const projectName = decodeURIComponentPatched(params?.project ?? "");
  const orgName = decodeURIComponentPatched(params?.org ?? "");

  const active = useActivePage();

  const [sidenavState, setSidenavState] = useAtom(sidenavStateAtom);
  const [isHovering, setIsHovering] = useState(false);

  const widerMinimumWidth = isPageWithWiderMinimumWidth(pathname ?? "");

  const [didUserCollapseSidenav, setDidUserCollapseSidenav] = useEntityStorage({
    entityType: "app",
    entityIdentifier: "app",
    key: "isSidenavCollapsed",
  });

  const [recentProjectIds, setRecentProjectIds] = useEntityStorage({
    entityType: "org",
    entityIdentifier: orgName,
    key: "recentProjectIds",
  });

  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: {
      org_name: orgName,
    },
  });

  const selectedOrLastUsedProject = useMemo(() => {
    if (!projects) return;
    const selectedProject = projects.find(
      (p) => p.project_name === projectName,
    );
    if (selectedProject) {
      return selectedProject;
    }

    // Try to find the most recent project that still exists
    if (recentProjectIds?.length) {
      for (const projectId of recentProjectIds) {
        const recentProject = projects.find((p) => p.project_id === projectId);
        if (recentProject) {
          return recentProject;
        }
      }
    }

    return projects[0];
  }, [projectName, projects, recentProjectIds]);

  useEffect(() => {
    if (!selectedOrLastUsedProject) {
      return;
    }

    // Update recent project IDs
    setRecentProjectIds((prev) => {
      const newList = [
        selectedOrLastUsedProject.project_id,
        ...(prev ?? []).filter(
          (id) => id !== selectedOrLastUsedProject.project_id,
        ),
      ].slice(0, 5); // Keep only the 5 most recent projects
      return newList;
    });
  }, [selectedOrLastUsedProject, setRecentProjectIds]);

  const closeIfFloating = () => {
    if (sidenavState !== "floating-open") return;
    setSidenavState("floating-closed");
  };

  const handleHoverEnter = () => {
    if (sidenavState === "collapsed" || sidenavState === "floating-closed") {
      setIsHovering(true);
    }
  };

  const handleHoverLeave = () => {
    setIsHovering(false);
  };

  // Determine the effective state (considering hover)
  const effectiveSidenavState =
    isHovering &&
    (sidenavState === "collapsed" || sidenavState === "floating-closed")
      ? "floating-open"
      : sidenavState;

  const toggleSidenavState = useToggleSidenavState();

  useHotkeys("[", toggleSidenavState, { description: "Toggle sidenav" });

  useHotkeys(
    "Escape",
    () => {
      setSidenavState("floating-closed");
    },
    {
      enabled: sidenavState === "floating-open",
    },
  );

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < (widerMinimumWidth ? 1440 : 1200)) {
        setSidenavState((s) =>
          s === "floating-open" ? "floating-open" : "floating-closed",
        );
      } else if (didUserCollapseSidenav) {
        setSidenavState("collapsed");
      } else {
        setSidenavState("docked");
      }
    };

    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [setSidenavState, widerMinimumWidth, didUserCollapseSidenav]);

  const projectLink = getProjectLink({
    orgName,
    projectName: selectedOrLastUsedProject?.project_name ?? "",
  });

  const { flags, isLoading: isLoadingFlags } = useFeatureFlags();

  const recentProjects = useMemo(() => {
    return recentProjectIds
      ?.filter(
        (projectId) =>
          !!projects?.find((p) => p.project_id === projectId) &&
          projectId !== selectedOrLastUsedProject?.project_id,
      )
      .slice(0, 3);
  }, [recentProjectIds, projects, selectedOrLastUsedProject]);

  if (!org.id) {
    return null;
  }

  const isFreePlan = orgPlan === "free";

  return (
    <>
      <div
        onClick={() => {
          // Only close if it's actually open, not just hovering
          if (sidenavState === "floating-open") {
            setSidenavState("floating-closed");
          } else if (isHovering) {
            // If we're just hovering, clear the hover state instead
            setIsHovering(false);
          }
        }}
        className={cn(
          "fixed inset-0 z-40 pointer-events-none bg-transparent transition-colors",
          {
            "bg-black/20 pointer-events-auto":
              effectiveSidenavState === "floating-open",
          },
        )}
      />
      {(sidenavState === "collapsed" || sidenavState === "floating-closed") && (
        <div
          className="fixed left-0 top-0 z-50 h-screen w-3 bg-transparent"
          onMouseEnter={handleHoverEnter}
          onMouseLeave={handleHoverLeave}
        />
      )}
      <div
        className={cn(
          "flex h-screen w-full max-w-56 flex-none flex-col bg-primary-50 transition-all opacity-100 group/sidenav",
          {
            "sticky top-0": effectiveSidenavState === "docked",
            "fixed top-0 z-50": effectiveSidenavState !== "docked",
            "-translate-x-full max-w-0 opacity-0 pointer-events-none scale-90":
              effectiveSidenavState === "collapsed" ||
              effectiveSidenavState === "floating-closed",
            "shadow-lg border-r-0 dark:border-r border-primary-100":
              effectiveSidenavState === "floating-open",
          },
        )}
        onMouseEnter={handleHoverEnter}
        onMouseLeave={handleHoverLeave}
      >
        <div className="flex h-11 flex-none items-center px-2">
          <div className="flex-1 truncate">
            {org.name && org.id && (
              <HeaderMenu type="orgs" orgName={org.name} orgId={org.id} />
            )}
          </div>
          <BasicTooltip tooltipContent="Settings">
            <Link
              href={getOrgSettingsLink({ orgName: org.name })}
              onClick={closeIfFloating}
              className={cn(
                buttonVariants({ variant: "ghost", size: "icon" }),
                "flex-none px-1 text-primary-400 size-7",
              )}
            >
              <SlidersHorizontal className="size-4" />
            </Link>
          </BasicTooltip>

          {effectiveSidenavState !== "floating-closed" &&
            effectiveSidenavState !== "floating-open" && (
              <BasicTooltip
                tooltipContent={
                  <>
                    Toggle navigation
                    <span className="ml-2.5 inline-block opacity-50">
                      {renderHotkey("[")}
                    </span>
                  </>
                }
              >
                <Button
                  size="xs"
                  variant="ghost"
                  className="flex-none px-1 text-primary-400"
                  onClick={() => {
                    setSidenavState("collapsed");
                    setDidUserCollapseSidenav(true);
                  }}
                >
                  <PanelLeft className="size-4" />
                </Button>
              </BasicTooltip>
            )}
        </div>
        <div
          className={cn("flex flex-1 relative overflow-hidden", {
            invisible: !selectedOrLastUsedProject,
          })}
        >
          <div className="absolute inset-x-0 top-0 z-10 h-3 bg-gradient-to-b from-primary-50 to-transparent" />
          <div className="absolute inset-x-0 bottom-0 z-10 h-3 bg-gradient-to-t from-primary-50 to-transparent" />
          <div className="no-scrollbar flex flex-1 flex-col gap-0.5 overflow-auto p-2">
            <div className="flex items-center justify-between px-2 text-xs text-primary-500">
              <span>Project</span>
              <Link
                href={getOrgLink({ orgName: org.name })}
                onClick={closeIfFloating}
                className="opacity-0 transition-opacity hover:text-primary-800 group-hover/sidenav:opacity-100"
              >
                View all
              </Link>
            </div>
            <div className="mb-1 flex-none truncate">
              {org.name && org.id && (
                <HeaderMenu
                  type="projects"
                  orgName={org.name}
                  orgId={org.id}
                  selectedProject={selectedOrLastUsedProject}
                  fullWidth
                />
              )}
            </div>
            <div className="flex flex-col gap-0.5">
              <HeaderNavLink
                href={getProjectLink({
                  orgName,
                  projectName: selectedOrLastUsedProject?.project_name ?? "",
                })}
                isActive={active === "project"}
                Icon={Home}
                onClick={closeIfFloating}
              >
                Overview
              </HeaderNavLink>
              <HeaderNavLink
                href={getProjectLogsLink({
                  orgName,
                  projectName: selectedOrLastUsedProject?.project_name ?? "",
                })}
                isActive={active === "logs"}
                Icon={Activity}
                onClick={closeIfFloating}
              >
                Logs
              </HeaderNavLink>
              <HeaderNavLink
                href={`${getOrgLink({ orgName: org.name })}/monitor?projectId=${selectedOrLastUsedProject?.project_id}`}
                isActive={!!isMonitorPage(pathname ?? "")}
                Icon={ChartNoAxesColumn}
                onClick={closeIfFloating}
              >
                Monitor
              </HeaderNavLink>
              <HeaderNavLink
                href={getPlaygroundsLink({
                  orgName,
                  projectName: selectedOrLastUsedProject?.project_name ?? "",
                })}
                isActive={active === "playgrounds"}
                Icon={Shapes}
                onClick={closeIfFloating}
              >
                Playgrounds
              </HeaderNavLink>
              <HeaderNavLink
                href={getExperimentsLink({
                  orgName,
                  projectName: selectedOrLastUsedProject?.project_name ?? "",
                })}
                isActive={active === "experiments"}
                Icon={Beaker}
                onClick={closeIfFloating}
              >
                Experiments
              </HeaderNavLink>
              <HeaderNavLink
                href={getDatasetsLink({
                  orgName,
                  projectName: selectedOrLastUsedProject?.project_name ?? "",
                })}
                isActive={active === "datasets"}
                Icon={Database}
                onClick={closeIfFloating}
              >
                Datasets
              </HeaderNavLink>

              <HeaderNavLink
                href={`${projectLink}/prompts`}
                isActive={active === "prompts"}
                Icon={MessageCircle}
                onClick={closeIfFloating}
              >
                Prompts
              </HeaderNavLink>

              <HeaderNavLink
                href={`${projectLink}/scorers`}
                isActive={active === "scorers"}
                Icon={Percent}
                onClick={closeIfFloating}
              >
                Scorers
              </HeaderNavLink>

              <Tooltip delayDuration={10}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="xs"
                    className={cn(
                      buttonVariants({
                        variant:
                          active === "tools" || active === "agents"
                            ? "default"
                            : "ghost",
                        size: "sm",
                      }),
                      "px-2 w-full text-left justify-start group text-[13px] h-7 gap-2.5",
                      {
                        "text-primary-700 hover:text-primary-950 hover:bg-primary-100":
                          !(active === "tools" || active === "agents"),
                        "bg-primary-200":
                          active === "tools" || active === "agents",
                      },
                    )}
                  >
                    <MoreHorizontal className="size-4 flex-none text-primary-900" />
                    <span className="flex-1 truncate">More</span>
                    <ChevronRight className="size-3 opacity-0 transition-opacity text-primary-400 group-hover/sidenav:opacity-100" />
                  </Button>
                </TooltipTrigger>
                <TooltipPortal>
                  <TooltipContent
                    hideWhenDetached
                    className="flex w-44 flex-col gap-0.5 p-1"
                    side="right"
                    align="start"
                  >
                    <HeaderNavLink
                      href={`${projectLink}/tools`}
                      isActive={active === "tools"}
                      Icon={Bolt}
                      onClick={closeIfFloating}
                    >
                      Tools
                    </HeaderNavLink>

                    {!isLoadingFlags && flags.agents && (
                      <HeaderNavLink
                        href={`${projectLink}/agents`}
                        isActive={active === "agents"}
                        Icon={Route}
                        onClick={closeIfFloating}
                      >
                        Agents
                      </HeaderNavLink>
                    )}
                  </TooltipContent>
                </TooltipPortal>
              </Tooltip>

              <HeaderNavLink
                href={getProjectConfigurationLink({
                  orgName,
                  projectName: selectedOrLastUsedProject?.project_name ?? "",
                })}
                isActive={active === "configuration"}
                Icon={Settings2}
                onClick={closeIfFloating}
              >
                Configuration
              </HeaderNavLink>
            </div>

            {recentProjects?.length > 0 && (
              <>
                <div className="px-2 pb-1 pt-5 text-xs text-primary-500">
                  Recent projects
                </div>
                {recentProjects.map((projectId) => {
                  const project = projects?.find(
                    (p) => p.project_id === projectId,
                  );
                  if (!project) return null;
                  return (
                    <HeaderNavLink
                      key={projectId}
                      href={getProjectHref({
                        orgName,
                        projectName: project.project_name,
                        pathname,
                      })}
                      isActive={false}
                      Icon={Clock}
                      iconClassName="px-px text-primary-500"
                      className="text-xs text-primary-500"
                      onClick={closeIfFloating}
                    >
                      <span className="flex-1 truncate">
                        {project.project_name}
                      </span>
                    </HeaderNavLink>
                  );
                })}
              </>
            )}
          </div>
        </div>
        {isFreePlan && <FreePlanUsageChart orgId={org.id} />}

        <div className="flex p-2">
          <HeaderNavLink
            href={`${getOrgLink({ orgName: org.name })}/btql`}
            isActive={!!isBTQLSandboxPage(pathname ?? "")}
            Icon={Asterisk}
            onClick={closeIfFloating}
            className="w-full"
          >
            BTQL sandbox
          </HeaderNavLink>
        </div>
      </div>
    </>
  );
};
