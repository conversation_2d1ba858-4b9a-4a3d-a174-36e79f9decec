"use server";

import { HTTPError, type AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  type ServiceToken,
  serviceTokenSchema,
} from "@braintrust/core/typespecs";
import {
  deleteObjects,
  extractSingularRow,
} from "#/pages/api/_object_crud_util";
import { makeOwnerApiKeysFullResultSetQuery } from "#/pages/api/apikey/_util";
import { transformServiceToken } from "#/pages/api/service_token/_util";
import { checkIsOrgOwnerQuery } from "#/pages/api/_special_queries";

export async function deleteServiceTokenAsOrgOwner(
  { service_token_id, org_id }: { service_token_id: string; org_id: string },
  authLookupRaw?: AuthLookup,
): Promise<null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const { query: isOrgOwnerQuery, queryParams: isOrgOwnerQueryParams } =
    checkIsOrgOwnerQuery({
      userId: authLookup.user_id,
      orgId: org_id,
    });
  const supabase = getServiceRoleSupabase();
  const isOrgOwner = extractSingularRow({
    rows: (await supabase.query(isOrgOwnerQuery, isOrgOwnerQueryParams.params))
      .rows,
    notFoundErrorMessage: "Missing organization owner permissions",
  });
  if (!isOrgOwner.exists) {
    throw new HTTPError(401, "Missing organization owner permissions");
  }

  const { query, queryParams, notFoundErrorMessage } =
    makeOwnerApiKeysFullResultSetQuery({
      authLookup,
      org_id,
      id: service_token_id,
      user_type: "service_account",
    });

  await deleteObjects({
    fullResultsQueryOverride: query,
    baseTableOverride: "api_key",
    startingParams: queryParams,
    fullResultsSize: 1,
    notFoundErrorMessage,
  });
  return null;
}

export async function fetchServiceTokensAsOrgOwner(
  { org_id }: { org_id: string },
  authLookupRaw?: AuthLookup,
): Promise<ServiceToken[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const { query: isOrgOwnerQuery, queryParams: isOrgOwnerQueryParams } =
    checkIsOrgOwnerQuery({
      userId: authLookup.user_id,
      orgId: org_id,
    });
  const supabase = getServiceRoleSupabase();
  const isOrgOwner = extractSingularRow({
    rows: (await supabase.query(isOrgOwnerQuery, isOrgOwnerQueryParams.params))
      .rows,
    notFoundErrorMessage: "Missing organization owner permissions",
  });
  if (!isOrgOwner.exists) {
    throw new HTTPError(401, "Missing organization owner permissions");
  }

  const { query, queryParams } = makeOwnerApiKeysFullResultSetQuery({
    authLookup,
    org_id,
    user_type: "service_account",
  });

  const { rows } = await supabase.query(query, queryParams.params);
  return serviceTokenSchema.array().parse(rows.map(transformServiceToken));
}
