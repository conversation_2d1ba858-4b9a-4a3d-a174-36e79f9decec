import { Button } from "#/ui/button";
import { DropdownMenuItem } from "#/ui/dropdown-menu";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Blend } from "lucide-react";
import {
  type ContextObject,
  useGlobalChat,
} from "#/ui/optimization/use-global-chat-context";
import { type TableSelection } from "#/ui/table/useTableSelection";
import { BasicTooltip } from "#/ui/tooltip";
import { useHotkeys } from "react-hotkeys-hook";
import { useRef, useState } from "react";

const LoopLogsButton = ({
  selectionProps,
}: {
  selectionProps: TableSelection;
}) => {
  const { setCurrentSessionContextObjects, setIsChatOpen, handleSendMessage } =
    useGlobalChat();

  const buttonRef = useRef<HTMLButtonElement>(null);
  const [open, setOpen] = useState(false);
  useHotkeys(
    "l",
    (e) => {
      e.preventDefault();
      setOpen((prev) => !prev);
    },
    {
      description: "Loop shortcuts",
    },
  );

  const { getSelectedRowsWithData } = selectionProps;

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <BasicTooltip
        tooltipContent={
          <>
            Loop shortcuts
            <span className="ml-2.5 inline-block opacity-50">L</span>
          </>
        }
        side="bottom"
      >
        <DropdownMenuTrigger asChild ref={buttonRef}>
          <Button size="xs" variant="border" Icon={Blend} />
        </DropdownMenuTrigger>
      </BasicTooltip>

      <DropdownMenuContent align="start">
        <DropdownMenuItem
          onSelect={() => {
            const rows = getSelectedRowsWithData();
            const traceContextObjects: Record<string, ContextObject> =
              Object.fromEntries(
                rows.map((row) => [
                  String(row.id),
                  {
                    id: String(row.id),
                    name: row.input,
                    resource: "trace",
                    expected: row.expected,
                    input: row.input,
                    metadata: row.metadata,
                    origin: row.origin,
                    output: row.output,
                    scores: row.scores,
                    tags: row.tags,
                    created: row.created,
                    error: row.error,
                    metrics: row.metrics,
                  },
                ]),
              );

            setCurrentSessionContextObjects((prev) => ({
              ...prev,
              ...traceContextObjects,
            }));
            setTimeout(() => {
              setIsChatOpen(true);
            }, 250);
          }}
        >
          Add to Loop chat
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => {
            const rows = getSelectedRowsWithData();
            const traceContextObjects: Record<string, ContextObject> =
              Object.fromEntries(
                rows.map((row) => [
                  String(row.id),
                  {
                    id: String(row.id),
                    name: row.input,
                    resource: "trace",
                    expected: row.expected,
                    input: row.input,
                    metadata: row.metadata,
                    origin: row.origin,
                    output: row.output,
                    scores: row.scores,
                    tags: row.tags,
                    created: row.created,
                    error: row.error,
                    metrics: row.metrics,
                  },
                ]),
              );

            handleSendMessage(
              {
                id: crypto.randomUUID(),
                type: "user_message",
                message: `Find other traces that are similar to the tagged traces.`,
                contextObjects: traceContextObjects,
              },
              {
                clearContextObjects: false,
                clearUserMessage: false,
              },
            );
            setTimeout(() => {
              setIsChatOpen(true);
            }, 250);
          }}
        >
          Find similar traces
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LoopLogsButton;
