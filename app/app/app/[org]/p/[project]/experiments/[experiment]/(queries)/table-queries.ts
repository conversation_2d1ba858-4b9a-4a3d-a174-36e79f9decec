import { use<PERSON><PERSON>back, useContext, useMemo } from "react";

import {
  compileSort,
  constructFullSummaryScanAbbrev,
  doubleQuote,
  ident,
} from "@braintrust/local/query";
import { useQuery } from "@tanstack/react-query";
import {
  buildDefaultOrderBy,
  type SortComparison,
  type Search,
} from "#/utils/search/search";
import {
  BT_GROUP_BY_METADATA,
  BT_GROUP_KEY,
  BT_IS_GROUP,
  groupExprFn,
  nullValueExpr,
} from "#/ui/table/grouping/queries";
import { computeDisplayPaths } from "#/utils/display-paths";
import { buildDiffStruct, wrapStruct } from "../diffstruct";
import {
  type ComparisonExperimentSpanSummary,
  type PrimaryExperimentSpanSummary,
} from "./useExperiment";
import { type DiffModeState } from "#/ui/query-parameters";
import { DataType } from "apache-arrow";
import { dbQuery, useDuckDB } from "#/utils/duckdb";
import { type ChartBrushFilter } from "../DistributionChartInsight";
import { toast } from "sonner";
import { parseObjectJSON } from "#/utils/schema";
import { GROUP_BY_INPUT_VALUE } from "#/ui/table/grouping/controls";
import { fieldNamesForExperiment, outputVsExpectedExpr } from "./diffQueries";
import { ProjectContext } from "../../../projectContext";
import type * as duckdb from "@duckdb/duckdb-wasm";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { singleQuote } from "@braintrust/local/query";
import { fetchBtqlBinaryParquet } from "#/utils/btql/btql";
import { type ParsedQuery } from "@braintrust/btql/parser";
import { useOrg } from "#/utils/user";
import { useBtqlFlags } from "#/lib/feature-flags";
import { useSessionToken } from "#/utils/auth/session-token";
import { filterDataByColumnVisibility } from "#/utils/data-object";

export type GetRowsForExportFn = (args?: {
  withComparisons?: boolean;
  objectType?: DataObjectType;
  rowIds?: string[];
  refetchDataQuery?: ParsedQuery | undefined;
  columnVisibility?: Record<string, boolean>;
  allColumns?: boolean;
}) => Promise<{ [k: string]: unknown }[] | undefined>;

export function useTableQueries({
  experimentScanRaw,
  experiment,
  comparisonExperimentData,
  search,
  projectedPaths,
  diffMode,
  chartBrushFilters,
  tableGrouping,
  comparisonKey,
  comparisonKeyFilterClause,
  defaultSortExprs,
  layout,
  isPlayground,
}: {
  experimentScanRaw: string;
  experiment: PrimaryExperimentSpanSummary | undefined;
  comparisonExperimentData: ComparisonExperimentSpanSummary[];
  search: Search;
  projectedPaths: string[];
  diffMode: DiffModeState | null;
  chartBrushFilters?: ChartBrushFilter[];
  tableGrouping: string;
  comparisonKey: (relation: string) => string;
  comparisonKeyFilterClause: (relation: string) => string;
  defaultSortExprs?: string[];
  layout: string;
  isPlayground?: boolean;
}) {
  const projectContext = useContext(ProjectContext);
  const { config } = projectContext;
  const gridLayout = layout === "grid";
  const org = useOrg();
  const btqlFlags = useBtqlFlags();
  const { getOrRefreshToken } = useSessionToken();

  const sortableScoreFields = useCallback(
    (scoreFields: string[]) => {
      const humanReviewFields = new Set(
        config?.scores.map((s) => s.name) ?? [],
      );
      return scoreFields.filter((n) => !humanReviewFields.has(n)) ?? [];
    },
    [config?.scores],
  );
  const primarySortableScoreFields = useMemo(
    () => sortableScoreFields(experiment?.scoreFields ?? []),
    [experiment?.scoreFields, sortableScoreFields],
  );

  const sortPaths = useMemo(
    () =>
      search.sort?.flatMap((s) => {
        if (!s.spec?.path) {
          return [];
        }
        if (s.comparison) {
          const comparison = comparisonExperimentData.find(
            (e) => e.id === s.comparison?.experimentId,
          );
          if (
            !comparison ||
            !validScorePath(s.spec.path.path, comparison.scoreFields)
          ) {
            return [];
          }
        } else if (
          !validScorePath(s.spec.path.path, experiment?.scoreFields ?? [])
        ) {
          return [];
        }
        return [
          {
            ...s.spec.path,
            comparison: s.comparison,
          },
        ];
      }) ?? [],
    [search.sort, experiment?.scoreFields, comparisonExperimentData],
  );

  const sortPathsExprsFn = useCallback(
    (
      defaultExprs: string[],
      opts?: { relation?: string; ignoreComparisons?: boolean },
    ) => {
      const relationArr = opts?.relation ? [opts.relation] : [];
      return (
        [
          ...(sortPaths.length > 0
            ? sortPaths.map(({ path, desc, comparison }) => {
                const comparisonIndex = comparisonExperimentData.findIndex(
                  (e) => e.id === comparison?.experimentId,
                );
                const prefix = opts?.ignoreComparisons
                  ? []
                  : comparisonIndex >= 0
                    ? [...relationArr, `e${comparisonIndex + 2}`, "data"]
                    : [...relationArr, "e1"];
                return `${compileSort({ path: [...prefix, ...path], desc })} NULLS LAST`;
              })
            : defaultExprs),
          // Always add the base order by to the end of the sort, so that if the
          // sort order changes (e.g. when we add the "_log" to the query it may jumble
          // up some records if there are ties), the table stays stable
          buildDefaultOrderBy(
            {},
            undefined,
            [...relationArr, ...(opts?.ignoreComparisons ? [] : ["e1"])].join(
              ".",
            ),
          ),
        ].join(", ") || "true"
      );
    },
    [sortPaths, comparisonExperimentData],
  );

  const { displayPaths, comparisonPaths } = useMemo(() => {
    // NOTE: We should probably exclude "id" (and in general take into account which fields are shown vs hidden)
    const displayPaths = computeDisplayPaths(
      experiment?.spanSummarySchema || null,
      projectedPaths,
      comparisonExperimentData.flatMap(({ spanSummarySchema }) =>
        spanSummarySchema ? [spanSummarySchema] : [],
      ),
    );
    const diffStruct = buildDiffStruct({
      experimentsFields: [
        experiment?.spanSummarySchema?.fields.filter((f) =>
          projectedPaths.includes(f.name),
        ) ?? [],
        ...comparisonExperimentData.map(({ spanSummarySchema }) =>
          spanSummarySchema!.fields.filter((f) =>
            projectedPaths.includes(f.name),
          ),
        ),
      ],
      names: [
        "e1",
        ...comparisonExperimentData.map((_, i) => `e${i + 2}."data"`),
      ],
      parentPath: [],
      displayPaths,
      allKeysOrdered: projectedPaths,
    });
    return { displayPaths, comparisonPaths: diffStruct };
  }, [experiment?.spanSummarySchema, comparisonExperimentData, projectedPaths]);

  // Allow to switch to comparison mode only if there is expected and output fields
  const expectedVsOutputDiff = useMemo(() => {
    if (!experiment?.spanSummarySchema) {
      return null;
    }

    const expectedField = experiment.spanSummarySchema.fields.find(
      (f) => f.name === "expected",
    );
    const outputField = experiment.spanSummarySchema.fields.find(
      (f) => f.name === "output",
    );

    if (!expectedField || !outputField) {
      return null;
    }

    if (
      DataType.isStruct(expectedField) &&
      DataType.isStruct(outputField) &&
      displayPaths?.children["expected"]?.type === "node"
    ) {
      const diffStruct = buildDiffStruct({
        experimentsFields: [
          expectedField.type.children,
          outputField.type.children,
        ],
        names: ["expected", "output"],
        parentPath: [],
        displayPaths: displayPaths.children["expected"],
      });
      return wrapStruct(diffStruct);
    } else {
      return outputVsExpectedExpr(
        expectedField.name,
        outputField.name,
        "base.e1",
      );
    }
  }, [displayPaths?.children, experiment?.spanSummarySchema]);

  const fieldNames = useMemo(() => {
    if (diffMode?.enabled && diffMode.enabledValue === "expected_output") {
      return projectedPaths
        .filter((p) => p !== "expected")
        .map((p) =>
          p === "output" && expectedVsOutputDiff !== null
            ? `${expectedVsOutputDiff} AS "output_vs_expected"`
            : p,
        );
    }
    return projectedPaths;
  }, [diffMode, projectedPaths, expectedVsOutputDiff]);

  const validChartBrushFilters = useMemo(() => {
    return chartBrushFilters?.filter((f) => !!f.scoreBounds) ?? [];
  }, [chartBrushFilters]);

  const tableGroupingField = useMemo(() => {
    try {
      JSON.parse(tableGrouping);
      if (fieldNames.includes(BT_GROUP_BY_METADATA)) {
        return BT_GROUP_BY_METADATA;
      }
      return tableGrouping;
    } catch {}
    return tableGrouping;
  }, [tableGrouping, fieldNames]);

  const baseQueryFn = useCallback(
    ({
      tableScan,
      scoreFields,
      relation,
      excludeFieldNames,
    }: {
      tableScan?: string | null;
      scoreFields?: string[];
      relation: "base.e1" | "base";
      excludeFieldNames?: string[];
    }) =>
      scoreFields && tableScan
        ? `SELECT
      ${groupExprFn(tableGroupingField, relation, "")}
      ${fieldNames
        .reduce<string[]>((acc, fieldName) => {
          if (!excludeFieldNames || !excludeFieldNames.includes(fieldName)) {
            acc.push(
              fieldName.includes("output_vs_expected")
                ? fieldName
                : `${relation}.${fieldName}`,
            );
          }
          return acc;
        }, [])
        .join(", ")}
    FROM (${tableScan}) base
    ${comparisonKeyFilterClause(relation)}
    WHERE true
    ${
      validChartBrushFilters.length > 0
        ? validChartBrushFilters
            .map(
              (chartBrushFilter) =>
                ` AND ${relation}.scores.${doubleQuote(
                  chartBrushFilter.scoreName,
                )} BETWEEN ${chartBrushFilter.scoreBounds!.join(" AND ")}`,
            )
            .join("")
        : ""
    }
    ORDER BY ${sortPathsExprsFn(
      sortableScoreFields(scoreFields).map(
        (n) => `${relation}.scores.${doubleQuote(n)} ASC NULLS LAST`,
      ),
      { relation: "base", ignoreComparisons: relation === "base" },
    )}`
        : null,
    [
      fieldNames,
      tableGroupingField,
      comparisonKeyFilterClause,
      validChartBrushFilters,
      sortPathsExprsFn,
      sortableScoreFields,
    ],
  );

  // The comparison key is guaranteed to be a non-empty string, so it's safe to
  // use `=` instead of `IS DISTINCT FROM`
  const comparisonJoin = useCallback(
    (relation: string = "e2") =>
      `${comparisonKey("e1")}=${comparisonKey(relation)}`,
    [comparisonKey],
  );
  const aliases = comparisonExperimentData.map((_, i) => `e${i + 2}`);
  const baseComparisonQuery = experiment?.tableScan
    ? `WITH base as (
      SELECT * from (${experiment.tableScan})
    )
    SELECT e1, e1_counts, ${aliases.join(",")}
      FROM
       (
        SELECT e1,
        (SELECT struct_pack(count := COUNT(1)) as exp from base WHERE ${comparisonJoin("base")}) AS e1_counts,
        ${comparisonExperimentData
          .map(
            (e, i) =>
              `(SELECT struct_pack(
                  ${
                    experiment?.hasTrials
                      ? `
                        -- for trials
                        "compareIds" := list(${aliases[i]}.id ORDER BY ${aliases[i]}.id),
                        "data" := first(${aliases[i]} ORDER BY ${aliases[i]}.id)`
                      : `
                        "compareIds" := [],
                        "data" := first(${aliases[i]})
                        `
                  }
                ) as exp
                FROM (${e.tableScan}) AS ${aliases[i]} WHERE ${comparisonJoin(aliases[i])}) ${aliases[i]}`,
          )
          .join(",")}
        FROM base AS e1
       )`
    : null;

  const _baseQuery = baseQueryFn({
    tableScan: baseComparisonQuery,
    scoreFields: experiment?.scoreFields,
    relation: "base.e1",
  });
  const compileDiffSort = useCallback(
    ({
      path,
      desc,
      comparison,
    }: {
      path: string[];
      desc?: boolean;
      comparison?: SortComparison | null;
    }) => {
      const comparisonIndex = comparisonExperimentData.findIndex(
        (e) => e.id === comparison?.experimentId,
      );
      if (path[0] === "scores") {
        if (!comparison) {
          return compileSort({
            path: ["e1"].concat(path),
            desc,
          });
        }
        if (comparisonIndex === -1) {
          return "TRUE";
        }
        const path1 = ["e1"].concat(path);
        const path2 = [`e${comparisonIndex + 2}`, "data"].concat(path);
        // for sorting by regression we want to reverse the order of the score difference
        const order =
          comparison?.type === "regression"
            ? desc
              ? "ASC"
              : "DESC"
            : desc
              ? "DESC"
              : "ASC";
        const scoreSortExpr = `${ident(path2)} ${order}`;
        if (comparison?.type === "value") {
          // Sort by the score, and tie break with the score difference
          return `${scoreSortExpr}, ${ident(path1)} - ${ident(path2)} ${order}`;
        }
        // Sort by the difference, and tie break with the value itself
        return `${ident(path1)} - ${ident(path2)} ${order}, ${scoreSortExpr}`;
      } else {
        return compileSort({
          path: [
            ...(comparisonIndex >= 0
              ? [`e${comparisonIndex + 2}`, "data"]
              : ["e1"]),
          ].concat(path),
          desc,
        });
      }
    },
    [comparisonExperimentData],
  );

  const distributionScoreQuery =
    validChartBrushFilters.length > 0
      ? validChartBrushFilters
          .map(
            (chartBrushFilter) => ` AND (
    ${[
      "e1",
      ...comparisonExperimentData.flatMap(({ scoreFields }, i) =>
        scoreFields.includes(chartBrushFilter.scoreName)
          ? [`e${i + 2}.data`]
          : [],
      ),
    ]
      .map(
        (alias) =>
          `${alias}.scores.${doubleQuote(
            chartBrushFilter.scoreName,
          )} BETWEEN ${chartBrushFilter.scoreBounds!.join(" AND ")}`,
      )
      .join(" OR ")}
  )`,
          )
          .join("")
      : "";

  const diffQuery =
    baseComparisonQuery && comparisonPaths && experiment?.scoreFields
      ? `SELECT
      ${groupExprFn(tableGroupingField, "e1", "")}
       ${comparisonPaths
         ?.map(({ name, value }) => `${value} AS ${doubleQuote(name)}`)
         .join(",\n")},
        struct_pack(
          ${[
            isPlayground
              ? `_meta := struct_pack(
                diffModeEnabled := ${!!diffMode?.enabled && diffMode.enabledValue === "between_experiments"},
                layoutType := 'list'
              )`
              : null,
            `e1 := struct_pack(rowCount := e1_counts."count")`,
            ...comparisonExperimentData.map(
              (_, i) =>
                `e${i + 2} := struct_pack(
                  compareIds := e${i + 2}."compareIds"
                )`,
            ),
          ]
            .filter(Boolean)
            .join(",\n")}
          ) AS __bt_internal
      FROM
       (${baseComparisonQuery})
      ${comparisonKeyFilterClause("e1")}
      WHERE true
      ${distributionScoreQuery}
      ${
        sortPaths.length > 0
          ? `ORDER BY ${sortPaths.map(({ path, desc, comparison }) => `${compileDiffSort({ path, desc, comparison })} NULLS LAST`).join(", ")}`
          : !search.sort && primarySortableScoreFields.length > 0
            ? `ORDER BY ${sortPathsExprsFn(
                defaultSortExprs ??
                  primarySortableScoreFields.map(
                    (n) => `e1.scores.${doubleQuote(n)} ASC NULLS LAST`,
                  ),
              )}`
            : ""
      }`
      : null;

  const newBaseQuery =
    baseComparisonQuery && projectedPaths && experiment?.scoreFields
      ? // TODO: construct the proper field names for non-overlapping fields when diff mode is on
        `SELECT
          ${groupExprFn(tableGroupingField, "e1", "")}
          ${fieldNamesForExperiment(fieldNames, "e1")},
          ${
            gridLayout
              ? `
          struct_pack(
            ${[
              `_meta := struct_pack(
                diffModeEnabled := ${!!diffMode?.enabled && diffMode.enabledValue === "between_experiments"},
                layoutType := 'grid'
              )`,
              `e1 := struct_pack(rowCount := e1_counts."count")`,
              ...comparisonExperimentData.map(
                // TODO: construct the proper field names for non-overlapping fields (e.g. comparison score names) when diff mode is on
                (_, i) => `
                  e${i + 2} := struct_pack(
                    compareIds := e${i + 2}."compareIds",
                    data := struct_pack(
                      ${fieldNamesForExperiment(fieldNames, `e${i + 2}."data"`, true)}
                    )
                  )`,
              ),
            ].join(",\n")}
          )`
              : "NULL"
          } AS __bt_internal
        FROM
         (${baseComparisonQuery})
        ${comparisonKeyFilterClause("e1")}
        WHERE true
        ORDER BY ${
          sortPaths.length > 0
            ? `${sortPaths.map(({ path, desc, comparison }) => `${compileDiffSort({ path, desc, comparison })} NULLS LAST`).join(", ")}`
            : sortPathsExprsFn(
                defaultSortExprs ??
                  primarySortableScoreFields.map(
                    (n) => `e1.scores.${doubleQuote(n)} ASC NULLS LAST`,
                  ),
              )
        }`
      : null;
  const baseQuery = gridLayout ? newBaseQuery : _baseQuery;

  const isGroupByInput = tableGroupingField === GROUP_BY_INPUT_VALUE;
  const duck = useDuckDB();
  const { data: groupQueryData, isLoading } = useQuery({
    queryKey: [
      [...(experiment?.queryKeys ?? [])],
      "groupQuery",
      tableGroupingField,
      tableGrouping,
      [
        fieldNames,
        _baseQuery,
        sortPathsExprsFn(["span_type_info ASC NULLS LAST"]),
        isGroupByInput,
      ],
    ],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const conn = await duck!.connect();
      const groupExpr = groupExprFn(tableGroupingField, "e1", "");
      if (!groupExpr) {
        return null;
      }

      const query =
        _baseQuery &&
        `WITH base AS (
        SELECT
          ${groupExpr}
          *,
        FROM (${_baseQuery}) e1
      ),
      grouped AS (
        SELECT
          ${BT_GROUP_KEY},
          ${[
            ...fieldNames.map((f: string) => {
              // output_vs_expected is a struct rather than just a field name, so use this heuristic
              if (f.includes("output_vs_expected")) {
                return nullValueExpr("output_vs_expected");
              }

              switch (f) {
                case "comparison_key":
                  // used for group-by-input
                  return "first(comparison_key) AS comparison_key";
                case "span_type_info":
                  // since we're rendering the group name in the span_type_info column, put the group value here
                  return `${isGroupByInput ? "first(input)" : `COALESCE(${BT_GROUP_KEY}, NULL)`} AS span_type_info`;
                case "scores":
                  // Scores aggregates are calculated via summary now, and we don't need to preserve the type information since
                  // subrow information is calculated in a separate query
                  return "NULL AS scores";
                case "metrics":
                  // Metric aggregates are calculated via summary now, and we don't need to preserve the type information since
                  // subrow information is calculated in a separate query
                  return "NULL AS metrics";
                case "created":
                  // for sorting
                  return `MAX(created) AS created`;
                default:
                  return nullValueExpr(f);
              }
            }),
          ].join(",\n")},
        FROM base
        GROUP BY ${BT_GROUP_KEY}
      )
      SELECT TRUE AS ${BT_IS_GROUP}, * EXCLUDE ("created") FROM grouped
      ORDER BY ${sortPathsExprsFn(["span_type_info ASC NULLS LAST"], { relation: "grouped", ignoreComparisons: true })}
    `;

      const result = await dbQuery(conn, signal, query);
      return { groupRows: result?.toArray(), tableGrouping };
    },
    staleTime: Infinity,
    enabled: !!duck && !!_baseQuery,
  });

  const getRowsForExport: GetRowsForExportFn = useCallback(
    async (args?: {
      withComparisons?: boolean;
      objectType?: DataObjectType;
      rowIds?: string[];
      refetchDataQuery?: string | ParsedQuery;
      columnVisibility?: Record<string, boolean>;
    }) => {
      async function queryRows() {
        const allExperiments = [experiment, ...comparisonExperimentData];
        const playgroundQueries = allExperiments
          .map((e) => {
            return baseQueryFn({
              tableScan: e?.tableScan,
              scoreFields: e?.scoreFields,
              relation: "base",
            });
          })
          .filter((v) => v != null);
        const { withComparisons, objectType, rowIds } = args ?? {};
        if (
          !duck ||
          !_baseQuery ||
          (withComparisons &&
            allExperiments.length !== playgroundQueries.length)
        ) {
          toast.error(`Failed to retrieve table rows`, {
            description: "Table not ready",
          });
          return;
        }

        if (withComparisons) {
          const rows = await Promise.all(
            playgroundQueries.map(async (query) => {
              return await queryData(duck, query, objectType, rowIds);
            }),
          );
          return rows.flat();
        }

        if (args?.refetchDataQuery) {
          const parquetFile = await fetchBtqlBinaryParquet({
            args: {
              query: args.refetchDataQuery,
              brainstoreRealtime: true,
              disableLimit: true,
            },
            btqlFlags,
            apiUrl: org.api_url,
            getOrRefreshToken,
            duck,
          });
          if (!parquetFile) {
            toast.error(`Failed to retrieve table rows`, {
              description: "Table not ready",
            });
            return;
          }

          const baseQuery = baseQueryFn({
            tableScan: constructFullSummaryScanAbbrev({
              summaryScan: `SELECT * FROM parquet_scan([${singleQuote(parquetFile)}])`,
              filters: experiment?.plainFilters,
              scoreNames: null,
              metricNames: null,
              integerMetrics: null,
            }),
            scoreFields: experiment?.scoreFields,
            relation: "base",
            excludeFieldNames: [BT_GROUP_BY_METADATA],
          });

          if (!baseQuery) {
            toast.error(`Failed to retrieve table rows`, {
              description: "Table not ready",
            });
            return;
          }
          const result = await queryData(duck, baseQuery, objectType, rowIds);
          duck.dropFile(parquetFile);
          return result;
        }

        return await queryData(duck, _baseQuery, objectType, rowIds);
      }
      const rows = await queryRows();
      if (!rows) {
        return;
      }
      const filteredData = filterDataByColumnVisibility({
        data: rows,
        layout,
        isPlayground,
        columnVisibility: args?.columnVisibility,
      });
      return filteredData;
    },
    [
      experiment,
      comparisonExperimentData,
      duck,
      _baseQuery,
      baseQueryFn,
      btqlFlags,
      layout,
      isPlayground,
      org.api_url,
      getOrRefreshToken,
    ],
  );

  const getRawData = useCallback(async () => {
    if (!duck || !experimentScanRaw) {
      return;
    }
    return queryData(duck, experimentScanRaw);
  }, [duck, experimentScanRaw]);

  return {
    displayPaths,
    baseQuery,
    diffQuery,
    baseComparisonQuery,
    groupQueryData,
    getRowsForExport,
    getRawData,
    isLoading,
  };
}

async function queryData(
  duck: duckdb.AsyncDuckDB,
  query: string,
  objectType: DataObjectType = "experiment",
  rowIds?: string[],
) {
  const conn = await duck.connect();
  const rows = await conn.query(
    rowIds
      ? `SELECT * FROM (${query}) WHERE id IN (${rowIds.map(singleQuote).join(",")})`
      : query,
  );
  const jsonRows = rows?.toArray()?.map((e) => e.toJSON()) ?? [];
  return jsonRows.map((r) => parseObjectJSON(objectType, r));
}

function validScorePath(path: string[], scoreFields: string[]) {
  return path[0] !== "scores" || scoreFields.includes(path[1]);
}
