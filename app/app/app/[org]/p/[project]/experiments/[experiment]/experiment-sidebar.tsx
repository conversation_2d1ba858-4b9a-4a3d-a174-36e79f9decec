import { useRouter } from "next/navigation";
import {
  type Dispatch,
  memo,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
  type RefObject,
} from "react";
import {
  useDiffModeState,
  useExperimentComparisonIds,
} from "#/ui/query-parameters";
import { BasicTooltip } from "#/ui/tooltip";
import { Button, buttonVariants } from "#/ui/button";
import {
  ArrowLeftToLine,
  ArrowUpDown,
  Database,
  GitBranch,
  MessageCircle,
  Plus,
  TagIcon,
  X,
} from "lucide-react";
import { EXPERIMENT_COMPARISON_COLOR_CLASSNAMES } from "#/ui/charts/colors";
import { Combobox } from "#/ui/combobox/combobox";
import { cn } from "#/utils/classnames";
import { getExperimentLink } from "./getExperimentLink";
import { useOrg } from "#/utils/user";
import { ProjectContext } from "../../projectContext";
import { type ExtendedExperiment } from "./experiment-actions";
import { type ExperimentItem } from "./ExperimentHeader";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import { smartTimeFormat } from "#/ui/date";
import { UpdateableDataTextEditor } from "#/ui/data-text-editor";
import { toast } from "sonner";
import { type SavingState } from "#/ui/saving";
import { Skeleton } from "#/ui/skeleton";
import Link from "next/link";
import { getDatasetLink } from "../../datasets/[dataset]/getDatasetLink";
import { useEntityStorage } from "#/lib/clientDataStorage";
import {
  BaselineObjectBadge,
  LatestObjectBadge,
} from "../../experiments-formatters";
import { useRecentExperiments } from "../clientpage";
import { GitMetaData } from "./GitMetaData";
import { DatasetMetadata } from "./DatasetMetadata";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import TextArea from "#/ui/text-area";
import { useExperimentPrompt } from "./(queries)/use-experiment-prompt";
import { ExperimentSidebarScorers } from "./experiment-sidebar-scorers";
import { usePlaygroundCopilotContext } from "#/ui/copilot/playground";
import { PromptPreviewTooltip } from "../../playgrounds/[playground]/prompt-preview-tooltip";
import { FunctionDialog } from "#/ui/prompts/function-editor/function-dialog";
import { type ChartBrushFilter } from "./DistributionChartInsight";
import {
  type PrimaryExperimentSpanSummary,
  type ComparisonExperimentSpanSummary,
} from "./(queries)/useExperiment";
import { type ErrorSummary } from "./(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { type UpdateValueFn } from "#/ui/arrow-table";
import { TagsCombobox, updateTagList } from "#/ui/trace/tags";
import { Tag, tagSortFn } from "#/ui/tag";
import { type UIFunction } from "#/ui/prompts/schema";

const ExperimentSidebarComponent = ({
  viewLayout,
  isExperimentPending,
  experiment,
  selectedComparisonExperiments,
  setComparisonExperiments,
  isReadOnly,
  scoreFields,
  setSavingState,
  baseComparisonQuery,
  onSetChartBrushFilter,
  distributionChartSignals,
  histogramRef,
  primaryExperiment,
  comparisonExperimentData,
  errorSummaryData,
}: {
  viewLayout: string;
  isExperimentPending: boolean;
  experiment: ExtendedExperiment;
  selectedComparisonExperiments: ExperimentItem[];
  setComparisonExperiments: (
    experiments: ExperimentItem[],
    initializing?: boolean,
  ) => void;
  isReadOnly?: boolean;
  scoreFields: string[];
  setSavingState: Dispatch<SetStateAction<SavingState>>;
  baseComparisonQuery: string | null;
  onSetChartBrushFilter: (filterData: ChartBrushFilter) => void;
  distributionChartSignals: number[];
  histogramRef: RefObject<{
    [scoreName: string]: { removeBrush: () => void };
  } | null>;
  primaryExperiment?: PrimaryExperimentSpanSummary;
  comparisonExperimentData: ComparisonExperimentSpanSummary[];
  errorSummaryData?: Record<string, ErrorSummary>;
}) => {
  const router = useRouter();
  const { name: orgName, id: orgId } = useOrg();
  const {
    projectName,
    projectId,
    experiments,
    orgDatasets,
    mutateExperiments,
    config: projectConfig,
  } = useContext(ProjectContext);
  const copilotContext = usePlaygroundCopilotContext();

  const [
    disableDefaultComparisonAutoSelect,
    setDisableDefaultComparisonAutoSelect,
  ] = useEntityStorage({
    entityType: "org",
    entityIdentifier: orgId ?? "",
    key: "disableDefaultComparisonAutoSelect",
  });

  const [openedPromptId, setOpenedPromptId] = useState<string | null>();
  const [currentTags, setCurrentTags] = useState<string[]>(
    experiment.tags ?? [],
  );
  const {
    prompt,
    status: promptLoadingStatus,
    createPrompt,
  } = useExperimentPrompt(experiment?.id);
  const isSavedPrompt = !!prompt?.name;

  const { latestExperimentId, baselineExperimentId } = useRecentExperiments();

  const [diffMode] = useDiffModeState();

  const flipBaseAndComparison = useCallback(() => {
    const comparisonExperimentName = selectedComparisonExperiments[0].name;
    const baseExperimentName = experiment.name;
    const params = new URLSearchParams({});

    const c = selectedComparisonExperiments
      .flatMap((e) =>
        e.name === comparisonExperimentName ? [baseExperimentName] : [e.name],
      )
      .join(",");
    params.set("c", c);
    if (diffMode?.enabled) {
      params.set("diff", diffMode.enabledValue);
    }

    router.push(
      getExperimentLink({
        orgName,
        projectName,
        experimentName: comparisonExperimentName,
      }) +
        "?" +
        params.toString(),
    );
  }, [
    experiment,
    selectedComparisonExperiments,
    orgName,
    projectName,
    router,
    diffMode,
  ]);

  const comparableExperiments = useMemo(
    () => experiment?.comparables ?? [],
    [experiment?.comparables],
  );

  const dataset = useMemo(
    () => orgDatasets?.find((e) => e.id === experiment.dataset_id),
    [orgDatasets, experiment.dataset_id],
  );

  const [compareToParam, setCompareToParam] = useExperimentComparisonIds();

  const [comparisonsLoaded, setComparisonsLoaded] = useState(false);
  useEffect(() => {
    if (comparisonsLoaded || !experiment.comparables) {
      return;
    }

    const comparableExpArray = comparableExperiments || [];
    const baseExp =
      experiment?.base_exp_id &&
      comparableExpArray.find((e) => e.id === experiment.base_exp_id);
    let comparisonExperiments: ExperimentItem[] = [];
    if (compareToParam) {
      // some users want to de-select the comparison experiments so
      // an empty array is a valid state that we will support as nothing selected
      if (compareToParam.length > 0) {
        const compareExperiments = compareToParam.flatMap((name) => {
          const matching = experiment.comparables.find((e) => e.name === name);
          return matching ? [matching] : [];
        });
        comparisonExperiments = compareExperiments;
        setCompareToParam(compareExperiments.map((e) => e.name));
      }
    } else if (!disableDefaultComparisonAutoSelect) {
      const baselineExperiment = baselineExperimentId
        ? comparableExpArray.find((e) => e.id === baselineExperimentId)
        : null;
      const comparisonExperiment =
        baseExp || baselineExperiment || comparableExpArray[0];
      if (comparisonExperiment) {
        comparisonExperiments = [comparisonExperiment];
        setCompareToParam([comparisonExperiment.name]);
      }
    }
    setComparisonExperiments(comparisonExperiments, true);
    setComparisonsLoaded(true);
  }, [
    experiment,
    comparableExperiments,
    compareToParam,
    setComparisonExperiments,
    setCompareToParam,
    comparisonsLoaded,
    baselineExperimentId,
    disableDefaultComparisonAutoSelect,
  ]);

  const updateExperiment = useCallback(
    async (
      field: "description" | "metadata" | "tags",
      value: string | Record<string, unknown> | string[],
    ) => {
      if (!experiment) return null;
      setSavingState("saving");

      const body: {
        id: string;
        [key: string]: unknown;
        shallow_replace_json_fields?: string[];
      } = { id: experiment.id, [field]: value };

      if (field === "metadata") {
        body.shallow_replace_json_fields = ["metadata"];
      }
      const resp = await fetch("/api/experiment/patch_id", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });
      if (!resp.ok) {
        const respText = await resp.text();
        toast.error("Failed to update experiment", { description: respText });
        setSavingState(new Error(respText));
      } else {
        setSavingState("saved");
        mutateExperiments();
      }

      setTimeout(() => setSavingState("none"), 5000);
      // returning "0" to effectively disable optimistic updates in UpdateableDataTextEditor
      return "0";
    },
    [setSavingState, mutateExperiments, experiment],
  );
  const updateMetadata: UpdateValueFn = useCallback(
    async (value) =>
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      updateExperiment("metadata", value as Record<string, unknown>),
    [updateExperiment],
  );

  const handleTagsChange = useCallback(
    (label: string) => {
      const isSelected = currentTags.includes(label);
      const newTags = updateTagList(currentTags, label, !isSelected);
      setCurrentTags(newTags);
      updateExperiment("tags", newTags);
    },
    [currentTags, updateExperiment],
  );

  const [_, setCollapsed] = useEntityStorage({
    entityType: "collapsibleSection",
    entityIdentifier: "experimentSidebar",
    key: "isCollapsed",
  });

  const distributionChartProps = useMemo(
    () => ({
      baseComparisonQuery,
      setFilter: onSetChartBrushFilter,
      primaryExperiment,
      comparisonExperimentData,
      signals: distributionChartSignals,
    }),
    [
      baseComparisonQuery,
      onSetChartBrushFilter,
      primaryExperiment,
      comparisonExperimentData,
      distributionChartSignals,
    ],
  );

  const sortedTags = useMemo(() => {
    return currentTags
      .map((tagName) => {
        const tagConfig = projectConfig?.tags?.find((t) => t.name === tagName);
        return {
          name: tagName,
          config: tagConfig,
        };
      })
      .sort((a, b) =>
        tagSortFn(a.config || { name: a.name }, b.config || { name: b.name }),
      );
  }, [currentTags, projectConfig?.tags]);

  if (isExperimentPending) {
    return (
      <div className="flex flex-1 flex-col overflow-auto p-4 pt-3">
        <Skeleton className="mb-2 h-7 w-full" />
        <Skeleton className="mb-6 h-14 w-full" />
        <Skeleton className="mb-2 h-7 w-full" />
        <Skeleton className="mb-6 h-14 w-full" />
      </div>
    );
  }

  const creator = experiments.find((e) => e.id === experiment.id)?.user;

  return (
    <div className="group flex flex-1 flex-col overflow-auto p-4 pt-3">
      <div className="mb-4 flex flex-col gap-2 text-sm">
        <div className="mt-px flex justify-between text-xs font-normal text-primary-600">
          Comparisons
          <div className="flex items-center gap-3">
            {comparisonsLoaded &&
              !isReadOnly &&
              selectedComparisonExperiments.length === 1 && (
                <BasicTooltip tooltipContent="Flip base and comparison experiments">
                  <Button
                    size="inline"
                    transparent
                    className="text-xs opacity-0 transition-all text-primary-400 group-hover:opacity-100"
                    onClick={flipBaseAndComparison}
                  >
                    <ArrowUpDown className="size-3" />
                  </Button>
                </BasicTooltip>
              )}
            <BasicTooltip tooltipContent="Collapse sidebar">
              <Button
                size="inline"
                transparent
                className="text-xs text-primary-400"
                onClick={() => setCollapsed(true)}
                Icon={ArrowLeftToLine}
              />
            </BasicTooltip>
          </div>
        </div>
        {!comparisonsLoaded && <Skeleton className="h-7 w-full flex-none" />}
        {comparisonsLoaded && !isReadOnly && (
          <Combobox
            options={comparableExperiments.map((e) => ({
              value: e.id,
              label: e.name,
              created: e.created,
              disabled:
                selectedComparisonExperiments.length >= 5 &&
                !selectedComparisonExperiments.find((ce) => e.id === ce.id),
              isLatest: e.id === latestExperimentId,
              isBaseline: e.id === experiment.base_exp_id,
              isDefaultBaseline: e.id === baselineExperimentId,
            }))}
            onChange={(value) => {
              if (!value) return;

              let existing = false;
              const newList = selectedComparisonExperiments.filter((e) => {
                if (e.id === value) {
                  existing = true;
                }
                return e.id !== value;
              });
              if (existing) {
                setComparisonExperiments(newList);
                return;
              }

              const selectedExperiment = comparableExperiments.find(
                (e) => e.id === value,
              );
              setComparisonExperiments([
                ...newList,
                ...(selectedExperiment ? [selectedExperiment] : []),
              ]);
            }}
            selectedValues={[experiment, ...selectedComparisonExperiments].map(
              ({ id }) => id,
            )}
            iconClassName={cn({
              hidden: isReadOnly || selectedComparisonExperiments.length === 0,
            })}
            placeholderLabel={
              selectedComparisonExperiments.length === 0 ? (
                <span className="flex items-center gap-1.5 text-xs font-medium text-primary-600">
                  <Plus className="size-3" />
                  Add comparisons
                </span>
              ) : (
                <>
                  {selectedComparisonExperiments.map((e, i) => (
                    <span key={i} className="flex items-baseline gap-2 text-xs">
                      <span
                        className={cn(
                          "block size-2 flex-none rounded-full bg-primary-700",
                          EXPERIMENT_COMPARISON_COLOR_CLASSNAMES[i],
                        )}
                      />
                      <span className="flex-1 py-0.5">{e.name}</span>
                    </span>
                  ))}
                </>
              )
            }
            side="right"
            align="start"
            clearable
            bottomActions={[
              {
                label: (
                  <span className="flex items-center gap-2">
                    <X className="size-3" />
                    Clear comparisons
                  </span>
                ),
                onSelect: () => setComparisonExperiments([]),
                hidden: selectedComparisonExperiments.length === 0,
              },
              {
                selected: !disableDefaultComparisonAutoSelect,
                label: "Auto-select a comparison experiment by default",
                onSelect: () => {
                  setDisableDefaultComparisonAutoSelect(
                    !disableDefaultComparisonAutoSelect,
                  );
                },
              },
            ]}
            searchPlaceholder="Find one or more comparison experiments"
            placeholderClassName="font-medium flex-1 flex flex-col gap-1"
            noResultsLabel="No experiments found"
            variant="button"
            buttonVariant="ghost"
            buttonClassName={cn(
              "px-0 transition-all hover:px-2 h-auto min-h-7 justify-start text-left py-1",
              {
                "pointer-events-none": isReadOnly,
              },
            )}
            buttonSize="sm"
            contentWidth={500}
            renderOptionLabel={experimentComboboxOptionRenderer}
            renderOptionTooltip={(option) => {
              const e = experiments?.find((e) => e.id === option.value);
              return experimentComboboxOptionTooltipRenderer({
                name: e?.name,
                summary: errorSummaryData?.[option.value],
                metadata: e?.metadata,
                description: e?.description,
                git: e?.repo_info ?? null,
              });
            }}
            stayOpenOnChange
          />
        )}
      </div>

      <div className="border-t" />

      <div className="mb-2 pt-4 text-xs font-normal text-primary-600">
        Prompt
      </div>
      {promptLoadingStatus === "loading" ? (
        <Skeleton className="h-7 w-full flex-none" />
      ) : !!prompt || promptLoadingStatus === "unsaved_prompt" ? (
        <BasicTooltip
          side="right"
          align="start"
          disableHoverableContent={false}
          className="flex w-full max-w-sm flex-col gap-2.5 py-3"
          tooltipContent={
            <PromptPreviewTooltip
              version={prompt?._xact_id}
              prompt={prompt?.prompt_data}
              promptMeta={{
                name: prompt?.name,
                description: prompt?.description,
                metadata: prompt?.metadata,
              }}
            />
          }
        >
          <div>
            <Button
              size="xs"
              Icon={MessageCircle}
              variant="ghost"
              className="flex w-full flex-1 justify-start gap-2 truncate px-0 text-left transition-all hover:px-2"
              onClick={() => setOpenedPromptId(prompt?.id ?? null)}
            >
              <span className="flex-1 truncate">
                {prompt?.name ?? "Untitled"}
              </span>
            </Button>
          </div>
        </BasicTooltip>
      ) : (
        <div className="text-xs text-primary-500">
          {promptLoadingStatus === "prompt_not_found" ? "Not found" : "None"}
        </div>
      )}

      {projectId && orgId && (
        <FunctionDialog
          context="try_prompt"
          mode={
            isSavedPrompt
              ? {
                  type: "view_saved",
                }
              : {
                  type: "view_unsaved",
                  upsert: (promptToSave: UIFunction) =>
                    createPrompt(promptToSave, false),
                }
          }
          type="prompt"
          objectType={isSavedPrompt ? "project_prompts" : "prompt_session"}
          identifier={`prompt-${openedPromptId}`}
          opened={openedPromptId !== undefined}
          setOpened={(open) => {
            if (!open) {
              setOpenedPromptId(undefined);
            }
          }}
          orgName={orgName}
          projectName={projectName}
          projectId={projectId}
          copilotContext={copilotContext}
          initialFunction={prompt ?? null}
          status={promptLoadingStatus}
        />
      )}

      <div className="border-b pt-4" />

      <div className="mb-2 pt-4 text-xs font-normal text-primary-600">
        Dataset
      </div>
      {dataset && experiment.dataset_id && experiment.dataset_version ? (
        <DatasetMetadata
          datasetId={experiment.dataset_id}
          datasetVersion={experiment.dataset_version}
          datasetName={dataset.name}
          orgName={orgName}
          projectName={projectName}
          side="right"
        >
          <Link
            href={getDatasetLink({
              orgName,
              projectName: dataset.project_name,
              datasetName: dataset.name,
            })}
            className={cn(
              buttonVariants({ variant: "ghost", size: "xs" }),
              "gap-2 px-0 transition-all hover:px-2",
            )}
          >
            <Database className="size-3 flex-none" />
            <span className="flex-1 truncate">{dataset.name}</span>
          </Link>
        </DatasetMetadata>
      ) : (
        <div className="text-xs text-primary-500">
          Rows not attached to a dataset
        </div>
      )}

      <div className="border-b pt-4" />

      <div className="mb-2 pt-4 text-xs font-normal text-primary-600">
        Scorers and distribution
      </div>
      <ExperimentSidebarScorers
        copilotContext={copilotContext}
        scoreFields={scoreFields}
        distributionChartProps={distributionChartProps}
        distributionChartRef={histogramRef}
        viewLayout={viewLayout}
      />

      <div className="border-b pt-4" />

      <div className="mb-2 pt-4 text-xs font-normal text-primary-600">
        Metadata
      </div>
      <UpdateableDataTextEditor
        isReadOnly={isReadOnly}
        value={experiment.metadata || {}}
        rowId={experiment.id}
        xactId="0"
        updateValue={updateMetadata}
        formatOnBlur
      />

      <div className="border-b pt-4" />

      <div className="mb-2 flex items-center justify-between pt-2.5 text-xs font-normal text-primary-600">
        Tags
        {!isReadOnly && (
          <TagsCombobox
            selectedTags={currentTags}
            onChange={handleTagsChange}
            canAddTag
          >
            <Button
              size="xs"
              variant="ghost"
              Icon={TagIcon}
              className="text-primary-500"
            />
          </TagsCombobox>
        )}
      </div>
      <div className="flex flex-wrap gap-1 whitespace-normal text-wrap break-words text-xs leading-5">
        {sortedTags.length > 0 ? (
          sortedTags.map((tag) => {
            return (
              <Tag key={tag.name} label={tag.name} color={tag.config?.color} />
            );
          })
        ) : (
          <div className="text-primary-500">No tags</div>
        )}
      </div>

      <div className="border-b pt-4" />

      <div className="mb-2 pt-4 text-xs font-normal text-primary-600">
        Description
      </div>
      <TextArea
        className="max-h-56 min-h-[38px] flex-none resize-none"
        placeholder="Enter experiment description"
        defaultValue={experiment.description ?? undefined}
        onDebouncedChange={(val) => {
          updateExperiment("description", val);
        }}
      />
      {experiment.repo_info && !isReadOnly && (
        <GitMetaData
          {...experiment.repo_info}
          className="mt-6 self-start text-xs"
          enableInteractiveTooltip
          side="right"
        />
      )}
      <CopyToClipboardButton
        size="xs"
        variant="ghost"
        copyMessage="Copy experiment ID to clipboard"
        textToCopy={experiment.id}
        className="mt-5 hidden flex-none px-0 font-mono text-xs font-medium transition-all text-primary-500 hover:px-2 lg:flex"
      >
        <span className="font-inter text-[10px] uppercase tracking-wider opacity-60">
          ID
        </span>
        <span className="flex-1 truncate text-left">{experiment.id}</span>
      </CopyToClipboardButton>
      {experiment.created && creator && (
        <div className="mb-3 mt-5 text-xs text-primary-400">
          Experiment created{" "}
          {smartTimeFormat(new Date(experiment.created).getTime())} by{" "}
          {creator.given_name} {creator.family_name}
        </div>
      )}
    </div>
  );
};

export function experimentComboboxOptionTooltipRenderer({
  name,
  summary,
  metadata,
  description,
  git,
}: {
  name?: string | null;
  summary?: { rowCount: number; errorCount: number; errorRate: number };
  metadata?: Record<string, unknown> | null;
  description?: string | null;
  git: {
    commit?: string | null;
    branch?: string | null;
  } | null;
}) {
  const content = [
    name ? (
      <div key="name" className="flex-none">
        <div className="my-1 text-xs font-medium">{name}</div>
      </div>
    ) : null,
    summary ? (
      <div key="examples" className="flex-none">
        <div className="mb-1 text-xs text-primary-500">Examples</div>
        <div className="text-xs">{summary.rowCount}</div>
      </div>
    ) : null,
    summary && summary.errorCount > 0 ? (
      <div key="errors" className="flex-none">
        <div className="mb-1 text-xs text-primary-500">Error rate</div>
        <div className="text-xs">
          {Math.round(summary.errorRate * 100)}%{" "}
          <span className="text-xs text-primary-400">
            ({summary.errorCount})
          </span>
        </div>
      </div>
    ) : null,
    description ? (
      <span key="description" className="flex-none">
        <div className="mb-1 text-xs text-primary-500">Description</div>
        <div className="text-xs">{description}</div>
      </span>
    ) : null,
    git && (git.commit || git.branch) ? (
      <div key="git" className="flex flex-col">
        <div className="mb-1 text-xs text-primary-500">Git</div>
        <div key="git" className="flex flex-row items-center gap-2">
          <GitBranch className="size-3 flex-none" />
          <span className="font-mono text-xs">
            {git.branch}
            {git.commit ? <span> {git.commit.slice(0, 7)}</span> : null}
          </span>
        </div>
      </div>
    ) : null,
    !metadata || Object.keys(metadata).length === 0 ? null : (
      <span key="metadata" className="flex-none">
        <div className="mb-1 text-xs text-primary-500">Metadata</div>
        <SyntaxHighlight
          className="text-xs"
          language="json"
          content={JSON.stringify(metadata, null, 2)}
        />
      </span>
    ),
  ];

  if (content.every((v) => v == null)) return null;

  return <div className="flex flex-col gap-4">{content}</div>;
}

export const ExperimentSidebar = memo(ExperimentSidebarComponent);
ExperimentSidebar.displayName = "ExperimentSidebar";

export function experimentComboboxOptionRenderer(option: {
  label: string;
  created?: string | null;
  isLatest?: boolean;
  isBaseline?: boolean;
  isDefaultBaseline?: boolean;
}) {
  return (
    <div className="flex w-full items-baseline gap-3">
      <div className="flex-1 truncate text-xs">{option.label}</div>
      {option.isLatest && <LatestObjectBadge className="m-0 py-0" />}
      {option.isBaseline && <BaselineObjectBadge className="m-0 py-0" />}
      {option.isDefaultBaseline && (
        <BaselineObjectBadge isDefault className="m-0 py-0" />
      )}
      {option.created && (
        <div className="flex-none text-xs tabular-nums text-primary-600">
          {smartTimeFormat(new Date(option.created).getTime())}
        </div>
      )}
    </div>
  );
}
