"use client";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useAnalytics } from "#/ui/use-analytics";
import { useViewStates, type ViewParams } from "#/utils/view/use-view";
import { Views } from "#/ui/views";
import {
  type Clause,
  type ClauseType,
  isClauseType,
} from "#/utils/search/search";
import AppliedFilters from "#/ui/applied-filters";
import {
  CancelSelectionButton,
  SelectionBarButton,
} from "#/ui/table/selection-bar";
import { useTableSelection } from "#/ui/table/useTableSelection";
import { VizQuery } from "#/ui/viz-query";
import { useArrowAPI } from "#/utils/btapi/btapi";
import {
  dbQuery,
  useDBQuery,
  useDuckDB,
  useMaterializedArrow,
} from "#/utils/duckdb";
import { isEmpty } from "#/utils/object";
import {
  type Table as ArrowTable,
  type TypeMap,
  Field,
  Float,
  Int,
  Precision,
  Schema,
  Utf8,
} from "apache-arrow";
import { type ClientOptions } from "openai";
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
  useRef,
} from "react";
import { useEntityBatchActions } from "../../useEntityBatchActions";
import { ExperimentsChart } from "../(ExperimentsChart)/ExperimentsChart";
import {
  useLoadExperimentChartData,
  MAX_EXPERIMENTS,
} from "../(ExperimentsChart)/useLoadExperimentChartData";
import {
  makeExperimentsFormatters,
  useProjectRowEvents,
} from "../experiments-formatters";
import {
  EmptyStateCodeSnippets,
  ORGS_WITH_DISABLED_EMPTY_STATE_CODE_SNIPPETS,
  TableEmptyState,
} from "#/ui/table/TableEmptyState";
import { Beaker, HelpCircle, Plus, Trash } from "lucide-react";
import { ExternalLink } from "#/ui/link";
import { doubleQuote, singleQuote } from "#/utils/sql-utils";
import {
  type BTQLTableDefinition,
  useClauseChecker,
} from "#/utils/search-btql";
import { z } from "zod";
import { runAISearch } from "#/utils/ai-search/actions/events";
import { useOrg } from "#/utils/user";
import { useBtql, useIsRootBtqlSnippet } from "#/utils/btql/btql";
import * as Query from "#/utils/btql/query-builder";
import { deriveScoreFields } from "#/ui/use-log-summary";
import { AccessFailed } from "#/ui/access-failed";
import { makeWeightedScoreSQL } from "@braintrust/local/query";
import { getExperimentLink } from "./[experiment]/getExperimentLink";
import Link from "next/link";
import { cn } from "#/utils/classnames";
import { Button, buttonVariants } from "#/ui/button";
import type { AggregationType } from "#/utils/queries/aggregations";
import {
  isEqualSelectionType,
  GROUP_BY_SCORE,
  isCategoricalXMetric,
  X_AXIS_COMPARISON,
  GROUP_BY_NONE,
} from "#/ui/charts/selectionTypes";
import { decodeURIComponentPatched } from "#/utils/url";
import { zodToLogicalSchema } from "#/utils/zod-to-logical-schema";
import {
  nullValueExpr,
  BT_GROUP_KEY,
  BT_IS_GROUP,
  BT_SUBROWS,
} from "#/ui/table/grouping/queries";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { invalidateId } from "#/utils/react-query";
import { ProjectListLayout } from "../project-list-layout";
import { parseObjectJSON } from "#/utils/schema";
import { useCustomScorerNames } from "#/ui/prompts/use-scorer-names";
import { toast } from "sonner";
import { EntityContextMenu } from "#/ui/entity-context-menu";
import { traverseExpr, type BoundExpr } from "@braintrust/btql/binder";
import useFilterSortBarSearch from "#/ui/use-filter-sort-search";
import { setTagSearchFn } from "#/ui/trace/tags";
import { useFeatureFlags } from "#/lib/feature-flags";
import { CreateSingleExperimentNullStateDialog } from "../../../prompt/[prompt]/create-experiment-dialog";
import { ProjectPromptsDropdownProvider } from "../playgrounds/prompts-dropdown";
import { AgentsDropdownProvider } from "../playgrounds/agents-dropdown";
import {
  generateFields,
  getGroupBy,
  useExperimentsQuery,
} from "./query-builders";
import { ExperimentsBulkTagEditor } from "./ExperimentsBulkTagEditor";
import { ScorersDropdownProvider } from "../playgrounds/scorers-dropdown";
import { useCustomColumns } from "#/utils/custom-columns/use-custom-columns";

export interface Params {
  org: string;
  project: string;
}

// DEPRECATION_NOTICE: Once everyone turns on BTQL with pivot support (0.0.44), we can remove this
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export const emptyExperimentSummarySchema = (row: any) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const scoreFields: Field<any>[] = !isEmpty(row)
    ? Object.keys(row)
        .filter((key) => key.startsWith("avg_"))
        .map((key) =>
          Field.new({ name: key, type: new Float(Precision.DOUBLE) }),
        )
    : [];
  return new Schema(
    [
      Field.new({ name: "experiment_id", type: new Utf8() }),
      Field.new({ name: "last_updated", type: new Utf8() }),
      Field.new({ name: "num_examples", type: new Int(true, 64) }),
    ].concat(scoreFields),
  );
};

function makeExperimentSummarySchema(
  scoreFields: string[],
): BTQLTableDefinition {
  return {
    logical: zodToLogicalSchema(
      z.strictObject({
        id: z.string(),
        name: z.string(),
        description: z.string().nullable(),
        last_updated: z.string().datetime(),
        duration: z.number(),
        llm_duration: z.number(),
        prompt_tokens: z.number(),
        completion_tokens: z.number(),
        total_tokens: z.number(),
        num_examples: z.number().int(),
        num_errors: z.number().int(),
        error_rate: z.number(),
        creator: z.record(z.any()),
        source: z.record(z.any()),
        metadata: z.record(z.any()),
        dataset: z.string().nullable(),
        tags: z.array(z.string()).nullable(),
        ...Object.fromEntries(scoreFields.map((key) => [key, z.number()])),
      }),
    ),
    physical: {
      columns: {
        id: { path: ["id"], type: { type: "varchar" } },
        name: { path: ["name"], type: { type: "varchar" } },
        description: { path: ["description"], type: { type: "varchar" } },
        last_updated: { path: ["last_updated"], type: { type: "varchar" } },
        num_examples: { path: ["num_examples"], type: { type: "bigint" } },
        num_errors: { path: ["num_errors"], type: { type: "bigint" } },
        error_rate: { path: ["error_rate"], type: { type: "double" } },
        duration: { path: ["duration"], type: { type: "double" } },
        llm_duration: { path: ["llm_duration"], type: { type: "double" } },
        prompt_tokens: { path: ["prompt_tokens"], type: { type: "bigint" } },
        completion_tokens: {
          path: ["completion_tokens"],
          type: { type: "bigint" },
        },
        total_tokens: { path: ["total_tokens"], type: { type: "bigint" } },
        creator: { path: ["creator"], type: { type: "json" } },
        source: { path: ["source"], type: { type: "json" } },
        metadata: { path: ["metadata"], type: { type: "json" } },
        dataset: { path: ["dataset"], type: { type: "varchar" } },
        tags: {
          path: ["tags"],
          type: { type: "json" },
        },
        ...Object.fromEntries(
          scoreFields.map((key) => [
            key,
            { path: [key], type: { type: "double" } },
          ]),
        ),
      },
    },
  };
}

const metricNames = [
  "duration",
  "llm_duration",
  "prompt_tokens",
  "completion_tokens",
  "total_tokens",
];

export default function ClientPage({ params }: { params: Params }) {
  const orgName = decodeURIComponentPatched(params.org);
  const projectName = decodeURIComponentPatched(params.project);

  const {
    selectedRows: rowSelectionExperiments,
    setSelectedRows: setRowSelectionExperiments,
    getSelectedRowsWithData: getSelectedRowsWithDataExperiments,
    selectedRowsNumber: selectedRowsNumberExperiments,
    deselectAllTableRows: deselectAllTableRowsExperiments,
    tableRef: tableRefExperiments,
  } = useTableSelection();

  const {
    projectId,
    experimentsReady,
    experimentsTable,
    mutateExperiments,
    config,
  } = useContext(ProjectContext);

  const { data: countData } = useDBQuery(
    experimentsTable
      ? `SELECT COUNT(1) as count FROM ${doubleQuote(experimentsTable)}`
      : null,
    [experimentsReady],
  );
  const numExperiments = Number(countData?.at(0)?.count);

  useAnalytics({
    page: projectId
      ? {
          category: "project",
          props: { project_id: projectId },
        }
      : null,
  });

  const {
    experimentIds,
    latestExperimentId,
    baselineExperimentId,
    setBaselineExperimentId,
    isLimitingExperiments,
  } = useRecentExperiments();

  const btqlSummary = useBtql({
    name: "Experiment summary (scores) query",
    query: useMemo(
      () =>
        projectId && experimentIds
          ? {
              from: Query.from("experiment", experimentIds),
              unpivot: [
                {
                  expr: { btql: "scores" },
                  alias: ["score", "value"],
                },
              ],
              measures: [
                { alias: "last_updated", expr: { btql: "max(created)" } },
                { alias: "avg", expr: { btql: "avg(value)" } },
              ],
              dimensions: [
                { alias: "experiment_id", expr: { btql: "experiment_id" } },
              ],
              pivot: [{ alias: "scores", expr: { btql: "score" } }],
            }
          : null,
      [experimentIds, projectId],
    ),
    disableLimit: true,
    brainstoreRealtime: true,
  });

  const isRootBtqlSnippet = useIsRootBtqlSnippet();

  // DEVNOTE: Keep this in sync with test_token_counts.py
  const btqlCountSummary = useBtql({
    name: "Experiment summary (counts) query",
    query: useMemo(
      () =>
        projectId && experimentIds
          ? {
              from: Query.from("experiment", experimentIds),
              measures: [
                {
                  alias: "num_examples",
                  expr: { btql: `sum(${isRootBtqlSnippet})` },
                },
                {
                  alias: "duration",
                  expr: {
                    // If the experiment has any task durations, the first AVG() will satisfy the COALESCE(),
                    // otherwise we fall back to the second one, which is the overall duration.
                    btql: `COALESCE(AVG(span_attributes.name = 'task' ? metrics.end-metrics.start : NULL), AVG(${isRootBtqlSnippet} ? metrics.end-metrics.start : NULL))`,
                  },
                },
                {
                  alias: "llm_duration",
                  expr: {
                    btql: "AVG(COALESCE(span_attributes.purpose, 'default') != 'scorer' AND span_attributes.type = 'llm' ? metrics.end-metrics.start : NULL)",
                  },
                },
                {
                  alias: "prompt_tokens",
                  expr: {
                    btql: "SUM(COALESCE(span_attributes.purpose, 'default') != 'scorer' ? metrics.prompt_tokens : NULL)/num_examples",
                  },
                },
                {
                  alias: "completion_tokens",
                  expr: {
                    btql: "SUM(COALESCE(span_attributes.purpose, 'default') != 'scorer' ? metrics.completion_tokens : NULL)/num_examples",
                  },
                },
                {
                  alias: "total_tokens",
                  expr: {
                    btql: "prompt_tokens + completion_tokens",
                  },
                },
                {
                  alias: "num_errors",
                  expr: {
                    btql: `COUNT(${isRootBtqlSnippet} ? error : NULL)`,
                  },
                },
                {
                  alias: "error_rate",
                  expr: {
                    btql: `num_errors / num_examples`,
                  },
                },
              ],
              dimensions: [
                { alias: "experiment_id", expr: { btql: "experiment_id" } },
              ],
            }
          : null,
      [experimentIds, projectId, isRootBtqlSnippet],
    ),
    disableLimit: true,
    expensive: true,
    brainstoreRealtime: true,
  });

  const projectIdParams = useMemo(
    () =>
      projectId && experimentIds && btqlSummary.unsupported
        ? {
            project_id: projectId,
            experiment_ids: experimentIds,
          }
        : null,
    [projectId, experimentIds, btqlSummary.unsupported],
  );

  const { data: projectSummary } = useArrowAPI({
    name: "Experiment summary query",
    endpoint: projectIdParams && "summary",
    params: projectIdParams,
    schema: emptyExperimentSummarySchema,
  });

  const { customScorerNames, promptDataHasLoaded, promptsReady } =
    useCustomScorerNames();

  const { actions: deleteExperimentsActions, modals: deleteExperimentsModal } =
    useEntityBatchActions({
      entityType: "experiment",
      onUpdate: () => {
        mutateExperiments();
        deselectAllTableRowsExperiments();
      },
      entityName: projectName,
    });

  const {
    refreshed: summaryReady,
    name: summaryTable,
    schema: summarySchema,
  } = useMaterializedArrow(
    `experiments_summary_${projectId}`,
    btqlSummary.unsupported ? projectSummary : (btqlSummary.data ?? null),
  );

  const { refreshed: countReadyRaw, name: countTable } = useMaterializedArrow(
    `experiments_count_${projectId}`,
    btqlCountSummary.data ?? null,
  );

  // Disable this if the BTQL API is unsupported
  const countReady = btqlCountSummary.unsupported ? 1 : countReadyRaw;

  // We need to use the schema from the actual table, not the
  // arrow schema, because it may have not materialized into
  // the table itself.
  const {
    scoreFields: sortedScoreFields,
    scoreAliases,
    scoreToAlias,
    scoreAliasesLoading,
  } = useMemo(
    () => ({
      ...deriveScoreFields({
        summarySchema,
        btqlSchema: btqlSummary.schema,
        scoreConfig: config.scores,
        existingMapping: customScorerNames,
      }),
      scoreAliasesLoading:
        (!btqlSummary.unsupported && !btqlSummary.schema) ||
        !summarySchema ||
        !promptDataHasLoaded,
    }),
    [
      summarySchema,
      btqlSummary.schema,
      config.scores,
      customScorerNames,
      btqlSummary.unsupported,
      promptDataHasLoaded,
    ],
  );

  const [groupAggregationTypes, setGroupAggregationTypes] = useState<
    Record<string, AggregationType>
  >({});
  const setGroupAggregationType = useCallback(
    (name: string, value: AggregationType) => {
      setGroupAggregationTypes((prev) => ({
        ...prev,
        [name]: value,
      }));
    },
    [setGroupAggregationTypes],
  );
  const sizeConstraintsMap = useMemo(
    () => ({
      name: {
        minSize: 250,
      },
      last_updated: {
        minSize: 120,
      },
      source: {
        minSize: 180,
      },
    }),
    [],
  );
  const columnVisibilityMap = useMemo(
    () => ({
      id: false,
      error_rate: false,
      description: false,
    }),
    [],
  );

  const aggregateScoreSQL: Record<string, string> = useMemo(
    () =>
      makeWeightedScoreSQL({
        scoreConfig: config.scores,
        scoreNames: sortedScoreFields,
        scoreToField: scoreToAlias,
      }),
    [config.scores, sortedScoreFields, scoreToAlias],
  );

  const experimentsSchema = useMemo(
    () =>
      scoreAliasesLoading ? null : makeExperimentSummarySchema(scoreAliases),
    [scoreAliases, scoreAliasesLoading],
  );

  const { clauseChecker, setCustomColumnsSchema } = useClauseChecker(
    experimentsSchema,
    true,
  );
  const viewParams: ViewParams = {
    objectType: "project",
    objectId: projectId ?? "",
    viewType: "experiments",
  };
  const pageIdentifier = "project-experiments-list-" + projectId;

  const experimentsChartingProps = useMemo(
    () => ({
      groupingDefaultSelectionType: GROUP_BY_SCORE,
    }),
    [],
  );
  const viewProps = useViewStates({
    viewParams,
    clauseChecker,
    pageIdentifier,
    experimentsChartingProps,
  });

  const xAxis = viewProps.xAxis;

  const baseGroupByMetadata =
    !viewProps.grouping ||
    isEqualSelectionType(viewProps.grouping, GROUP_BY_NONE)
      ? GROUP_BY_SCORE
      : viewProps.grouping;
  const primaryGrouping =
    isEqualSelectionType(baseGroupByMetadata, GROUP_BY_SCORE) &&
    isCategoricalXMetric(xAxis)
      ? xAxis
      : baseGroupByMetadata;

  const { applySearch } = useFilterSortBarSearch({
    clauseChecker,
    setSearch: viewProps.setSearch,
  });

  const onTagClick = useMemo(
    () => setTagSearchFn(clauseChecker, viewProps.setSearch),
    [clauseChecker, viewProps.setSearch],
  );

  const customColumnState = useCustomColumns({
    scope: projectId
      ? {
          object_type: "project",
          object_id: projectId,
          subtype: "experiment",
          variant: "experiment_list",
        }
      : undefined,
  });

  // Basic signals needed for table structure - only need experiments and prompts
  const basicTableSignals = useMemo(
    () => [experimentsReady, promptsReady],
    [experimentsReady, promptsReady],
  );

  // Full signals for complete data (used by chart)
  const experimentsSignals = useMemo(
    () => [summaryReady, experimentsReady, countReady, promptsReady],
    [summaryReady, experimentsReady, countReady, promptsReady],
  );

  const {
    experimentsQuery,
    customColumnsTyped,
    parseCustomColumnsError,
    parseCustomColumnsPending,
  } = useExperimentsQuery({
    params: {
      experimentsTable,
      viewProps,
      sortedScoreFields,
      scoreAliases,
      summaryTable,
      summaryReady: summaryReady > 0,
      aggregateScoreSQL,
      btqlSummaryUnsupported: btqlSummary.unsupported ?? false,
      btqlCountSummaryUnsupported: btqlCountSummary.unsupported ?? false,
      countTable,
      countReady: countReady > 0,
      projectId: projectId!,
      scoreToAlias,
      scoreConfig: config.scores,
    },
    customColumns: customColumnState.customColumnDefinitions,
    experimentsSignals,
    experimentsSchema,
  });

  const {
    flags: { customColumnsV2 },
  } = useFeatureFlags();

  useEffect(() => {
    if (parseCustomColumnsPending) {
      if (!customColumnsV2) {
        setCustomColumnsSchema(null);
      }
      return;
    }
    if (customColumnState.isError || parseCustomColumnsError) {
      setCustomColumnsSchema(null);
      return;
    }
    setCustomColumnsSchema(customColumnsTyped?.customColumnsSchema ?? null);
  }, [
    setCustomColumnsSchema,
    parseCustomColumnsPending,
    customColumnsTyped?.customColumnsSchema,
    customColumnState.isError,
    parseCustomColumnsError,
    customColumnsV2,
  ]);

  // Calculate which columns should show loading state
  const loadingColumns = useMemo(() => {
    const loading: string[] = [];

    // Score columns need summaryReady
    if (!summaryReady) {
      loading.push(...scoreAliases);
    }

    // Metric columns need countReady
    if (!countReady) {
      loading.push(
        "num_examples",
        "error_rate",
        "num_errors",
        "duration",
        "llm_duration",
        "prompt_tokens",
        "completion_tokens",
        "total_tokens",
      );
    }

    return loading;
  }, [summaryReady, countReady, scoreAliases]);

  const queryClient = useQueryClient();
  useEffect(() => {
    invalidateId(queryClient, projectId);
  }, [queryClient, projectId, summaryReady, experimentsReady, countReady]);

  const duck = useDuckDB();
  const expAISchemaColumns = (
    projectSummary?.schema.fields
      .map((f) => f.name)
      .filter(
        (x) =>
          ![
            "experiment_id",
            "last_updated",
            "num_examples",
            "duration",
          ].includes(x),
      ) ?? []
  ).concat(["name", "creator", "source", "metadata", "dataset"]);

  const experimentRowEvents = useProjectRowEvents({
    entity: "experiment",
    entityNameProp: "name",
    orgName,
    projectName,
  });

  const org = useOrg();

  const runExperimentsAISearch = (openAIOpts: ClientOptions, query: string) =>
    runAISearch({
      searchType: "projectExperiments",
      apiUrl: org.api_url,
      openAIOpts,
      orgName,
      query,
      aiSchemaColumns: expAISchemaColumns,
    });

  const {
    experimentData,
    loading: loadingChartData,
    metadataFields,
    numericMetadataFields,
    selectedMeasures,
    setSelectedMeasures,
  } = useLoadExperimentChartData({
    experimentsQuery: scoreAliasesLoading ? null : experimentsQuery,
    experimentsSignals,
    scoreNames: scoreAliases,
    metricNames: metricNames,
    excludedMeasures: viewProps.excludedMeasures,
    setExcludedMeasures: viewProps.setExcludedMeasures,
    isViewsPending: viewProps.isPending,
  });

  const chartControlsProps = useMemo(() => {
    return {
      grouping: baseGroupByMetadata,
      setGrouping: viewProps.setGrouping,
      selectedMeasures,
      setSelectedMeasures,
      yMetric: viewProps.yMetric ?? null,
      setYMetric: viewProps.setYMetric,
      xMetric: viewProps.xAxis,
      setXMetric: viewProps.setXAxis,
      symbolGrouping: viewProps.symbolGrouping,
      setSymbolGrouping: viewProps.setSymbolGrouping,
      xAxisAggregation: viewProps.xAxisAggregation,
      setXAxisAggregation: viewProps.setXAxisAggregation,
      chartAnnotations: viewProps.chartAnnotations,
      setChartAnnotations: viewProps.setChartAnnotations,
    };
  }, [
    baseGroupByMetadata,
    viewProps.setGrouping,
    selectedMeasures,
    setSelectedMeasures,
    viewProps.yMetric,
    viewProps.setYMetric,
    viewProps.xAxis,
    viewProps.setXAxis,
    viewProps.symbolGrouping,
    viewProps.setSymbolGrouping,
    viewProps.xAxisAggregation,
    viewProps.setXAxisAggregation,
    viewProps.chartAnnotations,
    viewProps.setChartAnnotations,
  ]);

  const { data: groupQueryData } = useQuery({
    queryKey: [
      projectId,
      "groupQuery",
      experimentsQuery,
      primaryGrouping,
      [
        viewProps.search.sort,
        experimentsQuery,
        sortedScoreFields,
        scoreAliases,
        scoreToAlias,
        groupAggregationTypes,
        customColumnsTyped?.customColumns,
      ],
    ],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const conn = await duck!.connect();
      if (
        !primaryGrouping ||
        isEqualSelectionType(primaryGrouping, GROUP_BY_SCORE)
      ) {
        return null;
      }

      const orderByClauses: string[] = [];
      if (viewProps.search.sort?.length) {
        orderByClauses.push(...viewProps.search.sort.map((s) => s.text));
      }
      orderByClauses.push(`${BT_GROUP_KEY} ASC NULLS LAST`);
      orderByClauses.push("last_updated DESC NULLS LAST");
      const orderBy = orderByClauses.join(", ");

      const groupColumnName = "name";
      const groupBy = getGroupBy(
        primaryGrouping.value,
        customColumnsTyped?.customColumns,
      );

      const baseQuery = `SELECT *, e as experiment,
        ${groupBy}
        FROM (${experimentsQuery}) e
        LIMIT ${MAX_EXPERIMENTS}`;
      const groupQuery = `
      WITH base AS (
        ${baseQuery}
      )
      SELECT
          TRUE AS ${BT_IS_GROUP},
          ${BT_GROUP_KEY},
          ${generateFields(
            scoreAliases.map((s) => {
              return `${groupAggregationTypes.scores ?? "AVG"}(${doubleQuote(
                s,
              )}) AS ${doubleQuote(s)}`;
            }),
            customColumnsTyped?.customColumns,
            (f: string) => {
              if (f === groupColumnName) {
                return `${BT_GROUP_KEY} AS ${groupColumnName}`;
              }
              if (f === "last_updated") {
                return "MAX(last_updated) AS last_updated";
              }
              if (f === "error_rate") {
                return `${groupAggregationTypes.scores ?? "AVG"}(${f}) AS ${f}`;
              }
              if (f === "num_examples" || f === "num_errors") {
                return `${groupAggregationTypes.metrics ?? "SUM"}(${f}) AS ${f}`;
              }
              if (
                f === "duration" ||
                f === "llm_duration" ||
                f === "prompt_tokens" ||
                f === "completion_tokens" ||
                f === "total_tokens"
              ) {
                return `${groupAggregationTypes.metrics ?? "SUM"}(${f}) AS ${f}`;
              }
              if (f === "creator") {
                return `ARRAY_AGG(DISTINCT creator) AS creator`;
              }
              return nullValueExpr(f);
            },
          )},
        array_agg(base.experiment) AS ${BT_SUBROWS}
      FROM base
      GROUP BY ${BT_GROUP_KEY}
      ORDER BY ${orderBy}
    `;

      const colorIndexQuery = `WITH base AS (
        ${baseQuery}
      ),
      grouped AS (
        SELECT ${BT_GROUP_KEY}
        FROM base
        GROUP BY ${BT_GROUP_KEY}
      )
      SELECT ${BT_GROUP_KEY}, row_number() over (ORDER BY ${BT_GROUP_KEY} ASC NULLS LAST) as colorIndex
      FROM grouped
      `;

      const colorMap: Record<string, number> = {};
      const result = await dbQuery(conn, signal, colorIndexQuery);
      if (!result) {
        return null;
      }
      for (const row of result) {
        colorMap[row[BT_GROUP_KEY]] = Number(row.colorIndex) - 1;
      }
      return { groupQuery, colorMap };
    },
    staleTime: Infinity,
    enabled:
      !!duck && experimentsSignals.every((s) => s > 0) && !!experimentsQuery,
  });

  const rowGroupingData = useMemo(() => {
    if (
      !primaryGrouping ||
      isEqualSelectionType(primaryGrouping, GROUP_BY_SCORE)
    ) {
      return null;
    }
    return {
      groupRows: [],
      groupBy: primaryGrouping.value,
    };
  }, [primaryGrouping]);

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const experimentsFormatters = useMemo(
    () =>
      makeExperimentsFormatters({
        scoreFields: scoreAliases,
        groupAggregationTypes,
        setGroupAggregationType,
        isGrouping: !isEqualSelectionType(primaryGrouping, GROUP_BY_SCORE),
        colorMap:
          !isEqualSelectionType(baseGroupByMetadata, GROUP_BY_SCORE) &&
          !isEqualSelectionType(xAxis, X_AXIS_COMPARISON)
            ? groupQueryData?.colorMap
            : undefined,
        latestExperimentId,
        baselineExperimentId: baselineExperimentId ?? undefined,
        config,
        onTagClick,
      }),
    [
      scoreAliases,
      groupAggregationTypes,
      setGroupAggregationType,
      primaryGrouping,
      baseGroupByMetadata,
      groupQueryData?.colorMap,
      xAxis,
      latestExperimentId,
      baselineExperimentId,
      config,
      onTagClick,
    ],
  );

  const vizQueryRef = useRef<{
    data: ArrowTable<TypeMap> | null;
  }>(null);

  const getRowsForExport = useCallback(async () => {
    const jsonRows =
      vizQueryRef.current?.data?.toArray()?.map((e) => e.toJSON()) ?? [];
    return jsonRows.map((r) => parseObjectJSON("project_experiments", r));
  }, []);

  const neverVisibleColumns = useMemo(() => {
    return new Set([]);
  }, []);

  const onDeleteFilter = useCallback(
    (clause: Clause<ClauseType>) => {
      if (isClauseType(clause, "filter")) {
        const rows = extractIdsFromFilterClause(clause);
        setRowSelectionExperiments((prev) => ({
          ...prev,
          ...Object.fromEntries(rows.map((r) => [r, true])),
        }));
      }
    },
    [setRowSelectionExperiments],
  );

  const customColumns = useMemo(
    () => ({
      options: {
        metadata: metadataFields ?? [],
      },
      isEnabled: customColumnState.customColumnsEnabled,
      columns: customColumnState.customColumnDefinitions,
      createColumn: customColumnState.createCustomColumn,
      deleteColumn: customColumnState.deleteCustomColumn,
      updateColumn: customColumnState.updateCustomColumn,
      loading: customColumnState.customColumnDefinitions === undefined,
      error: parseCustomColumnsError,
    }),
    [
      customColumnState.customColumnsEnabled,
      customColumnState.createCustomColumn,
      customColumnState.customColumnDefinitions,
      customColumnState.deleteCustomColumn,
      customColumnState.updateCustomColumn,
      metadataFields,
      parseCustomColumnsError,
    ],
  );

  const customColumnGroupByOptions = useMemo(
    () => customColumnsTyped?.customColumns?.map(({ name }) => [name]),
    [customColumnsTyped?.customColumns],
  );

  if (isEmpty(projectId)) {
    return <AccessFailed objectType="Project" objectName={projectName} />;
  }

  const selectedExperiments = getSelectedRowsWithDataExperiments();

  return (
    <ProjectPromptsDropdownProvider>
      <AgentsDropdownProvider>
        <ScorersDropdownProvider>
          <ProjectListLayout
            active="experiments"
            orgName={orgName}
            projectName={projectName}
            scrollContainerRef={scrollContainerRef}
          >
            <AppliedFilters
              search={viewProps.search}
              clauseChecker={clauseChecker}
              setSearch={viewProps.setSearch}
              runAISearch={runExperimentsAISearch}
              onClear={onDeleteFilter}
              className="sticky left-0 w-full pt-2"
              fromClause={`experiments(${singleQuote(projectId)})`}
              disableFullBtqlQueryHint
              scoreNames={scoreAliases}
            />
            <VizQuery
              className="pt-3"
              query={groupQueryData?.groupQuery ?? experimentsQuery}
              error={
                isLimitingExperiments ? (
                  <>
                    This page is limited to 20 experiments because Brainstore is
                    not fully configured. Please contact your ops team.{" "}
                    <Link
                      href="https://www.notion.so/braintrustdata/Brainstore-configuration-documentation-180f7858028980bd94c2ee4b911544cb"
                      target="_blank"
                      className="font-medium hover:underline"
                    >
                      Learn more
                    </Link>
                  </>
                ) : undefined
              }
              vizQueryRef={vizQueryRef}
              signals={basicTableSignals}
              loadingColumns={loadingColumns}
              initiallyVisibleColumns={columnVisibilityMap}
              neverVisibleColumns={neverVisibleColumns}
              sizeConstraintsMap={sizeConstraintsMap}
              formatters={experimentsFormatters}
              scrollContainerRef={scrollContainerRef}
              hasNoRowsComponent={
                numExperiments === 0 ? (
                  <TableEmptyState
                    className="mt-8"
                    Icon={Beaker}
                    labelClassName="text-sm leading-normal"
                    label={
                      <>
                        <span className="mb-3 block text-base">
                          There are no experiments in this project yet.
                        </span>
                        Experiments are eval snapshots, a point-in-time
                        measurement of the performance of your AI application.
                        <div className="mb-8 mt-3">
                          <CreateSingleExperimentNullStateDialog>
                            <Button>Create experiment</Button>
                          </CreateSingleExperimentNullStateDialog>
                        </div>
                      </>
                    }
                  >
                    {ORGS_WITH_DISABLED_EMPTY_STATE_CODE_SNIPPETS.includes(
                      orgName,
                    ) ? null : (
                      <EmptyStateCodeSnippets
                        label={
                          <div className="flex items-center gap-1.5 text-sm text-primary-600">
                            Or, try running this code snippet{" "}
                            <ExternalLink
                              href={
                                "/docs/welcome/start#create-a-simple-evaluation-script"
                              }
                            >
                              <HelpCircle className="size-3" />
                            </ExternalLink>
                          </div>
                        }
                        ts={`import { Eval } from "braintrust";
import { Factuality } from "autoevals";

Eval(
  "${projectName}",
  {
    data: () => {
      return [
        {
          input: "David",
          expected: "Hi David",
        },
      ]; // Replace with your eval dataset
    },
    task: async (input) => {
      return "Hi " + input; // Replace with your LLM call
    },
    scores: [Factuality],
  },
);`}
                        py={`from braintrust import Eval
from autoevals import Factuality

Eval(
  "${projectName}",
  data=lambda: [
      {
          "input": "Foo",
          "expected": "Hi Foo",
      },
      {
          "input": "Bar",
          "expected": "Hello Bar",
      },
  ],  # Replace with your eval dataset
  task=lambda input: "Hi " + input,  # Replace with your LLM call
  scores=[Factuality],
)`}
                      />
                    )}
                  </TableEmptyState>
                ) : undefined
              }
              runAISearch={runExperimentsAISearch}
              extraLeftControls={
                <>
                  <CreateSingleExperimentNullStateDialog>
                    <Button
                      variant="primary"
                      size="xs"
                      className="h-7"
                      Icon={Plus}
                    >
                      Experiment
                    </Button>
                  </CreateSingleExperimentNullStateDialog>
                  <Views<true>
                    pageIdentifier={pageIdentifier}
                    viewParams={viewParams}
                    viewProps={viewProps}
                  />
                </>
              }
              chartSlot={
                <ExperimentsChart
                  experimentData={experimentData}
                  loading={loadingChartData}
                  scoreNames={scoreAliases}
                  metricNames={metricNames}
                  metadataFields={metadataFields}
                  numericMetadataFields={numericMetadataFields}
                  controlsProps={chartControlsProps}
                  selectedExperiments={rowSelectionExperiments}
                  onBrush={(v) => {
                    viewProps.setSearch((oldSearch) => {
                      const filteredSearch = {
                        ...oldSearch,
                        filter: oldSearch.filter?.filter(
                          (s) => !s.text.startsWith("last_updated"),
                        ),
                      };

                      return filteredSearch;
                    });

                    if (!v) {
                      return;
                    }
                    applySearch(
                      `last_updated >= '${new Date(
                        v.min,
                      ).toISOString()}' AND last_updated <= '${new Date(
                        v.max,
                      ).toISOString()}'`,
                    );
                  }}
                  onExperimentsClick={(experiments: { id: string }[]) => {
                    setRowSelectionExperiments((old) => {
                      return Object.fromEntries(
                        Object.entries({
                          ...old,
                          ...experiments.reduce(
                            (acc, e) => ({
                              ...acc,
                              [e.id]: !rowSelectionExperiments[e.id],
                            }),
                            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                            {} as Record<string, boolean>,
                          ),
                        }).filter(([_, v]) => v),
                      );
                    });
                  }}
                  chartHeight={viewProps.chartHeight}
                  setChartHeight={viewProps.setChartHeight}
                  customColumnOptions={customColumnGroupByOptions}
                >
                  <EntityContextMenu
                    objectType="project"
                    objectId={projectId}
                    objectName={projectName}
                    orgName={orgName}
                    projectName={projectName}
                    buttonClassName="h-7 w-7"
                    getRowsForExport={getRowsForExport}
                    exportName={[projectName, "experiments"].join(" ")}
                  />
                </ExperimentsChart>
              }
              scrollMargin={viewProps.chartHeight}
              toolbarSlot={
                selectedRowsNumberExperiments > 0 ? (
                  <>
                    <CancelSelectionButton
                      onCancelSelection={deselectAllTableRowsExperiments}
                      selectedRowsNumber={selectedRowsNumberExperiments}
                    />
                    {selectedExperiments.length === 1 && (
                      <Button
                        size="xs"
                        onClick={() => {
                          toast.promise(
                            setBaselineExperimentId(
                              baselineExperimentId === selectedExperiments[0].id
                                ? null
                                : selectedExperiments[0].id,
                            ),
                            {
                              loading: "Setting baseline experiment",
                              success: "Experiment baseline set",
                              error: "Failed to set baseline experiment",
                            },
                          );
                        }}
                      >
                        {baselineExperimentId === selectedExperiments[0].id
                          ? "Clear baseline"
                          : "Set as baseline"}
                      </Button>
                    )}
                    {selectedExperiments.length > 1 &&
                      selectedExperiments.length <= 6 && (
                        <Link
                          className={cn(buttonVariants({ size: "xs" }))}
                          href={
                            getExperimentLink({
                              orgName,
                              projectName,
                              experimentName: selectedExperiments[0].name,
                            }) +
                            "?" +
                            new URLSearchParams({
                              c: selectedExperiments
                                .slice(1)
                                .map((e) => e.name)
                                .join(","),
                              diff: "between_experiments",
                            }).toString()
                          }
                        >
                          Compare
                        </Link>
                      )}
                    <Button
                      size="xs"
                      onClick={() => {
                        viewProps.setSearch((oldSearch) => {
                          const filteredSearch = {
                            ...oldSearch,
                            filter: oldSearch.filter?.filter(
                              (s) => !s.text.startsWith("id = "),
                            ),
                          };

                          return filteredSearch;
                        });
                        applySearch(
                          `${selectedExperiments.map((e) => `id = '${e.id}'`).join(" OR \n")}`,
                        );

                        setRowSelectionExperiments({});
                      }}
                    >
                      Filter to selected
                    </Button>
                    <ExperimentsBulkTagEditor
                      selectedExperiments={selectedExperiments}
                      onTagsUpdated={() => {
                        // Optionally clear selection after updating tags
                        // setRowSelectionExperiments({});
                      }}
                    />
                    <SelectionBarButton
                      onClick={() => {
                        deleteExperimentsActions.deleteEntities({
                          entityIds: getSelectedRowsWithDataExperiments().map(
                            (row) => row.id,
                          ),
                        });
                      }}
                      Icon={Trash}
                    />
                  </>
                ) : undefined
              }
              viewProps={viewProps}
              rowEvents={experimentRowEvents}
              rowSelection={rowSelectionExperiments}
              setRowSelection={setRowSelectionExperiments}
              tableRef={tableRefExperiments}
              rowGroupingData={rowGroupingData}
              disableLimit={!!rowGroupingData}
              customColumns={customColumns}
              scoreNames={scoreAliases}
              skipErrorReporting={skipErrorReportingFn}
            />
            {deleteExperimentsModal}
          </ProjectListLayout>
        </ScorersDropdownProvider>
      </AgentsDropdownProvider>
    </ProjectPromptsDropdownProvider>
  );
}

export function useRecentExperiments() {
  const {
    experiments,
    isExperimentsLoading,
    projectId,
    projectSettings,
    mutateProject,
  } = useContext(ProjectContext);

  const setBaselineExperimentId = async (experimentId: string | null) => {
    const resp = await fetch(`/api/project/patch_id`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        id: projectId,
        settings: {
          ...projectSettings,
          baseline_experiment_id: experimentId,
        },
      }),
    });

    if (!resp.ok) {
      throw new Error(
        `Failed to set baseline experiment: ${await resp.text()}`,
      );
    } else {
      mutateProject();
    }
  };

  const latestExperimentId = useMemo(
    () =>
      experiments.sort(
        (a, b) =>
          new Date(b.created ?? "").getTime() -
          new Date(a.created ?? "").getTime(),
      )[0]?.id,
    [experiments],
  );

  const {
    flags: { brainstore, manyExperiments },
    isLoading: isLoadingFlags,
  } = useFeatureFlags();

  const isLimitingExperiments = useMemo(() => {
    return !brainstore && !isLoadingFlags;
  }, [brainstore, isLoadingFlags]);

  // Only fetch detailed experiment data for the most recent N experiments.
  const experimentIds = useMemo(
    () =>
      isExperimentsLoading
        ? undefined
        : getRecentMetadataEntries(
            experiments,
            isLimitingExperiments ? 20 : manyExperiments ? 2000 : 500,
          ),
    [experiments, isExperimentsLoading, isLimitingExperiments, manyExperiments],
  );
  return {
    experimentIds,
    latestExperimentId,
    baselineExperimentId: projectSettings?.baseline_experiment_id ?? null,
    setBaselineExperimentId,
    isLimitingExperiments,
  };
}

interface MetadataObject {
  created?: string | null;
  id: string;
}

export function getRecentMetadataEntries<T extends MetadataObject>(
  entries: T[],
  limit: number = 500,
) {
  const copy = [...entries];
  copy.sort((lhs, rhs) => {
    const lhsCreated = lhs.created;
    const rhsCreated = rhs.created;
    if (!lhsCreated) {
      return rhsCreated ? 1 : 0;
    } else if (!rhsCreated) {
      return lhsCreated ? -1 : 0;
    } else {
      return lhsCreated < rhsCreated ? 1 : -1;
    }
  });
  return copy.slice(0, limit).map((r) => r.id);
}

function extractIdsFromFilterClause(clause: Clause<"filter">) {
  const bound = clause.btql?.bound;
  if (!bound) {
    return [];
  }

  const result: string[] = [];
  traverseExpr(bound, (expr: BoundExpr) => {
    if (
      expr.op === "eq" &&
      expr.left.op === "field" &&
      expr.left.name[0] === "id" &&
      expr.right.op === "literal" &&
      typeof expr.right.value === "string"
    ) {
      result.push(expr.right.value);
    }
    return true;
  });

  return result;
}

function skipErrorReportingFn(value: string) {
  return value.includes("This page is limited to 20 experiments");
}
