import * as React from "react";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import Link from "next/link";
import {
  Activity,
  ArrowUpRight,
  Beaker,
  Bolt,
  Database,
  MessageCircle,
  Percent,
  Route,
  Settings2,
  Shapes,
} from "lucide-react";
import { buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { getPlaygroundsLink } from "../../prompt/[prompt]/getPromptLink";
import { getExperimentsLink } from "./experiments/[experiment]/getExperimentLink";
import Footer from "#/ui/landing/footer";
import { getProjectConfigurationLink, getProjectLink } from "./getProjectLink";
import { useFeatureFlags } from "#/lib/feature-flags";
import { getDatasetsLink } from "./datasets/[dataset]/getDatasetLink";
import { getProjectLogsLink } from "./logs/getProjectLogsLink";
import {
  BodyWrapper,
  HEIGHT_WITH_DOUBLE_TOP_OFFSET,
  HEIGHT_WITH_TOP_OFFSET,
} from "#/app/app/body-wrapper";
import { useIsSidenavDocked } from "../../sidenav-state";

export const ProjectNavItem = ({
  href,
  active,
  className,
  activeClassName,
  children,
  onClick,
}: {
  href: string;
  active?: boolean;
  className?: string;
  activeClassName?: string;
  children: React.ReactNode;
  onClick?: () => void;
}) => {
  return (
    <Link
      prefetch
      href={href}
      className={cn(
        buttonVariants({
          variant: "ghost",
          size: "xs",
        }),
        "px-2 gap-1.5 justify-start text-primary-600",
        className,
        {
          [`text-primary-950 bg-primary-200 pointer-events-none ${activeClassName}`]:
            active,
        },
      )}
      onClick={onClick}
    >
      {children}
    </Link>
  );
};

export const ProjectListLayout = ({
  children,
  orgName,
  projectName,
  active,
  scrollContainerRef,
}: React.PropsWithChildren<{
  orgName: string;
  projectName: string;
  active?:
    | "playgrounds"
    | "experiments"
    | "datasets"
    | "prompts"
    | "scorers"
    | "tools"
    | "agents"
    | "configuration";
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
}>) => {
  const { flags, isLoading } = useFeatureFlags();
  const agentsEnabled = !isLoading && flags.agents;

  const isSidenavDocked = useIsSidenavDocked();

  const projectLink = getProjectLink({
    orgName,
    projectName,
  });
  return (
    <MainContentWrapper
      className={cn(
        "flex flex-col overflow-hidden p-0",
        HEIGHT_WITH_TOP_OFFSET,
      )}
      hideFooter
    >
      {!isSidenavDocked && (
        <div className="no-scrollbar flex flex-none gap-1.5 overflow-x-auto px-3 pb-2 pt-1 bg-primary-50">
          <ProjectNavItem
            href={getPlaygroundsLink({
              orgName,
              projectName,
            })}
            active={active === "playgrounds"}
          >
            <Shapes className="size-3" />
            Playgrounds
          </ProjectNavItem>
          <ProjectNavItem
            href={getExperimentsLink({
              orgName,
              projectName,
            })}
            active={active === "experiments"}
          >
            <Beaker className="size-3" />
            Experiments
          </ProjectNavItem>
          <ProjectNavItem
            href={getDatasetsLink({
              orgName,
              projectName,
            })}
            active={active === "datasets"}
          >
            <Database className="size-3" />
            Datasets
          </ProjectNavItem>

          <ProjectNavItem
            href={`${projectLink}/prompts`}
            active={active === "prompts"}
          >
            <MessageCircle className="size-3" />
            Prompts
          </ProjectNavItem>

          <ProjectNavItem
            href={`${projectLink}/scorers`}
            active={active === "scorers"}
          >
            <Percent className="size-3" />
            Scorers
          </ProjectNavItem>

          <ProjectNavItem
            href={`${projectLink}/tools`}
            active={active === "tools"}
          >
            <Bolt className="size-3" />
            Tools
          </ProjectNavItem>

          {agentsEnabled && (
            <ProjectNavItem
              href={`${projectLink}/agents`}
              active={active === "agents"}
            >
              <Route className="size-3" />
              Agents
            </ProjectNavItem>
          )}
          <ProjectNavItem
            href={getProjectConfigurationLink({
              orgName,
              projectName,
            })}
            activeClassName="bg-primary-100"
            active={active === "configuration"}
          >
            <Settings2 className="size-3" />
            Configuration
          </ProjectNavItem>
          <div className="flex flex-1 justify-end">
            <Link
              href={getProjectLogsLink({
                orgName,
                projectName,
              })}
              className={cn(
                buttonVariants({
                  size: "xs",
                }),
              )}
            >
              <Activity className="size-3 text-primary-600" />
              Logs
              <ArrowUpRight className="size-3 text-primary-400" />
            </Link>
          </div>
        </div>
      )}
      <BodyWrapper
        innerClassName="flex"
        outerClassName={cn({
          [HEIGHT_WITH_DOUBLE_TOP_OFFSET]: !isSidenavDocked,
          [HEIGHT_WITH_TOP_OFFSET]: isSidenavDocked,
        })}
      >
        <div
          className="relative flex w-full flex-1 flex-col overflow-auto px-3"
          ref={scrollContainerRef}
        >
          {children}
          <div className="grow" />
          <Footer
            className="sticky left-0 w-full pb-4 sm:pb-4 lg:pb-4"
            inApp
            orgName={orgName}
          />
        </div>
      </BodyWrapper>
    </MainContentWrapper>
  );
};
