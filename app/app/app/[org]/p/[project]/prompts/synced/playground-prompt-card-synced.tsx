import { <PERSON><PERSON> } from "#/ui/button";
import { BasicTooltip } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { type ModelSpec } from "@braintrust/proxy/schema";
import {
  Blend,
  CopyIcon,
  Fullscreen,
  GripVertical,
  Minimize,
  Trash,
} from "lucide-react";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";

import "@xyflow/react/dist/style.css";
import { Skeleton } from "#/ui/skeleton";
import { type ModelDetails } from "#/ui/prompts/models";
import { toast } from "sonner";
import { type PlaygroundCopilotContext } from "#/ui/copilot/playground";
import { CopyAIProxyCode } from "../copy-ai-proxy-code";
import { type SavedPromptMeta } from "../../playgrounds/[playground]/use-saved-prompt-meta";
import {
  EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
  EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
} from "#/ui/charts/colors";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { AgentCanvas } from "../agent-canvas";
import { ReactFlowProvider } from "@xyflow/react";
import { type TransactionId } from "@braintrust/core";
import { useSyncedPrompts } from "./use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import { RemoteEvalForm } from "../remote-evals";
import { useGlobalChat } from "#/ui/optimization/use-global-chat-context";
import {
  type TaskEditConfirmationData,
  useOptimizationContext,
} from "#/utils/optimization/provider";
import { PromptDiff } from "../prompt-diff";
import { useIsLoopEnabled } from "#/ui/optimization/optimization-chat";
import OptimizePromptButton from "./optimize-prompt-button";
import { type TextEditorProps } from "#/ui/text-editor";
import {
  type PromptExtensionsParams,
  usePromptExtensions,
  type JSONStructure,
} from "#/ui/prompts/hooks";
import { PlaygroundPromptBlock } from "./playground-prompt-block";
import { PlaygroundScorerBlock } from "./playground-scorer-block";

export const PlaygroundPromptCardSynced = memo(
  ({
    isReadOnly,
    index,
    modelOptionsByProvider,
    deletePrompt,
    isDeleteDisabled,
    orgName,
    projectId,
    allAvailableModels,
    onRunPrompts,
    promptId,
    savedPromptMeta,
    copilotContext,
    isSortable,
    isFullscreen,
    setFullscreen,
    datasetId,
    diffId,
    datasetJsonStructure,
    promptExtensionsParams: promptExtensionsParamsProp,
    rowData,
    extraMessagesPath,
    showNoConfiguredSecretsMessage,
  }: {
    isReadOnly?: boolean;
    index: number;
    modelOptionsByProvider: Record<string, ModelDetails[]>;
    deletePrompt: (id: string) => Promise<TransactionId | null>;
    isDeleteDisabled?: boolean;
    isSortable?: boolean;
    orgName: string;
    projectId?: string;
    extensions?: TextEditorProps["extensions"];
    allAvailableModels: Record<string, ModelSpec>;
    onRunPrompts: VoidFunction;
    promptId: string;
    savedPromptMeta: Record<string, SavedPromptMeta | undefined>;
    copilotContext?: PlaygroundCopilotContext;
    isFullscreen?: boolean;
    setFullscreen: (index: number | null) => void;
    datasetId?: string;
    diffId?: string;
    datasetJsonStructure: JSONStructure;
    promptExtensionsParams: PromptExtensionsParams;
    rowData?: Record<string, unknown>;
    extraMessagesPath?: string | null;
    showNoConfiguredSecretsMessage: boolean;
  }) => {
    const { duplicateEditor_ROOT, sortedSyncedPromptsAtom_ROOT } =
      useSyncedPrompts();
    const isLoopEnabled = useIsLoopEnabled();

    const {
      setCurrentSessionContextObjects,
      setIsChatOpen,
      setIsAwaitingEditConfirmation,
      editTaskConfirmationData,
      setEditTaskConfirmationData,
      handleSendMessage,
    } = useGlobalChat();

    const syncedPromptAtom = useMemo(
      () =>
        atom((get) =>
          get(sortedSyncedPromptsAtom_ROOT).find((p) => p.id === promptId),
        ),
      [sortedSyncedPromptsAtom_ROOT, promptId],
    );
    const {
      prompt_data: promptData,
      function_data: functionData,
      function_type: functionType,
    } = useAtomValue(syncedPromptAtom) ?? {};
    const currentModel = promptData?.options?.model;

    const containerRef = useRef<HTMLDivElement>(null);

    const { attributes, listeners, setNodeRef, transform, transition } =
      useSortable({ id: promptId, disabled: !isSortable });

    const draggingStyle = {
      transform: CSS.Transform.toString({
        x: transform?.x ?? 0,
        y: transform?.y ?? 0,
        scaleX: 1,
        scaleY: 1,
      }),
      transition,
    };

    const setFullscreenAtIndex = useCallback(
      (f: boolean) => setFullscreen(f ? index : null),
      [setFullscreen, index],
    );

    const isAgent = functionData?.type === "graph";
    const remoteEval =
      functionData?.type === "remote_eval" ? functionData : undefined;
    const isScorer = functionType === "scorer";

    const shouldIncludeLoopFix = isLoopEnabled && !remoteEval;
    const onFixWithLoop = useCallback(
      (message: string) => {
        handleSendMessage(
          {
            id: crypto.randomUUID(),
            type: "user_message",
            message: message,
          },
          {
            clearContextObjects: false,
            clearUserMessage: false,
          },
        );
        setTimeout(() => {
          setIsChatOpen(true);
        }, 250);
      },
      [handleSendMessage, setIsChatOpen],
    );

    const promptExtensionsParams = useMemo(() => {
      return {
        ...promptExtensionsParamsProp,
        onFixWithLoop: shouldIncludeLoopFix ? onFixWithLoop : undefined,
      };
    }, [promptExtensionsParamsProp, shouldIncludeLoopFix, onFixWithLoop]);
    const { extensions: promptExtensions } = usePromptExtensions(
      promptExtensionsParams,
    );

    const savedPromptMetaHasLoaded = useMemo(() => {
      // HACK: if there's an origin and no meta, show a loading state
      const hasOrigin = !!promptData?.origin?.prompt_id;
      if (!hasOrigin || isAgent || isScorer) return true;
      return hasOrigin && !!currentModel;
    }, [promptData?.origin?.prompt_id, isAgent, isScorer, currentModel]);

    const [isDeleting, setIsDeleting] = useState(false);
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);

    const {
      registerTaskEditConfirmationHandler,
      unregisterTaskEditConfirmationHandler,
    } = useOptimizationContext();

    useEffect(() => {
      if (
        isReadOnly ||
        !registerTaskEditConfirmationHandler ||
        !unregisterTaskEditConfirmationHandler
      ) {
        return;
      }

      const handler = (data: TaskEditConfirmationData) => {
        setEditTaskConfirmationData(data);
        //can likely just create a map of taskIndex -> isAwaitingEditConfirmation globally.
        setIsAwaitingEditConfirmation(true);
      };

      registerTaskEditConfirmationHandler(index, handler);

      // Cleanup function
      return () => {
        unregisterTaskEditConfirmationHandler(index);
      };
    }, [
      index,
      isReadOnly,
      registerTaskEditConfirmationHandler,
      unregisterTaskEditConfirmationHandler,
      setEditTaskConfirmationData,
      setIsAwaitingEditConfirmation,
    ]);

    if ((isDeleting || !savedPromptMetaHasLoaded) && !isReadOnly) {
      return (
        <div className="flex min-w-96 flex-1">
          <Skeleton className="flex-1" />
        </div>
      );
    }

    const swatchClassName = [
      EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
      ...EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
    ][index];

    const hasEditConfirmationData =
      editTaskConfirmationData?.type === "edit_task" &&
      editTaskConfirmationData.index === index;

    const originPromptId = promptData?.origin?.prompt_id;
    const originPromptVersion = promptData?.origin?.prompt_version;

    const rowKey = `${originPromptId}-${originPromptVersion}`;

    return (
      <div
        ref={(r) => {
          containerRef.current = r;
          setNodeRef(r);
        }}
        id={`playground-prompt-${index}`}
        {...attributes}
        className="group/promptblock flex min-w-96 flex-1 cursor-auto flex-col rounded-md border transition-colors bg-primary-50 focus-within:border-primary-200 hover:border-primary-300"
        style={draggingStyle}
      >
        <div className="relative flex flex-none gap-1 px-2 py-1">
          <div
            className={cn(
              "absolute inset-0 pointer-events-none opacity-10 rounded-t-[5px]",
              swatchClassName,
            )}
          />
          <div className="flex flex-1 items-center gap-2 truncate">
            <div
              className={cn(
                "rounded-full size-3 bg-primary-500 ml-1",
                swatchClassName,
              )}
            />
            <div className="flex-none text-xs text-primary-500">
              {index > 0 ? "Comparison task" : "Base task"}
            </div>
          </div>
          {!isReadOnly && (
            <div className="flex gap-0.5">
              <CopyAIProxyCode
                promptData={promptData}
                className="opacity-0 transition-opacity group-hover/promptblock:opacity-100 group-focus-visible/promptblock:opacity-100"
              />
              {!isFullscreen && (
                <>
                  <BasicTooltip tooltipContent="Duplicate task">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-7 opacity-0 transition-opacity text-primary-500 group-hover/promptblock:opacity-100 group-focus-visible/promptblock:opacity-100"
                      onClick={() => duplicateEditor_ROOT({ id: promptId })}
                      Icon={CopyIcon}
                    />
                  </BasicTooltip>
                  <BasicTooltip tooltipContent="Fullscreen task">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-7 opacity-0 transition-opacity text-primary-500 group-hover/promptblock:opacity-100 group-focus-visible/promptblock:opacity-100"
                      onClick={() => setFullscreen(index)}
                      Icon={Fullscreen}
                    />
                  </BasicTooltip>
                  {!isDeleteDisabled && (
                    <BasicTooltip tooltipContent="Remove task">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="size-7 opacity-0 transition-opacity text-primary-500 group-hover/promptblock:opacity-100 group-focus-visible/promptblock:opacity-100"
                        onClick={(e) => {
                          e.preventDefault();
                          setIsConfirmingDelete(true);
                        }}
                        Icon={Trash}
                      />
                    </BasicTooltip>
                  )}
                  {isLoopEnabled && (
                    <OptimizePromptButton
                      promptId={promptId}
                      currentModel={currentModel ?? ""}
                      promptMeta={savedPromptMeta[promptId]}
                      promptData={promptData}
                      functionData={functionData}
                      index={index}
                      handleSendMessage={handleSendMessage}
                      setIsChatOpen={setIsChatOpen}
                      setCurrentSessionContextObjects={
                        setCurrentSessionContextObjects
                      }
                      containerRef={containerRef}
                    />
                  )}
                </>
              )}
              {!isFullscreen && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="size-7 cursor-grab opacity-0 transition-opacity text-primary-500 group-hover/promptblock:opacity-100 group-focus-visible/promptblock:opacity-100"
                  Icon={GripVertical}
                  {...listeners}
                />
              )}
            </div>
          )}
          {isFullscreen && (
            <BasicTooltip tooltipContent="Exit fullscreen">
              <Button
                size="icon"
                variant="inverted"
                className="size-7"
                Icon={Minimize}
                onClick={() => setFullscreen(null)}
              />
            </BasicTooltip>
          )}
        </div>
        {isScorer ? (
          <PlaygroundScorerBlock
            isReadOnly={isReadOnly}
            promptData={promptData}
            functionData={functionData}
            onRunPrompts={onRunPrompts}
            savedPromptMeta={savedPromptMeta}
            copilotContext={copilotContext}
            containerRef={containerRef}
            orgName={orgName}
            promptId={promptId}
            datasetId={datasetId}
            projectId={projectId}
            promptExtensionsParams={promptExtensionsParams}
            index={index}
            rowData={rowData}
          />
        ) : isAgent ? (
          <ReactFlowProvider>
            <AgentCanvas
              containerRef={containerRef}
              id={promptId}
              functionData={functionData}
              origin={promptData?.origin}
              orgName={orgName}
              modelOptionsByProvider={modelOptionsByProvider}
              allAvailableModels={allAvailableModels}
              copilotContext={copilotContext}
              isFullscreen={isFullscreen ?? false}
              setFullscreen={setFullscreenAtIndex}
              savedPromptMeta={savedPromptMeta}
              datasetJsonStructure={datasetJsonStructure}
              showNoConfiguredSecretsMessage={showNoConfiguredSecretsMessage}
            />
          </ReactFlowProvider>
        ) : remoteEval ? (
          <RemoteEvalForm
            containerRef={containerRef}
            id={promptId}
            functionData={remoteEval}
            orgName={orgName}
            modelOptionsByProvider={modelOptionsByProvider}
            allAvailableModels={allAvailableModels}
            copilotContext={copilotContext}
            extensions={promptExtensions}
            isFullscreen={isFullscreen ?? false}
            setFullscreen={setFullscreenAtIndex}
            showNoConfiguredSecretsMessage={showNoConfiguredSecretsMessage}
          />
        ) : !hasEditConfirmationData ? (
          <PlaygroundPromptBlock
            showNoConfiguredSecretsMessage={showNoConfiguredSecretsMessage}
            isReadOnly={isReadOnly}
            index={index}
            orgName={orgName}
            projectId={projectId}
            promptId={promptId}
            promptData={promptData}
            modelOptionsByProvider={modelOptionsByProvider}
            allAvailableModels={allAvailableModels}
            onRunPrompts={onRunPrompts}
            savedPromptMeta={savedPromptMeta}
            copilotContext={copilotContext}
            containerRef={containerRef}
            diffId={diffId}
            datasetId={datasetId}
            extensions={promptExtensions}
            rowData={rowData}
            extraMessagesPath={extraMessagesPath}
          />
        ) : (
          <div className="flex h-full flex-col overflow-hidden">
            <div className="flex flex-none items-center gap-2 border-b px-3 py-2">
              <h3 className="flex flex-1 items-center gap-2 text-xs font-medium">
                <Blend className="size-3" />
                Suggested change
              </h3>
              <Button
                variant="ghost"
                size="xs"
                className="text-primary-600"
                onClick={() => {
                  editTaskConfirmationData?.onCancel(undefined, {
                    continueChat: false,
                  });
                  setEditTaskConfirmationData(null);
                  setIsAwaitingEditConfirmation(false);
                }}
              >
                Skip
              </Button>
              <Button
                variant="primary"
                size="xs"
                onClick={() => {
                  editTaskConfirmationData?.onConfirm({
                    continueChat: false,
                  });
                  setEditTaskConfirmationData(null);
                  setIsAwaitingEditConfirmation(false);
                }}
              >
                Accept
              </Button>
            </div>
            <div className="flex-1 overflow-y-auto px-4 py-2">
              <PromptDiff
                promptData={editTaskConfirmationData.newDefinition}
                diffPromptData={editTaskConfirmationData.originalDefinition}
                rowId={rowKey}
                hideDiffDisclaimer
                hideLineNumbers
              />
            </div>
          </div>
        )}
        {isConfirmingDelete && (
          <ConfirmationDialog
            open={isConfirmingDelete}
            onOpenChange={setIsConfirmingDelete}
            title="Remove task"
            description="Are you sure you want to remove this task from the playground? Unsaved tasks will be discarded."
            confirmText="Remove"
            onConfirm={async () => {
              try {
                setIsDeleting(true);
                await deletePrompt(promptId);
              } catch (e) {
                toast.error(
                  "There was a problem removing the task. Please refresh your browser.",
                );
                console.error(e);
                setIsDeleting(false);
              }
            }}
          />
        )}
      </div>
    );
  },
);
PlaygroundPromptCardSynced.displayName = "PlaygroundPromptCardSynced";
