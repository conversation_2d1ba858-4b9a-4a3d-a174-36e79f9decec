import { Field, Schema, Utf8 } from "apache-arrow";
import { createContext } from "react";
import {
  type ProjectContextExperiment,
  type ProjectContextDataset,
} from "./project-actions";
import { type ProjectConfig } from "#/utils/score-config";
import {
  type Permission,
  type ProjectSettings,
} from "@braintrust/core/typespecs";
import { builtInMetrics } from "@braintrust/local/api-schema";

// Mirrors the ProjectContextExperiment type defined in
// app/app/app/[org]/p/[project]/project-actions.ts.
export const emptyExperimentSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "name", type: new Utf8() }),
  Field.new({ name: "created", type: new Utf8() }),
  Field.new({ name: "project_id", type: new Utf8() }),
  Field.new({ name: "user", type: new Utf8() }),
  Field.new({ name: "repo_info", type: new Utf8() }),
  Field.new({ name: "metadata", type: new Utf8() }),
  Field.new({ name: "description", type: new Utf8() }),
  Field.new({ name: "dataset", type: new Utf8() }),
  Field.new({ name: "tags", type: new Utf8() }),
  // Added manually inside app/app/app/[org]/p/[project]/layout.tsx.
  Field.new({ name: "search_text", type: new Utf8() }),
]);

// Mirrors the ProjectContextDataset type defined in
// app/app/app/[org]/p/[project]/project-actions.ts.
export const emptyDatasetSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "name", type: new Utf8() }),
  Field.new({ name: "description", type: new Utf8() }),
  Field.new({ name: "metadata", type: new Utf8() }),
  Field.new({ name: "created", type: new Utf8() }),
  Field.new({ name: "project_id", type: new Utf8() }),
]);

export interface ProjectContextT {
  orgName: string;

  projectId: string | null;
  projectName: string;
  projectSettings: ProjectSettings | null;
  projectPermissions: Permission[];
  mutateProject: () => Promise<void>;

  experimentsReady: number;
  isExperimentsLoading: boolean;
  experimentsTable: string | null;
  experiments: ProjectContextExperiment[];
  mutateExperiments: () => Promise<void>;

  datasetsReady: number;
  datasetsTable: string | null;
  orgDatasets: ProjectContextDataset[];
  projectDatasets: ProjectContextDataset[];
  mutateDatasets: () => Promise<void>;
  mutateProjectDatasets: () => Promise<void>;

  config: ProjectConfig;
  isConfigLoading: boolean;
  mutateConfig: () => Promise<void>;
}

export const DEFAULT_CONFIG: ProjectConfig = {
  automations: [],
  scores: [],
  tags: [],
  span_iframes: [],
  metricDefinitions: builtInMetrics,
};

export const ProjectContext = createContext<ProjectContextT>({
  orgName: "",

  projectId: null,
  projectName: "",
  projectSettings: null,
  projectPermissions: [],
  mutateProject: () => Promise.resolve(),

  experimentsReady: 0,
  isExperimentsLoading: true,
  experimentsTable: null,
  experiments: [],
  mutateExperiments: () => Promise.resolve(),

  datasetsReady: 0,
  datasetsTable: null,
  orgDatasets: [],
  projectDatasets: [],
  mutateDatasets: () => Promise.resolve(),
  mutateProjectDatasets: () => Promise.resolve(),

  config: DEFAULT_CONFIG,
  isConfigLoading: true,
  mutateConfig: () => Promise.resolve(),
});
