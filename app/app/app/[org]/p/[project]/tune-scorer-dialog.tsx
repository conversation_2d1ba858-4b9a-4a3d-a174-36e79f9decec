import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON>Tit<PERSON> } from "#/ui/dialog";
import { <PERSON><PERSON> } from "#/ui/button";
import { ScorerDropdown } from "#/ui/single-select-scorer-dropdown";
import { Info, Percent } from "lucide-react";
import { useState, useMemo } from "react";
import { type SavedScorer } from "#/utils/scorers";
import { useScorerFunctions } from "#/app/app/[org]/prompt/[prompt]/scorers/open";
import { type UIFunction } from "#/ui/prompts/schema";
import { AutoEvalMap } from "#/app/app/[org]/prompt/[prompt]/scorers/scorers-dropdown";
import { FunctionDialogHeaderMetadata } from "#/ui/prompts/function-editor/function-dialog-header";
import { prettifyXact } from "@braintrust/core";
import { TransactionIdField } from "#/utils/duckdb";
import { DEFAULT_MODEL as DEFAULT_AUTOEVALS_MODEL } from "autoevals";
import { newPrompt } from "#/ui/prompts/schema";
import { slugify } from "#/utils/slug";
import { Skeleton } from "#/ui/skeleton";
import { PromptEditorSynced } from "../../prompt/[prompt]/prompt-editor-synced";
import { useAvailableModels } from "#/ui/prompts/models";
import { useOrg } from "#/utils/user";
import TextArea from "#/ui/text-area";
import { Label } from "#/ui/label";
import {
  useGlobalChat,
  type DatasetContextObject,
  type ScorerContextObject,
} from "#/ui/optimization/use-global-chat-context";
import { RadioGroup, RadioGroupItem } from "#/ui/radio";
import { cn } from "#/utils/classnames";
import { Switch } from "#/ui/switch";
import { useOptimizationContext } from "#/utils/optimization/provider";
import { BasicTooltip } from "#/ui/tooltip";
import { CodeEditor } from "#/ui/prompts/function-editor/code-editor";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { pluralizeWithCount } from "#/utils/plurals";
import { Slider } from "#/ui/slider";
import { Input } from "#/ui/input";

// Zod schema for form validation
const tuneScorerFormSchema = z.object({
  scorer: z
    .union([
      z.object({
        id: z.string(),
        type: z.literal("function"),
      }),
      z.object({
        name: z.string(),
        type: z.literal("global"),
      }),
    ])
    .nullable()
    // Make scorer optional and catch the error in the form submit.
    .optional(),
  selectedChoice: z
    .string()
    .min(
      1,
      "Please select a target classification or describe the target outcome",
    ),
  feedback: z.string().optional().default(""),
  tuneAsNewScorer: z.boolean().default(false),
  allowRunningWithoutConsent: z.boolean().default(false),
});

type TuneScorerFormData = z.infer<typeof tuneScorerFormSchema>;

interface TuneScorerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectName: string;
  projectId: string;
  onTune?: (scorerId: string, scorerFunction?: UIFunction) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  rowsToTuneScorer: any[];
  savedScorers?: SavedScorer[];
  scorerFunctions?: Record<string, UIFunction>;
}

export const TuneScorerDialog = ({
  open,
  onOpenChange,
  projectName,
  projectId,
  rowsToTuneScorer,
  savedScorers,
}: TuneScorerDialogProps) => {
  const { handleSendMessage, setIsChatOpen } = useGlobalChat();
  const { allowRunningWithoutConsent, setAllowRunningWithoutConsent } =
    useOptimizationContext();

  const [showScorerDetails, setShowScorerDetails] = useState(false);

  // Fetch functions (scorers) data - use passed functions if available
  const { functions: fetchedFunctions } = useScorerFunctions({});
  const functions = fetchedFunctions;

  // Get organization context
  const org = useOrg();
  const orgName = org?.name || "";

  // Get available models
  const { configuredModelsByProvider, allAvailableModels } = useAvailableModels(
    { orgName },
  );

  const autoSelectedScorer = useMemo(() => {
    // If row's contain scores (that's been run), use that
    if (
      functions &&
      Object.keys(functions).length > 0 &&
      rowsToTuneScorer.length > 0
    ) {
      const firstRowScores = rowsToTuneScorer[0]?.scores;
      if (firstRowScores && typeof firstRowScores === "object") {
        const scorerNames = Object.keys(firstRowScores);
        for (const scorerName of scorerNames) {
          const matchingFunction = Object.entries(functions).find(
            ([, func]) => func.name === scorerName,
          );

          if (matchingFunction) {
            const [functionId] = matchingFunction;
            const scorer: SavedScorer = {
              id: functionId,
              type: "function",
            };
            return scorer;
          }

          const globalScorer: SavedScorer = {
            name: scorerName,
            type: "global",
          };
          return globalScorer;
        }
      }
    }

    // if no scores in the rows but in the playground, use that.
    if (savedScorers && savedScorers.length > 0) {
      return savedScorers[0];
    }

    return null;
  }, [functions, rowsToTuneScorer, savedScorers]);

  const form = useForm<TuneScorerFormData>({
    resolver: zodResolver(tuneScorerFormSchema),
    defaultValues: {
      scorer: autoSelectedScorer || undefined,
      selectedChoice: "",
      feedback: "",
      tuneAsNewScorer: false,
      allowRunningWithoutConsent: allowRunningWithoutConsent,
    },
  });

  const watchedScorer = form.watch("scorer");
  const activeScorer = watchedScorer || autoSelectedScorer;

  const selectedScorerFunction = useMemo(() => {
    if (!activeScorer) return null;

    const scorer = activeScorer;
    if (scorer.type === "function" && "id" in scorer) {
      return functions?.[scorer.id];
    }

    if (scorer.type === "global" && "name" in scorer) {
      const autoEval = AutoEvalMap[scorer.name];
      if (!autoEval) return null;

      const globalScorerFunction: UIFunction = {
        ...newPrompt(projectId),
        id: `global_${scorer.name}`,
        name: scorer.name,
        slug: slugify(scorer.name),
        description: autoEval.description,
        metadata: undefined,
        function_data: {
          type: "global",
          name: scorer.name,
        },
        function_type: "scorer",
        prompt_data: autoEval.template
          ? {
              prompt: {
                type: "chat",
                messages: [
                  {
                    role: "user",
                    content: autoEval.template.prompt,
                  },
                ],
              },
              options: {
                model: DEFAULT_AUTOEVALS_MODEL,
                params: { temperature: 0 },
              },
              parser: {
                type: "llm_classifier",
                use_cot: true,
                choice_scores: autoEval.template.choice_scores,
              },
            }
          : undefined,
      };

      return globalScorerFunction;
    }

    return null;
  }, [activeScorer, functions, projectId]);

  const updateScorers = async (scorer: SavedScorer) => {
    form.setValue("scorer", scorer);
    form.setValue("selectedChoice", "");
    form.clearErrors();
    return Promise.resolve(null);
  };

  const versionNumberText = prettifyXact(
    selectedScorerFunction?.[TransactionIdField] ?? "0",
  );
  const renderedVersionNumber = selectedScorerFunction?.[TransactionIdField] ? (
    versionNumberText
  ) : activeScorer?.type === "global" ? (
    "Global"
  ) : (
    <Skeleton className="h-[18px] w-[116px]" />
  );

  const handleClose = () => {
    onOpenChange(false);
    form.reset();
  };

  const onSubmit = (data: TuneScorerFormData) => {
    // Ensure we have a scorer (either from form or autoSelected)
    if (!activeScorer) {
      form.setError("scorer", {
        type: "manual",
        message: "Please select a scorer",
      });
      return;
    }

    const scorerIdentifier =
      activeScorer.type === "global"
        ? `global scorer "${activeScorer.name}"`
        : `scorer [${selectedScorerFunction?.id}]`;

    handleSendMessage(
      {
        id: crypto.randomUUID(),
        type: "user_message",
        message: `Improve the tagged ${scorerIdentifier} based on the following instructions:
        \n${"-----"}\nLook at the tagged example dataset rows the user provided. ${
          selectedScorerFunction?.prompt_data?.parser?.choice_scores
            ? `The tuned scorer should accurately classify the example rows as **${data.selectedChoice}**. Make sure to get a maximum of 3~5 few shot examples that represent a mix of classifications from both user-provided examples and playground results.`
            : `The tuned scorer should achieve the following outcome: ${data.selectedChoice}.`
        }${data.tuneAsNewScorer || activeScorer?.type === "global" ? `\nCreate a new scorer with the new implementation instead of editing the existing one. ` : ""}Verify that the tuned scorer is working as expected and continue to improve it if possible.\n${"-----"} ${data.feedback && data.feedback.length > 0 ? `\n\nHere is some additional feedback from the user:\n${data.feedback}` : ""}`,
        contextObjects: {
          ...(selectedScorerFunction && {
            scorer: {
              id: selectedScorerFunction.id,
              resource: "scorer",
              name: selectedScorerFunction.name || "Unnamed scorer",
              description: `Scorer function: ${selectedScorerFunction.name || "Unnamed scorer"}`,
            },
          }),
          ...rowsToTuneScorer.reduce(
            (
              acc: Record<string, DatasetContextObject | ScorerContextObject>,
              row,
              index,
            ) => {
              if (row) {
                acc[`row_${index}`] = {
                  id: row.id,
                  resource: "dataset",
                  name: row.input || "",
                  datasetId: row.id,
                  input: row.input,
                  output: row.output,
                  expected: row.expected,
                  metadata: row.metadata,
                  tags: row.tags,
                };
              }
              return acc;
            },
            {},
          ),
        },
      },
      {
        clearContextObjects: false,
        clearUserMessage: false,
      },
    );
    handleClose();

    setTimeout(() => {
      setIsChatOpen(true);
    }, 250);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl gap-0 p-0 sm:max-w-3xl">
        <DialogHeader className="mb-6 px-4 pt-4">
          <DialogTitle>
            Tune scorer based on{" "}
            {pluralizeWithCount(
              rowsToTuneScorer.length,
              "selected row",
              "selected rows",
            )}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="relative flex w-full flex-col gap-6 overflow-hidden px-4 pb-4">
              <div className="flex flex-1 flex-col">
                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name="scorer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center text-xs font-medium">
                          Choose scorer
                        </FormLabel>
                        <FormControl>
                          <div className="flex">
                            <ScorerDropdown
                              projectName={projectName}
                              projectId={projectId}
                              selectedScorer={activeScorer}
                              updateScorers={updateScorers}
                              functions={functions || {}}
                            >
                              <Button
                                variant="border"
                                size="xs"
                                className={cn(
                                  "w-fit border-r-0 rounded-r-none",
                                  activeScorer
                                    ? "border-r-0"
                                    : "border-r-1 rounded-r-md",
                                )}
                                id="scorer-button"
                                isDropdown
                                Icon={activeScorer ? Percent : undefined}
                                type="button"
                              >
                                {activeScorer
                                  ? activeScorer.type === "function"
                                    ? functions[activeScorer.id]?.name ||
                                      activeScorer.id
                                    : activeScorer.name
                                  : "Scorer"}
                              </Button>
                            </ScorerDropdown>
                            {activeScorer && (
                              <Button
                                variant="border"
                                size="xs"
                                className="border-l-1 w-fit rounded-l-none text-primary-700"
                                onClick={() =>
                                  setShowScorerDetails(!showScorerDetails)
                                }
                                type="button"
                              >
                                {showScorerDetails
                                  ? "Hide details"
                                  : "View details"}
                              </Button>
                            )}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {selectedScorerFunction && showScorerDetails && (
                  <div className="mt-4 flex w-full flex-col rounded-md">
                    <div className="-mb-4 flex flex-col pb-2">
                      <div className="flex items-center gap-2 ">
                        <Percent className="size-3 text-lime-600" />
                        <span className="text-sm font-medium">
                          {selectedScorerFunction.name}
                        </span>
                      </div>
                      {activeScorer?.type !== "global" && (
                        <div className="flex flex-wrap items-center gap-x-1 gap-y-0">
                          <FunctionDialogHeaderMetadata
                            hasSavedVersions={true}
                            idText={selectedScorerFunction.id}
                            versionNumberText={""}
                            sourcePrompt={selectedScorerFunction}
                            renderedId={selectedScorerFunction.id}
                            renderedVersionNumber={renderedVersionNumber}
                            className="-ml-2 mr-0 w-fit flex-nowrap"
                          />
                          {selectedScorerFunction.slug && (
                            <CopyToClipboardButton
                              size="xs"
                              variant="ghost"
                              copyMessage="Copy Slug to clipboard"
                              textToCopy={selectedScorerFunction.slug}
                              className="font-mono text-xs font-medium text-primary-500"
                            >
                              <span className="font-inter text-[10px] uppercase tracking-wider opacity-60">
                                SLUG
                              </span>
                              <span className="max-w-[160px] truncate">
                                {selectedScorerFunction.slug}
                              </span>
                            </CopyToClipboardButton>
                          )}
                        </div>
                      )}
                      {selectedScorerFunction.prompt_data && (
                        <div className="mt-4">
                          <PromptEditorSynced
                            isReadOnly={true}
                            orgName={orgName}
                            modelOptionsByProvider={configuredModelsByProvider}
                            allAvailableModels={allAvailableModels}
                            promptData={selectedScorerFunction.prompt_data}
                            promptId={selectedScorerFunction.id}
                            onRun={() => {}} // Empty function since we're read-only
                            showNoConfiguredSecretsMessage={false}
                          />
                        </div>
                      )}

                      {selectedScorerFunction.function_data.type === "code" &&
                        selectedScorerFunction.function_data.data.type ===
                          "inline" && (
                          <div className="mt-4">
                            <CodeEditor
                              className="rounded-md border bg-primary-50"
                              savedCode={
                                selectedScorerFunction.function_data.data.code
                              }
                              language={
                                selectedScorerFunction.function_data.data
                                  .runtime_context.runtime === "node"
                                  ? "ts"
                                  : "py"
                              }
                              readOnly={true}
                              hideBanner
                            />
                          </div>
                        )}
                    </div>
                  </div>
                )}
              </div>
              {
                //This is only going to be available for LLM judge created in the UI
                selectedScorerFunction?.prompt_data?.parser?.choice_scores && (
                  <FormField
                    control={form.control}
                    name="selectedChoice"
                    render={({ field }) => (
                      <FormItem className="flex w-full flex-col">
                        <div className="flex flex-col">
                          <FormLabel className="flex items-center text-xs font-medium">
                            Target classification
                          </FormLabel>
                          <FormDescription>
                            Loop will optimize the scorer to better align
                            classification to this target for the selected rows
                          </FormDescription>
                        </div>
                        <FormControl>
                          <RadioGroup
                            value={field.value}
                            onValueChange={field.onChange}
                            className="max-w-sm"
                          >
                            {Object.entries(
                              selectedScorerFunction.prompt_data!.parser!
                                .choice_scores,
                            ).map(([label, value]) => (
                              <div
                                key={label}
                                className="flex w-full items-center space-x-2"
                              >
                                <RadioGroupItem value={label} id={label} />
                                <Label
                                  htmlFor={label}
                                  className="flex w-full cursor-pointer gap-2 text-xs font-normal"
                                >
                                  <div className="w-10 truncate rounded-md py-1 text-start font-medium text-primary-900">
                                    {value * 100}%
                                  </div>
                                  <div className="flex-1 rounded-md px-2 py-1 text-primary-700">
                                    {label}
                                  </div>
                                </Label>
                              </div>
                            ))}
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )
              }
              {
                // For code-based scorers or LLM judges without choice_scores
                selectedScorerFunction &&
                  !selectedScorerFunction?.prompt_data?.parser
                    ?.choice_scores && (
                    <FormField
                      control={form.control}
                      name="selectedChoice"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel className="flex items-center text-xs font-medium">
                            Target outcome
                          </FormLabel>
                          <FormControl>
                            <div className="flex max-w-xs items-center gap-2">
                              <div className="relative flex w-18 flex-none items-center">
                                <Input
                                  ref={field.ref}
                                  name={field.name}
                                  onBlur={field.onBlur}
                                  disabled={field.disabled}
                                  value={String(
                                    Math.round(Number(field.value) * 100),
                                  )}
                                  onChange={(e) => {
                                    const value = Math.max(
                                      0,
                                      Math.min(
                                        100,
                                        Math.round(Number(e.target.value)),
                                      ),
                                    );
                                    field.onChange(String(value / 100));
                                  }}
                                  type="number"
                                  placeholder="100"
                                  className="h-7 w-full text-sm"
                                  min={0}
                                  max={100}
                                  step={1}
                                />
                                <span className="pointer-events-none absolute right-2 text-xs text-primary-400">
                                  %
                                </span>
                              </div>
                              <Slider
                                value={[Math.round(Number(field.value) * 100)]}
                                onValueChange={(v) => {
                                  field.onChange(String(v[0] / 100));
                                }}
                                min={0}
                                max={100}
                                step={1}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )
              }
              <FormField
                control={form.control}
                name="feedback"
                render={({ field }) => (
                  <FormItem className="flex w-full flex-col">
                    <FormLabel className="flex items-center text-xs font-medium">
                      Additional instructions
                    </FormLabel>
                    <FormControl>
                      <TextArea
                        minRows={2}
                        placeholder="Optional instructions to tune the scorer..."
                        value={field.value || ""}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {activeScorer &&
                (activeScorer.type === "global" ? (
                  <span className="-mt-2 flex items-center gap-1 text-xs text-primary-500">
                    <Info className="size-3" /> Autoeval scorers are always
                    tuned as new custom scorers{" "}
                  </span>
                ) : (
                  <FormField
                    control={form.control}
                    name="tuneAsNewScorer"
                    render={({ field }) => (
                      <div className="-mt-2 flex flex-col gap-1">
                        <FormItem className="flex gap-1">
                          <FormControl>
                            <Switch
                              className="-ml-1 scale-75"
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <FormLabel
                            className={cn(
                              "!mt-0 cursor-pointer text-xs text-primary-900",
                            )}
                          >
                            Create a new scorer
                          </FormLabel>
                        </FormItem>
                        <FormDescription>
                          Create a new scorer instead of editing the existing
                          one
                        </FormDescription>
                      </div>
                    )}
                  />
                ))}
            </div>
            <div className="sticky bottom-0 z-10 flex justify-between border-t px-4 py-2 bg-primary-50">
              <FormField
                control={form.control}
                name="allowRunningWithoutConsent"
                render={({ field }) => (
                  <BasicTooltip tooltipContent="Automatically accept the changes without asking for confirmation. This can be changed anytime in the chat settings">
                    <FormItem className="-mx-1 flex items-center gap-1">
                      <FormControl>
                        <Switch
                          className="scale-75"
                          checked={field.value}
                          onCheckedChange={(checked) => {
                            field.onChange(checked);
                            setAllowRunningWithoutConsent(checked);
                          }}
                        />
                      </FormControl>
                      <FormLabel className="!mt-0 ml-1 cursor-pointer text-xs text-primary-900">
                        Auto-accept
                      </FormLabel>
                    </FormItem>
                  </BasicTooltip>
                )}
              />
              <Button
                variant={"primary"}
                size="xs"
                className="w-fit disabled:opacity-100"
                type="submit"
              >
                Submit
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
