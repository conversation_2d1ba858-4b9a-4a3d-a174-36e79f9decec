import { But<PERSON> } from "#/ui/button";
import { Blend } from "lucide-react";
import { useGlobalChat } from "#/ui/optimization/use-global-chat-context";
import { DropdownMenuItem } from "#/ui/dropdown-menu";

export const GenerateRowsButton = ({
  asDropdownMenuItem = false,
}: {
  asDropdownMenuItem?: boolean;
}) => {
  const {
    setIsChatOpen,
    handleSendMessage,
    setCurrentSessionUserMessage,
    pageKey,
  } = useGlobalChat();
  const generateButton = (
    <Button
      variant="ghost"
      size="xs"
      className="w-full justify-start gap-2 rounded-sm font-normal text-foreground hover:bg-primary-200"
      onClick={() => {
        handleSendMessage(
          {
            id: crypto.randomUUID(),
            type: "user_message",
            message: `${pageKey === "playground" ? "Generate 5 rows for the existing dataset. If none exists, generate a new with 5 rows given what is present in the playground" : "Generate 5 rows for this dataset"}`,
          },
          {
            clearContextObjects: false,
            clearUserMessage: false,
          },
        );
        setTimeout(() => {
          setIsChatOpen(true);
        }, 250);
      }}
    >
      <Blend className="size-3 text-foreground" />
      Generate 5 rows
    </Button>
  );
  if (asDropdownMenuItem) {
    return (
      <>
        <DropdownMenuItem asChild>{generateButton}</DropdownMenuItem>
        <Button
          variant="ghost"
          size="xs"
          className="flex size-fit items-center gap-2 px-2 py-1 text-xs font-normal text-primary-500 hover:bg-transparent"
          onClick={() => {
            setIsChatOpen(true);
            setCurrentSessionUserMessage(
              `Generate [NUMBER_OF_ROWS] rows for the existing dataset ${pageKey === "playground" ? ". If none exists, generate a new with [NUMBER_OF_ROWS] rows given what is present in the playground." : ""}`,
            );
          }}
        >
          Want to generate more than 5 rows? Ask Loop
        </Button>
      </>
    );
  }
  return generateButton;
};
