import {
  usePlaygroundFullscreenTaskIndex,
  usePlaygroundPromptSheetIndexState,
} from "#/ui/query-parameters";
import { cn } from "#/utils/classnames";
import { type PropsWithChildren, forwardRef, useEffect } from "react";

export const PlaygroundPromptBlocks = forwardRef<
  HTMLDivElement,
  PropsWithChildren<{}>
>(({ children }: PropsWithChildren<{}>, ref) => {
  const { promptSheetIndex } = usePlaygroundPromptSheetIndexState();

  useEffect(() => {
    if (promptSheetIndex === null) return;
    document
      .getElementById(`playground-prompt-${promptSheetIndex}`)
      ?.scrollIntoView({ behavior: "smooth" });
  }, [promptSheetIndex]);

  const [fullscreenTaskIndex] = usePlaygroundFullscreenTaskIndex();

  return (
    <div
      className={cn("sticky left-0 flex-none", {
        "flex-1 flex h-full": fullscreenTaskIndex !== null,
      })}
      ref={ref}
    >
      <div
        className={cn("relative flex h-[460px] w-full flex-col pt-3", {
          "flex-1 h-auto": fullscreenTaskIndex !== null,
        })}
      >
        <div className="-mx-3 flex grow gap-2 overflow-hidden overflow-x-auto px-3 pb-3">
          {children}
        </div>
      </div>
    </div>
  );
});
PlaygroundPromptBlocks.displayName = "PlaygroundPromptBlocks";
