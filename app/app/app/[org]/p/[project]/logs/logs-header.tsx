import { EntityContextMenu } from "#/ui/entity-context-menu";
import Link from "next/link";
import { type GetRowsForExportFn } from "../experiments/[experiment]/(queries)/table-queries";
import { ReviewButton } from "../review-button";
import { getProjectConfigurationLink } from "../getProjectLink";
import { cn } from "#/utils/classnames";
import { buttonVariants } from "#/ui/button";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import {
  OptimizationChat,
  useIsLoopEnabled,
} from "#/ui/optimization/optimization-chat";

export const LogsHeader = ({
  projectId,
  projectName,
  orgName,
  getRowsForExport,
  hideActions,
  columnVisibility,
  onBTQLFilter,
  isReadOnly,
}: {
  projectId: string;
  projectName: string;
  orgName: string;
  getRowsForExport: GetRowsForExportFn;
  hideActions?: boolean;
  columnVisibility?: Record<string, boolean>;
  onBTQLFilter?: (filterText: string) => void;
  isReadOnly?: boolean;
}) => {
  const isLoopEnabled = useIsLoopEnabled();

  return (
    <div className="flex items-center gap-2 px-3 pb-2 pt-1 bg-primary-50">
      <h1 className="text-base font-semibold">Logs</h1>
      <EntityContextMenu
        objectType="project_log"
        objectId={projectId ?? ""}
        objectName={projectName}
        orgName={orgName}
        projectName={projectName}
        buttonClassName="h-7 w-7"
        getRowsForExport={getRowsForExport}
        exportName={[projectName, "logs"].join(" ")}
        onlyExportingLoadedRows={true}
        columnVisibility={columnVisibility}
        handleCopyId={() => {
          navigator.clipboard.writeText(projectId ?? "");
          toast("Project ID copied to clipboard");
        }}
        isReadOnly={isReadOnly}
      />
      <div className="grow" />
      {!hideActions && (
        <>
          <ReviewButton />
          <Link
            href={`${getProjectConfigurationLink({ orgName, projectName })}/online-scoring`}
            className={cn(buttonVariants({ size: "xs" }))}
          >
            <Plus className="size-3" />
            Scorer
          </Link>
          {isLoopEnabled && (
            <OptimizationChat
              hasMultipleSelectedExperiments={false}
              onBTQLFilter={onBTQLFilter}
            />
          )}
        </>
      )}
    </div>
  );
};
