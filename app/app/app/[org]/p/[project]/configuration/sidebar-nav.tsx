"use client";

import Link from "next/link";
import { cn } from "#/utils/classnames";
import { useContext } from "react";
import { usePathname } from "next/navigation";
import { buttonVariants } from "#/ui/button";
import {
  AppWindowMac,
  Bell,
  ListChecks,
  type LucideIcon,
  MoreHorizontal,
  PercentDiamond,
  Radio,
  Tag,
  Unplug,
  Zap,
} from "lucide-react";
import { useOrg } from "#/utils/user";
import { getProjectConfigurationLink } from "../getProjectLink";
import { ProjectContext } from "../projectContext";
import { type FeatureFlags, useFeatureFlags } from "#/lib/feature-flags";

export const projectConfigurationNavItems = [
  {
    title: "Tags",
    href: "tags",
    Icon: Tag,
  },
  {
    title: "Human review",
    href: "review",
    Icon: ListChecks,
  },
  {
    title: "Aggregate scores",
    href: "aggregate-scores",
    Icon: PercentDiamond,
  },
  {
    title: "Online scoring",
    href: "online-scoring",
    Icon: Radio,
  },
  {
    title: "Remote evals",
    href: "remote-evals",
    Icon: Unplug,
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    featureFlag: "remoteEvals" as keyof FeatureFlags,
  },
  {
    title: "Automations",
    href: "automations",
    Icon: Bell,
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    featureFlag: "automations" as keyof FeatureFlags,
  },
  {
    title: "Span iframes",
    href: "span-iframes",
    Icon: AppWindowMac,
  },
  {
    title: "Brainstore",
    href: "brainstore",
    Icon: Zap,
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    featureFlag: "brainstore" as keyof FeatureFlags,
  },
  {
    title: "Advanced",
    href: "advanced",
    Icon: MoreHorizontal,
  },
];

export const SidebarNav = ({ className }: { className?: string }) => {
  const { isLoading, flags } = useFeatureFlags();
  return (
    <nav
      className={cn(
        "flex gap-x-2 -mx-3 px-3 pb-2 border-b lg:border-0 lg:mx-0 lg:p-0 lg:flex-col lg:gap-0 lg:gap-y-1 overflow-x-auto lg:overflow-x-visible no-scrollbar",
        className,
      )}
    >
      {projectConfigurationNavItems.map((item) => {
        if (item.featureFlag && (isLoading || !flags[item.featureFlag])) {
          return null;
        }
        return <SidebarNavLink key={item.href} {...item} />;
      })}
    </nav>
  );
};

const SidebarNavLink = ({
  href,
  title,
  Icon,
}: {
  href: string;
  title: string;
  Icon: LucideIcon;
}) => {
  const org = useOrg();
  const { projectName } = useContext(ProjectContext);
  const base = getProjectConfigurationLink({
    orgName: org.name,
    projectName,
  });
  const pathname = usePathname();

  return (
    <Link
      key={href}
      href={`${base}/${href}`}
      className={cn(
        buttonVariants({
          variant: pathname && pathname.endsWith(href) ? "default" : "ghost",
          size: "xs",
        }),
        "justify-start pl-2 gap-2 text-[13px]",
      )}
    >
      <Icon className="size-3.5 flex-none" />
      {title}
    </Link>
  );
};
