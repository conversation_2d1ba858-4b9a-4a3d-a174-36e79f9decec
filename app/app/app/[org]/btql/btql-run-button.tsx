import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useRef,
  useState,
} from "react";
import { Button } from "#/ui/button";
import { Play } from "lucide-react";
import { toast } from "sonner";
import { useSessionToken } from "#/utils/auth/session-token";
import { type BTQLResponse, fetchBtql } from "#/utils/btql/btql";
import { useOrg } from "#/utils/user";
import { useBtqlFlags } from "#/lib/feature-flags";
import { Spinner } from "#/ui/icons/spinner";

export function BtqlRunButton({
  getCurrentQuery,
  runButtonRef,
  setRawResult,
  setError,
  setLastQueryMs,
  value,
  setSelectedRow,
}: {
  getCurrentQuery: () => string | undefined;
  runButtonRef: React.RefObject<HTMLButtonElement | null>;
  setRawResult: (result: BTQLResponse<Record<string, unknown>> | null) => void;
  setError: (error: string | null) => void;
  setLastQueryMs: (lastQueryMs: number | null) => void;
  value: string;
  setSelectedRow: Dispatch<
    SetStateAction<{
      row: Record<string, unknown> | null;
      index?: number;
    } | null>
  >;
}) {
  const org = useOrg();
  const btqlFlags = useBtqlFlags();

  const [loading, setLoading] = useState(false);
  const signal = useRef<AbortController>(new AbortController());
  const { getOrRefreshToken } = useSessionToken();

  const runQuery = useCallback(async () => {
    setSelectedRow(null);
    const query = getCurrentQuery();
    if (!query || query.trim() === "") {
      toast.error("Please enter a query");
      return;
    }

    setLoading(true);
    setError(null);

    const start = Date.now();
    signal.current = new AbortController();
    try {
      const result = await fetchBtql({
        args: {
          query,
          brainstoreRealtime: true, // TODO: This should be a switch
          disableLimit: false,
        },
        btqlFlags,
        apiUrl: org.api_url,
        getOrRefreshToken,
        signal: signal.current.signal,
      });
      setRawResult(result);
    } catch (e) {
      if (signal.current.signal.aborted) {
        return;
      }
      setError(e instanceof Error ? e.message : `${e}`);
    } finally {
      setLastQueryMs(Date.now() - start);
      setLoading(false);
    }
  }, [
    btqlFlags,
    getCurrentQuery,
    getOrRefreshToken,
    org.api_url,
    setError,
    setLastQueryMs,
    setRawResult,
    setSelectedRow,
  ]);

  return (
    <Button
      variant="primary"
      size="xs"
      onClick={loading ? () => signal.current.abort() : runQuery}
      ref={runButtonRef}
      disabled={value.trim() === ""}
    >
      {loading ? (
        <>
          <Spinner className="size-3" />
          Stop
        </>
      ) : (
        <>
          <Play className="size-3" />
          Run
        </>
      )}
    </Button>
  );
}
