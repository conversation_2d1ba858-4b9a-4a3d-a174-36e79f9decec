"use client";

import { useAnalytics } from "#/ui/use-analytics";
import { useQueryFunc } from "#/utils/react-query";
import { useOrg } from "#/utils/user";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { BodyWrapper } from "../../body-wrapper";
import { CheckOrg } from "../clientlayout";
import { type getProjectSummary, type ProjectSummary } from "../org-actions";
import { useIsClient } from "#/utils/use-is-client";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { QueryResults } from "./results";
import { BtqlEditor } from "./btql-editor";
import { type TextEditorHandle } from "#/ui/text-editor";
import { ErrorBanner } from "#/ui/error-banner";
import { parseBtqlSchema, type BTQLResponse } from "#/utils/btql/btql";
import { BtqlRunButton } from "./btql-run-button";
import { CopyBtqlCodeSnippet } from "./btql-code-snippet";
import { cn } from "#/utils/classnames";
import { useSearchParams } from "next/navigation";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { RowDetailSheet } from "./row-detail-sheet";
import { ErrorBoundary } from "#/utils/error-boundary";
import { usePanelSize } from "#/ui/use-panel-size";

// TODO:
// - Use inference queries to autocomplete columns.
// - Fill in code snippets.
// - Include and link to in docs
// - Support scrolling through with cursors.
// - Show the query plan
//
// Crazier ideas:
// - Go from a slow query in the UI into the editor easily.
// - "Paste" a BTQL curl command and view it in the editor.
// - Would be kind of magical to make real-time work

export interface Params {
  org: string;
}

export default function ClientPage({
  projectSummary: projectSummaryServer,
}: {
  params: Params;
  projectSummary: ProjectSummary[];
}) {
  const { name: orgName } = useOrg();
  const searchParams = useSearchParams();

  useAnalytics({
    page: {
      category: "btql",
    },
  });

  const { data: projects, isPending: projectsPending } = useQueryFunc<
    typeof getProjectSummary
  >({
    fName: "getProjectSummary",
    args: { org_name: orgName },
    serverData: projectSummaryServer,
  });

  const isClient = useIsClient();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const scrollMarginRef = useRef<HTMLDivElement>(null);
  const runButtonRef = useRef<HTMLButtonElement>(null);

  // TO-DO right now, if we bring the query in via search param, it will just override
  // every tab that has btql working. This is probably not what the user would want.
  // Figure this out -- this could mean we save query to storage on run rather than on every value change.
  // or some smart mechanism to not override all tab if coming from search param
  // or create tabs .. etc
  const queryFromUrl = searchParams?.get("q") || null;

  const [selectedRow, setSelectedRow] = useState<{
    row: Record<string, unknown> | null;
    index?: number;
  } | null>(null);
  const [activeRow, setActiveRow] = useState<string | null>(null);

  const [editorValue, setEditorValue] = useEntityStorage({
    entityType: "btql",
    entityIdentifier: "editorValue",
    key: "editorValue",
    defaultValue: queryFromUrl || "",
  });

  useEffect(() => {
    if (queryFromUrl) {
      setEditorValue(queryFromUrl);
    }
  }, [queryFromUrl, setEditorValue]);

  const textEditorRef = useRef<TextEditorHandle | null>(null);
  const getCurrentQuery = useCallback(
    () => textEditorRef.current?.getValue(),
    [textEditorRef],
  );

  const [rawResult, setRawResult] = useState<BTQLResponse<
    Record<string, unknown>
  > | null>(null);
  const detailsMinWidth = usePanelSize(400);

  const lintErrors = useMemo(() => {
    if (!editorValue || !rawResult || rawResult.data.length === 0) return [];
    // eslint-disable-next-line react-compiler/react-compiler
    return textEditorRef?.current?.getLintErrors() ?? [];
  }, [textEditorRef, editorValue, rawResult]);

  const [error, setError] = useState<string | null>(null);
  const [lastQueryMs, setLastQueryMs] = useState<number | null>(null);

  const tableSchema = useMemo(() => {
    if (!rawResult?.schema) {
      return undefined;
    }
    return parseBtqlSchema(null /*ignored*/, rawResult.schema).fields;
  }, [rawResult?.schema]);

  if (!projects && projectsPending) {
    return <TableSkeleton />;
  }

  if (!projects) {
    return <CheckOrg params={{ org: orgName }}>{null}</CheckOrg>;
  }

  if (!isClient) {
    return <TableSkeleton />;
  }

  return (
    <CheckOrg params={{ org: orgName }}>
      <BodyWrapper innerClassName="flex">
        <ResizablePanelGroup
          autoSaveId="btqlPanelLayout"
          direction="horizontal"
          className="flex w-full"
        >
          <ResizablePanel
            className="relative flex min-w-48 flex-1"
            id="main"
            order={0}
          >
            <div
              ref={scrollContainerRef}
              className={cn("flex-1 flex-col overflow-auto px-3", {
                "pb-6": rawResult && rawResult.data.length > 0,
              })}
            >
              <div
                className={cn(
                  "sticky -left-3 -mx-3 -mt-3 flex h-1/2 flex-col border-b pt-3 bg-primary-100 transition-all",
                  {
                    "h-[calc(100vh-145px)]":
                      !rawResult || rawResult.data.length === 0,
                  },
                )}
                ref={scrollMarginRef}
              >
                <BtqlEditor
                  mode="query"
                  className="flex-1 overflow-auto p-3"
                  value={editorValue}
                  onDebouncedSave={setEditorValue}
                  onMetaEnter={() => runButtonRef.current?.click()}
                  projectData={projects}
                  textEditorRef={textEditorRef}
                />
                <div className="absolute bottom-3 right-3 flex gap-2">
                  <CopyBtqlCodeSnippet value={editorValue} />
                  <BtqlRunButton
                    getCurrentQuery={getCurrentQuery}
                    runButtonRef={runButtonRef}
                    setRawResult={setRawResult}
                    setError={setError}
                    setLastQueryMs={setLastQueryMs}
                    value={editorValue}
                    setSelectedRow={setSelectedRow}
                  />
                </div>
              </div>
              {lintErrors.length > 0 && (
                <ErrorBanner
                  skipErrorReporting
                  className="sticky left-0 flex-none"
                >
                  {lintErrors.map((error) => (
                    <div key={error.message}>{error.message}</div>
                  ))}
                </ErrorBanner>
              )}
              <QueryResults
                scrollContainerRef={scrollContainerRef}
                scrollMarginRef={scrollMarginRef}
                rawResult={rawResult}
                error={error}
                lastQueryMs={lastQueryMs}
                setSelectedRow={setSelectedRow}
                activeRow={activeRow}
                setActiveRow={setActiveRow}
              />
            </div>
          </ResizablePanel>
          {selectedRow && (
            <>
              <ResizableHandle />
              <ResizablePanel
                id="trace"
                order={1}
                className="flex flex-none"
                minSize={detailsMinWidth}
              >
                <div className="flex flex-1 flex-col overflow-hidden">
                  <ErrorBoundary
                    fallback={
                      <ErrorBanner skipErrorReporting>
                        There was a problem rendering the row details
                      </ErrorBanner>
                    }
                  >
                    <RowDetailSheet
                      rowData={selectedRow.row}
                      fields={tableSchema}
                      onClose={() => setSelectedRow(null)}
                      rowIdx={selectedRow.index}
                    />
                  </ErrorBoundary>
                </div>
              </ResizablePanel>
            </>
          )}
        </ResizablePanelGroup>
      </BodyWrapper>
    </CheckOrg>
  );
}
