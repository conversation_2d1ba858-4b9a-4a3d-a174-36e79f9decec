import { z } from "zod";

// copy of sanitizeApiKey with extra handling for user_family_name
function sanitizeServiceToken(r: Record<string, unknown>) {
  return Object.fromEntries(
    Object.entries(r).filter(
      (e) => e[0] !== "key_hash" && e[0] !== "user_family_name",
    ),
  );
}

export function transformServiceToken(row: unknown): Record<string, unknown> {
  const record = z.record(z.unknown()).parse(row);
  const sanitized = sanitizeServiceToken(record);

  return Object.fromEntries(
    Object.entries(sanitized).map(([k, v]) => {
      switch (k) {
        case "user_id":
          return ["service_account_id", v];
        case "user_email":
          return ["service_account_email", v];
        case "user_given_name":
          return ["service_account_name", v];
        default:
          return [k, v];
      }
    }),
  );
}
