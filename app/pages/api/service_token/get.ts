import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { makeOwnerApiKeysFullResultSetQuery } from "../apikey/_util";
import { transformServiceToken } from "./_util";
import {
  commonFilterParamsSchema,
  getObjects,
  paginationParamsSchema,
} from "../_object_crud_util";
import { serviceTokenSchema } from "@braintrust/core/typespecs";
import { HTTPError } from "#/utils/server-util";

const paramsSchema = paginationParamsSchema.merge(
  commonFilterParamsSchema.pick({
    name: true,
    org_name: true,
    id: true,
  }),
);

const outputSchema = serviceTokenSchema.array();

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const { name, id, ...paramsRest } = params;
      if (!authLookup.org_id) {
        throw new HTTPError(
          400,
          "You must provide an organization-scoped API key or an explicit org_name",
        );
      }

      const { query: fullResultSetQuery, queryParams } =
        makeOwnerApiKeysFullResultSetQuery({
          authLookup,
          org_id: authLookup.org_id,
          user_type: "service_account",
          name,
          id,
        });

      const results = await getObjects({
        fullResultsQueryOverride: fullResultSetQuery,
        startingParams: queryParams,
        ...paramsRest,
        fullResultsSize: undefined,
      });
      return results.map(transformServiceToken);
    },
    {
      paramsSchema,
      postprocessOutput: (results: unknown) => outputSchema.parse(results),
      outputSchema,
      renameFields: { service_token_name: "name" },
    },
  );
}
