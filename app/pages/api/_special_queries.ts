import { SqlQueryParams } from "#/utils/sql-query-params";

type IsOrgOwnerParams = { userId: string } & (
  | { orgName: string }
  | { orgId: string }
);

// Checks if the user has ownership privileges in the given org, either through
// a direct ACL grant or through membership in a group that has an owner grant.
export function makeIsOrgOwnerCTE(params: IsOrgOwnerParams): {
  query: string;
  queryParams: SqlQueryParams;
} {
  const queryParams = new SqlQueryParams();

  const userIdParam = queryParams.add(params.userId);

  const query = `
    with
        owner_role_id as (
            select get_owner_role_id() id
        ),
        org as (
          select id from organizations where ${
            "orgName" in params
              ? `name = ${queryParams.add(params.orgName)}`
              : `id = uuid_or_null(${queryParams.add(params.orgId)})`
          }
        ),
        is_org_owner as (
          select exists(
              select 1
              from
                  acls
                  join owner_role_id on true
                  join org on acls.object_type = 'organization' and org.id = acls.object_id
                  left join _expanded_group_members on (
                      acls.group_id = _expanded_group_members.group_id
                      and _expanded_group_members.user_object_type = 'user'
                      and _expanded_group_members.user_group_id = ${userIdParam}::uuid
                  )
              where
                  acls.grant_object_type = 'role'
                  and acls.role_id = owner_role_id.id
                  and (
                      acls.user_id = ${userIdParam}::uuid
                      or _expanded_group_members.group_id is not null
                  )
          )
        )
    `;
  return { query, queryParams };
}

export function checkIsOrgOwnerQuery(params: IsOrgOwnerParams): {
  query: string;
  queryParams: SqlQueryParams;
} {
  const { query: isOrgOwnerCTE, queryParams } = makeIsOrgOwnerCTE(params);

  return {
    query: `${isOrgOwnerCTE}
    select exists from is_org_owner`,
    queryParams,
  };
}
