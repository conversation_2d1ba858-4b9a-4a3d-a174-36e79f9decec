import { type ScaleLinear } from "d3";
import { type TimeseriesData } from "#/ui/charts/timeseries-data/chart-data.types";
import { COLOR_CLASSNAMES, DEFAULT_COLOR_CLASSNAME } from "#/ui/charts/colors";
import {
  type HighlightState,
  isLineHighlighted,
  isLineSelected,
} from "#/ui/charts/highlight";
import { type SelectionType } from "#/ui/charts/selectionTypes";
import { type CSSProperties, useMemo } from "react";
import type { StackedTimeBuckets } from "#/ui/charts/timeseries-data/get-timeseries-stacked-time";
import { cn } from "#/utils/classnames";
import { getBarWidth } from "./get-bar-width";

interface VizBarsProps<P, S> {
  style: CSSProperties;
  className: string;
  timeseriesData: TimeseriesData<P, S>;
  stackedData: StackedTimeBuckets;
  xScale: ScaleLinear<number, number>;
  yScale: ScaleLinear<number, number>;
  highlightState: HighlightState;
  nearestPoint: {
    seriesIndex: number;
    timeIndex: number;
    isStackedTotal?: boolean;
  } | null;
  clipPathId: string;
}

export const VizBars = <P, S extends { selectionType: SelectionType }>(
  props: VizBarsProps<P, S>,
) => {
  const {
    style,
    className,
    xScale,
    yScale,
    timeseriesData,
    highlightState,
    nearestPoint,
    clipPathId,
    stackedData,
  } = props;

  const { seriesMetadata } = timeseriesData;

  const {
    seriesIndex: nearestSeriesIndex,
    timeIndex: nearestTimeIndex,
    isStackedTotal,
  } = nearestPoint ?? { seriesIndex: -1, timeIndex: -1, isStackedTotal: false };

  const seriesClassNames = useMemo(() => {
    return seriesMetadata.map((s, seriesIndex) => {
      const colorClassName = COLOR_CLASSNAMES[seriesIndex];
      const lineClassName = cn(DEFAULT_COLOR_CLASSNAME, colorClassName);
      return lineClassName;
    });
  }, [seriesMetadata]);

  const timeBucketDOM = useMemo(() => {
    const { timestamps, seriesIndices, bucketDuration, seriesStackedSum } =
      stackedData;
    return timestamps.map((start, timeBucketIndex) => {
      const end = start + bucketDuration;

      const x1 = xScale(start);
      const x2 = xScale(end);

      const barWidth = getBarWidth(x2 - x1);

      return seriesIndices[timeBucketIndex].map((seriesIndex, j) => {
        if (j === 0) {
          return null;
        }

        const lineClassName = seriesClassNames[seriesIndex];

        const prevValue = seriesStackedSum[timeBucketIndex][j - 1];
        const currentValue = seriesStackedSum[timeBucketIndex][j];

        const y1 = yScale(prevValue);
        const y2 = yScale(currentValue);
        const height = y1 - y2;

        // skip any dom for near zeros
        if (height < 0.01) {
          return null;
        }

        return (
          <rect
            key={`viz-bar-${timeBucketIndex}-${j}`}
            className={lineClassName}
            x={x1}
            width={barWidth}
            y={y2}
            height={height}
            strokeWidth={0}
          />
        );
      });
    });
  }, [stackedData, xScale, seriesClassNames, yScale]);

  const highlightSeriesSVG = useMemo(() => {
    return timeBucketDOM.map((bucket, timeBucketIndex) => {
      return bucket.filter((_, i) => {
        if (isStackedTotal && timeBucketIndex === nearestTimeIndex) {
          return true;
        }
        const seriesIndex = stackedData.seriesIndices[timeBucketIndex][i];
        const s = seriesMetadata[seriesIndex];
        if (!isLineSelected(s.selectionType, highlightState)) {
          return false;
        }

        const isNearestSeries = nearestSeriesIndex === seriesIndex;
        const nearestHighlight = isNearestSeries;
        if (nearestHighlight) {
          return true;
        }

        const isHighlighted =
          Boolean(highlightState && highlightState.highlighted) &&
          isLineHighlighted(s.selectionType, highlightState);

        return isHighlighted;
      });
    });
  }, [
    timeBucketDOM,
    isStackedTotal,
    nearestTimeIndex,
    stackedData.seriesIndices,
    seriesMetadata,
    highlightState,
    nearestSeriesIndex,
  ]);

  const inVizHighlightState =
    Boolean(
      highlightState &&
        highlightState.highlighted &&
        highlightState.highlighted.groupVal,
    ) ||
    (nearestTimeIndex >= 0 && !isStackedTotal);

  return useMemo(() => {
    return (
      <>
        <div
          style={style}
          key="base"
          className={cn(
            className,
            "transition-opacity opacity-100 duration-0",
            {
              "opacity-70": isStackedTotal && seriesMetadata.length > 1,
              "opacity-30 duration-150 delay-100": inVizHighlightState,
            },
          )}
        >
          <svg className="size-full">
            <g className="pointer-events-none" clipPath={`url(#${clipPathId})`}>
              {timeBucketDOM}
            </g>
          </svg>
        </div>
        <div
          style={style}
          key="highlight"
          className={cn(className, "transition-opacity opacity-100")}
        >
          <svg className="size-full">
            <g className="pointer-events-none" clipPath={`url(#${clipPathId})`}>
              {highlightSeriesSVG}
            </g>
          </svg>
        </div>
      </>
    );
  }, [
    timeBucketDOM,
    highlightSeriesSVG,
    seriesMetadata,
    style,
    className,
    clipPathId,
    inVizHighlightState,
    isStackedTotal,
  ]);
};
