import {
  type CSSProperties,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { cn } from "#/utils/classnames";
import type { XScale, YScale } from "#/ui/charts/chart.types";
import { genBottomTicks } from "./gen-bottom-ticks";

/**
 * BottomAxis.tsx
 */

type BottomAxisProps = {
  xScale: XScale;
  yScale: YScale; // todo - bottom axis should not depend on yScale
  timestamps?: boolean;
  tickCount?: number;
  showGridLines?: boolean;
  shiftFirstTickLabel?: boolean;
  tickFormatter?: (v: Date | number, i: number) => string | null;
  label?: string;
  labelsOnly?: boolean;
  isTimeFrameAxis?: boolean;
  tzUTC?: boolean;
  /**
   * Minimum horizontal spacing in pixels required to show x-axis labels.
   * If the spacing between adjacent ticks is smaller than this value,
   * labels are hidden to avoid overlap.
   */
  minLabelSpacingPx?: number;
};

// tick length
const TICK_BOTTOM_LENGTH = 5;

export const BottomAxis = ({
  xScale,
  yScale,
  tickCount: tickCountRequested,
  showGridLines,
  shiftFirstTickLabel,
  tickFormatter,
  label,
  labelsOnly,
  isTimeFrameAxis, // rename for clarity
  tzUTC,
  minLabelSpacingPx,
}: BottomAxisProps) => {
  const rangeX = xScale.range();
  const rangeY = yScale.range();
  const height = rangeY[0] - rangeY[1];
  const width = rangeX[1] - rangeX[0];

  // clamp number of ticks to what's reasonable for size of graph
  const minTicks = 2;
  const maxTicks = Math.floor(width / 50);
  const goalTicks = Math.round(width / 100);
  const tickCount = Math.round(
    Math.min(maxTicks, Math.max(minTicks, tickCountRequested ?? goalTicks)),
  );

  const ticks = useMemo(
    () =>
      genBottomTicks({
        xScale,
        isTimeFrame: isTimeFrameAxis ?? false,
        tickCount,
        tickFormatter,
        shiftFirstTickLabel,
        tzUTC,
      }),
    [
      xScale,
      tickCount,
      tickFormatter,
      shiftFirstTickLabel,
      isTimeFrameAxis,
      tzUTC,
    ],
  );

  const hasValues = ticks.some((t) => t.value != null);

  // Determine if labels should be hidden based on minimum spacing between tick positions
  const shouldHideLabels = useMemo(() => {
    const requiredSpacing = Math.max(0, minLabelSpacingPx ?? 60);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- ticks are heterogeneous
    const offsets = (ticks as any[])
      .map((t) =>
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- xOffset narrowed to number
        typeof t?.xOffset === "number" ? (t.xOffset as number) : null,
      )
      .filter((v): v is number => v != null)
      .sort((a, b) => a - b);
    if (offsets.length < 2) return false;

    return offsets.some((offset, i) => {
      if (i === 0) return false; // skip first element
      return offset - offsets[i - 1] < requiredSpacing;
    });
  }, [ticks, minLabelSpacingPx]);

  return (
    <g className="pointer-events-none text-primary-300">
      {/* Main horizontal line */}
      {labelsOnly ? null : (
        <path
          d={["M", rangeX[0], 0, "L", rangeX[1], 0].join(" ")}
          fill="none"
          stroke="currentColor"
        />
      )}
      {/* Ticks and labels */}
      {ticks.map((v, i) => {
        const { value, xOffset, textAnchor } = v;
        return (
          <g
            key={i}
            transform={`translate(${xOffset}, ${labelsOnly ? -8 : 0})`}
          >
            {labelsOnly ? null : showGridLines ? (
              <line
                y1={TICK_BOTTOM_LENGTH}
                y2={-height - 10}
                stroke="currentColor"
                strokeWidth={1}
                opacity={0.4}
              />
            ) : (
              <line
                y2={TICK_BOTTOM_LENGTH}
                stroke="currentColor"
                strokeWidth={1}
              />
            )}
            {value &&
              !shouldHideLabels &&
              ("maxWidth" in v ? (
                <TruncatedText
                  x={xOffset}
                  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                  width={v.maxWidth as number}
                  value={value}
                />
              ) : (
                <text
                  className="fill-primary-500 text-[10px]"
                  style={{
                    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                    textAnchor: textAnchor as CSSProperties["textAnchor"],
                    transform: `translateY(${TICK_BOTTOM_LENGTH + 15}px)`,
                  }}
                >
                  {value}
                </text>
              ))}
          </g>
        );
      })}
      {label && (
        <text
          className="fill-primary-500 text-xs"
          style={{
            transform: `translate(${Math.round(width / 2)}px, ${
              TICK_BOTTOM_LENGTH + (hasValues && !shouldHideLabels ? 35 : 20)
            }px)`,
            textAnchor: "middle",
          }}
        >
          {label}
        </text>
      )}
    </g>
  );
};

function TruncatedText({
  x,
  width,
  value,
}: {
  x: number;
  width: number;
  value: string;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const [isTruncated, setIsTruncated] = useState(false);
  useLayoutEffect(() => {
    setIsTruncated(
      !!ref.current && ref.current.scrollWidth > ref.current.clientWidth,
    );
  }, [width]);

  return (
    <>
      <foreignObject
        className="overflow-visible"
        height={15}
        width={width}
        x={-width / 2}
        y={TICK_BOTTOM_LENGTH + 2}
      >
        <div
          ref={ref}
          className={cn("text-[10px] px-2 text-primary-500 truncate", {
            "flex justify-center": !isTruncated,
          })}
          style={{ width }}
        >
          {value}
        </div>
      </foreignObject>
    </>
  );
}
