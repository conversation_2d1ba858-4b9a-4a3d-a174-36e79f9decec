import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuGroup,
  DropdownMenuSeparator,
  DropdownMenuSubContent,
  DropdownMenuItem,
} from "#/ui/dropdown-menu";
import { Button, buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { type ModelDetails } from "#/ui/prompts/models";
import { PRIORITIZED_DEFAULT_MODELS } from "#/ui/optimization/global-chat-provider";
import { ModelDropdown } from "#/app/app/[org]/prompt/[prompt]/ModelDropdown";
import { useOrg } from "#/utils/user";
import { ModelOptionLabel } from "#/app/app/[org]/prompt/[prompt]/model-icon";
import { useMemo } from "react";
import { BasicTooltip } from "#/ui/tooltip";
import { ModelOptionTooltip } from "#/app/app/[org]/prompt/[prompt]/model-option-tooltip";
import { AlertTriangle, Check, Plus } from "lucide-react";
import { useFeatureFlags } from "#/lib/feature-flags";
import { AIProviderLogoStack } from "#/ui/prompts/empty";
import Link from "next/link";

// This is a hack to handle the fact that if we do includes on model name for openAI, it will pull in a bunch of mini and pro versions.
// we check for azure as well since zaure providers openai models.
// We want to do includes for claude because there are multiple providers that offer the model but have different names
const isModelMatch = (
  modelName: string,
  defaultModel: string,
  provider: string,
) => {
  return provider === "openai" || provider === "azure"
    ? modelName === defaultModel
    : modelName.includes(defaultModel);
};

export const OptimizationModelsDropdown = ({
  currentModel,
  setCurrentModel,
  configuredModelsByProvider,
  size,
}: {
  currentModel: string;
  setCurrentModel: (model: string) => void;
  configuredModelsByProvider: Record<string, ModelDetails[]>;
  size?: "widget" | "full";
}) => {
  const { name: orgName } = useOrg();
  const { flags } = useFeatureFlags();

  const modelLookup = useMemo(() => {
    const lookup = new Map<string, ModelDetails & { provider: string }>();
    Object.entries(configuredModelsByProvider).forEach(([provider, models]) => {
      models.forEach((model) => {
        lookup.set(model.modelName, { ...model, provider });
        if (model.children) {
          model.children.forEach((child) => {
            lookup.set(child.modelName, { ...child, provider });
          });
        }
      });
    });
    return lookup;
  }, [configuredModelsByProvider]);

  const prioritizedModelsWithVariants = useMemo(() => {
    return PRIORITIZED_DEFAULT_MODELS.map((defaultModel) => {
      const variantsByProvider = Object.entries(
        configuredModelsByProvider,
      ).reduce<Record<string, Array<ModelDetails & { provider: string }>>>(
        (acc, [provider, models]) => {
          const matchingModels = Array.from(
            models
              .filter((model) =>
                isModelMatch(model.modelName, defaultModel, provider),
              )
              // Remove duplicates by display name
              .reduce<Map<string, ModelDetails>>((acc, model) => {
                const key = model.displayName ?? model.modelName;
                if (!acc.has(key)) {
                  acc.set(key, model);
                }
                return acc;
              }, new Map())
              .values(),
          );

          if (matchingModels.length > 0) {
            acc[provider] = matchingModels.map((model) => ({
              ...model,
              provider,
            }));
          }

          return acc;
        },
        {},
      );

      return {
        defaultModel,
        variantsByProvider,
        isConfigured: Object.keys(variantsByProvider).length > 0,
      };
    });
  }, [configuredModelsByProvider]);

  const hasAnyVariants = prioritizedModelsWithVariants.some(
    ({ isConfigured }) => isConfigured,
  );

  const modelsToRender = useMemo(() => {
    return prioritizedModelsWithVariants.map(
      ({ defaultModel, variantsByProvider, isConfigured }) => {
        const allVariants = Object.values(variantsByProvider).flat();

        const currentModelVariant = allVariants.find(
          (variant) => variant.modelName === currentModel,
        );

        const representativeModel =
          currentModelVariant || allVariants[0] || null;
        const isCurrentModelInGroup = !!currentModelVariant;

        return {
          defaultModel,
          variantsByProvider,
          isConfigured,
          isCurrentModelInGroup,
          representativeModel,
        };
      },
    );
  }, [prioritizedModelsWithVariants, currentModel]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="xs"
          className={cn(
            "bg-transparent h-6 px-1 py-0 text-xs text-primary-500 font-normal",
            size === "full" && "h-7 px-1.5 text-[12.5px]",
          )}
          isDropdown
        >
          <span className="max-w-60 truncate">
            {modelLookup.get(currentModel)?.displayName || currentModel || (
              <span className="flex items-center gap-1 text-bad-500">
                <AlertTriangle className="size-3" />
                Select a model
              </span>
            )}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        {hasAnyVariants ? (
          <DropdownMenuGroup>
            <DropdownMenuLabel>Model</DropdownMenuLabel>
            {modelsToRender.map(
              ({
                defaultModel,
                variantsByProvider,
                isConfigured,
                isCurrentModelInGroup,
                representativeModel,
              }) => {
                return (
                  <div key={defaultModel}>
                    {isConfigured ? (
                      Object.keys(variantsByProvider).length === 1 ? (
                        Object.entries(variantsByProvider).map(
                          ([provider, variants]) => (
                            <div key={provider}>
                              {variants.map((variant) => (
                                <BasicTooltip
                                  key={variant.modelName}
                                  side="right"
                                  className="rounded-md"
                                  tooltipContent={
                                    <ModelOptionTooltip model={variant} />
                                  }
                                >
                                  <DropdownMenuCheckboxItem
                                    checked={currentModel === variant.modelName}
                                    onSelect={() => {
                                      setCurrentModel(variant.modelName);
                                    }}
                                    className="focus:bg-primary-200"
                                  >
                                    <ModelOptionLabel
                                      model={variant.modelName}
                                      displayName={variant.displayName}
                                      deprecated={variant.deprecated}
                                      experimental={variant.experimental}
                                    />
                                  </DropdownMenuCheckboxItem>
                                </BasicTooltip>
                              ))}
                            </div>
                          ),
                        )
                      ) : (
                        <DropdownMenuSub>
                          <DropdownMenuSubTrigger>
                            <Check
                              className={cn("size-3 opacity-0", {
                                "opacity-100": isCurrentModelInGroup,
                              })}
                            />
                            <ModelOptionLabel
                              model={defaultModel}
                              displayName={
                                representativeModel?.displayName ?? defaultModel
                              }
                              deprecated={
                                representativeModel?.deprecated ?? false
                              }
                              experimental={
                                representativeModel?.experimental ?? false
                              }
                            />
                          </DropdownMenuSubTrigger>
                          <DropdownMenuSubContent>
                            {Object.entries(variantsByProvider).map(
                              ([provider, variants]) => (
                                <div key={provider}>
                                  <DropdownMenuLabel className="capitalize">
                                    {provider}
                                  </DropdownMenuLabel>
                                  {variants.map((variant) => (
                                    <BasicTooltip
                                      key={variant.modelName}
                                      side="right"
                                      className="rounded-md"
                                      tooltipContent={
                                        <ModelOptionTooltip model={variant} />
                                      }
                                    >
                                      <DropdownMenuCheckboxItem
                                        checked={
                                          currentModel === variant.modelName
                                        }
                                        onSelect={() => {
                                          setCurrentModel(variant.modelName);
                                        }}
                                        className="focus:bg-primary-200"
                                      >
                                        <ModelOptionLabel
                                          model={variant.modelName}
                                          displayName={variant.displayName}
                                          deprecated={variant.deprecated}
                                          experimental={variant.experimental}
                                        />
                                      </DropdownMenuCheckboxItem>
                                    </BasicTooltip>
                                  ))}
                                </div>
                              ),
                            )}
                          </DropdownMenuSubContent>
                        </DropdownMenuSub>
                      )
                    ) : (
                      <DropdownMenuItem disabled>
                        <Check className={cn("size-3 opacity-0")} />
                        <ModelOptionLabel
                          model={defaultModel}
                          displayName={
                            representativeModel?.displayName ?? defaultModel
                          }
                          deprecated={representativeModel?.deprecated ?? false}
                          experimental={
                            representativeModel?.experimental ?? false
                          }
                        />
                      </DropdownMenuItem>
                    )}
                  </div>
                );
              },
            )}
            {flags.loopTryOtherModels && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>Other models</DropdownMenuSubTrigger>
                  <ModelDropdown
                    isInSubMenu
                    orgName={orgName}
                    modelOptionsByProvider={configuredModelsByProvider}
                    currentModel={currentModel}
                    onChange={setCurrentModel}
                  />
                </DropdownMenuSub>
              </>
            )}
          </DropdownMenuGroup>
        ) : (
          <div className="flex flex-col gap-3 p-3 text-xs text-primary-500">
            <div className="max-w-48">
              No supported models found. Add an AI provider that includes any of
              the following models to get started.
            </div>
            <ul className="flex list-inside list-disc flex-col gap-1">
              {PRIORITIZED_DEFAULT_MODELS.map((model) => (
                <li key={model} className="flex items-center gap-2">
                  <ModelOptionLabel
                    model={model}
                    displayName={model}
                    deprecated={false}
                    experimental={false}
                  />
                </li>
              ))}
            </ul>
            <Link
              href={`/app/${orgName}/settings/secrets`}
              className={buttonVariants({ variant: "border", size: "xs" })}
            >
              <Plus className="size-3" />
              <div className="flex flex-1 items-center justify-between">
                <span className="mr-2 text-xs">Add provider</span>
                <AIProviderLogoStack
                  iconSize={14}
                  iconClassName="size-5 -mr-1.5"
                  providerCount={2}
                />
              </div>
            </Link>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
