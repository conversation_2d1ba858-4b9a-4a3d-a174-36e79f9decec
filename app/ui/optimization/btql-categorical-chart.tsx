"use client";

import { useMemo, useRef } from "react";
import { type ParsedQuery } from "@braintrust/btql/parser";
import { useGroupedBarplot } from "#/ui/charts/grouped-barplot";
import { Chart } from "#/ui/charts/Chart";
import { useHighlightState } from "#/ui/charts/highlight";
import {
  type SelectionType,
  formatValueForSelectionType,
} from "#/ui/charts/selectionTypes";
import { getSeriesColorMap } from "#/app/app/[org]/monitor/groups";
import { COLOR_CLASSNAMES } from "#/ui/charts/colors";
import { useResizeObserver } from "#/ui/charts/padding/use-resize-observer";
import { ChartTooltip } from "#/ui/charts/tooltip/chart-tooltip";

interface CategoricalChartFromAstProps {
  ast: ParsedQuery;
  data: Record<string, unknown>[];
}

type DataRow = Record<string, unknown>;

type Data<Metadata> = {
  value: number | null;
  colorClassName: string;
  metadata: Metadata;
};

// Separator for composite categories
const CATEGORY_SEPARATOR = " - ";
// Max number of categories (bars) to render
const MAX_CATEGORIES = 5;
const OTHER_LABEL = "Other";

// Tooltip now uses shared ChartTooltip from charts/tooltip

export function CategoricalChartFromAst({
  ast,
  data,
}: CategoricalChartFromAstProps) {
  const { state: highlightState } = useHighlightState();

  // Extract dimensions and measures from AST
  const { dimensions, measures } = useMemo(() => {
    // Handle SELECT queries differently
    if (
      ast.select &&
      ast.select.length > 0 &&
      !ast.dimensions &&
      !ast.measures
    ) {
      // Extract field names from select expressions
      const selectedFields = ast.select
        .map((sel) => {
          if ("expr" in sel && sel.expr && "alias" in sel) {
            const expr = sel.expr;
            if ("op" in expr && expr.op === "ident" && "name" in expr) {
              // Now TypeScript knows expr is an Ident
              const fieldName = expr.name?.[0]?.toString() || "";
              return {
                name: fieldName,
                alias: sel.alias || fieldName,
              };
            }
          }
          return null;
        })
        .filter(
          (item): item is { name: string; alias: string } => item !== null,
        );

      // Identify metric fields
      const metricPatterns = [
        /^metrics\./,
        /_tokens?$/,
        /_time$/,
        /_duration$/,
        /_latency$/,
        /_count$/,
        /_rate$/,
        /_percentage$/,
        /^cost/,
        /^score/,
      ];

      const isMetric = (fieldName: string) =>
        metricPatterns.some((pattern) => pattern.test(fieldName.toLowerCase()));

      // Separate into dimension and measure fields
      const metricFields = selectedFields.filter((f) => isMetric(f.name));
      const nonMetricFields = selectedFields.filter((f) => !isMetric(f.name));

      // Use the non-metric field as dimension, metrics as measures
      const dims =
        nonMetricFields.length > 0
          ? [
              {
                alias: nonMetricFields[0].alias,
                expr: {
                  op: "identifier" as const,
                  name: { name: [nonMetricFields[0].name] },
                },
              },
            ]
          : [];

      const meas = metricFields.map((f) => ({
        alias: f.alias,
        expr: { op: "identifier" as const, name: { name: [f.name] } },
      }));

      return {
        dimensions: dims,
        measures:
          meas.length > 0
            ? meas
            : [
                {
                  alias: "value",
                  expr: { op: "literal" as const, value: "count(1)" },
                },
              ],
      };
    }

    // Original logic for queries with explicit dimensions/measures
    const allDims = ast.dimensions || [];
    const meas = ast.measures || [];

    // Filter out time dimensions
    const nonTimeDims = allDims.filter((d) => {
      if (d.expr && "op" in d.expr && d.expr.op === "function") {
        const funcExpr = d.expr;
        if (
          "name" in funcExpr &&
          funcExpr.name &&
          "op" in funcExpr.name &&
          funcExpr.name.op === "ident"
        ) {
          const funcIdent = funcExpr.name;
          const funcNameStr = funcIdent.name?.[0]?.toString() || "";
          return !["minute", "hour", "day", "week", "month", "year"].includes(
            funcNameStr,
          );
        }
      }
      return true;
    });

    return {
      dimensions: nonTimeDims,
      measures:
        meas.length > 0
          ? meas
          : [{ alias: "value", expr: { op: "literal", value: "count(1)" } }],
    };
  }, [ast]);

  const dimAliases = dimensions.map((d) => d.alias || "category");

  const stringifyCategoryValue = (value: unknown): string => {
    if (value == null) return "null";
    const valueType = typeof value;
    if (valueType === "object") {
      try {
        return JSON.stringify(value);
      } catch {
        return String(value);
      }
    }
    return String(value);
  };

  // Build categories from data
  const categories = useMemo((): SelectionType[] => {
    // Create a map to store category values and their first measure value for sorting
    const categoryMap = new Map<string, number>();
    const firstMeasureAlias = measures[0]?.alias || "value";

    data.forEach((row) => {
      // Create composite key from all dimension values
      const categoryParts = dimAliases.map((alias) => {
        const value = row[alias];
        return stringifyCategoryValue(value);
      });
      const categoryStr = categoryParts.join(CATEGORY_SEPARATOR);

      if (!categoryMap.has(categoryStr)) {
        const measureValue = row[firstMeasureAlias];
        categoryMap.set(
          categoryStr,
          typeof measureValue === "number" ? measureValue : 0,
        );
      }
    });

    // Sort by measure value (descending) then by category name
    const sorted = Array.from(categoryMap.entries()).sort((a, b) => {
      const valueDiff = b[1] - a[1]; // Sort by value descending
      if (valueDiff !== 0) return valueDiff;
      return a[0].localeCompare(b[0]); // Then by name ascending
    });

    const top = sorted.slice(0, MAX_CATEGORIES).map(([name]) => name);
    const hasOther = sorted.length > MAX_CATEGORIES;

    const base: SelectionType[] = top.map((value) => ({
      value,
      type: "metadata" as const,
    }));

    return hasOther
      ? [...base, { value: OTHER_LABEL, type: "metadata" as const }]
      : base;
  }, [data, dimAliases, measures]);

  const topCategoryNameSet = useMemo(() => {
    return new Set(
      categories.filter((c) => c.value !== OTHER_LABEL).map((c) => c.value),
    );
  }, [categories]);

  // Build subgroups from measures
  const subGroups = useMemo((): SelectionType[] => {
    return measures.map((measure) => ({
      value: measure.alias || "value",
      type: "metric" as const,
    }));
  }, [measures]);

  const noSubGroups = subGroups.length === 1;

  // Get color map for consistent colors
  const seriesColorMap = useMemo(() => {
    const seriesNames = noSubGroups
      ? categories.map((c) => c.value)
      : subGroups.map((s) => s.value);
    return getSeriesColorMap(seriesNames);
  }, [categories, subGroups, noSubGroups]);

  // Build grouped data structure
  const groupedData = useMemo(() => {
    const result: Record<
      string,
      Record<string, Record<string, Record<string, Data<DataRow>>>>
    > = {};

    categories.forEach((category) => {
      // Determine which rows contribute to this category
      const contributingRows =
        category.value === OTHER_LABEL
          ? data.filter((row) => {
              const categoryParts = dimAliases.map((alias) => {
                const value = row[alias];
                return stringifyCategoryValue(value);
              });
              const rowCategoryStr = categoryParts.join(CATEGORY_SEPARATOR);
              return !topCategoryNameSet.has(rowCategoryStr);
            })
          : (() => {
              const row = data.find((r) => {
                const categoryParts = dimAliases.map((alias) => {
                  const value = r[alias];
                  return stringifyCategoryValue(value);
                });
                const rowCategoryStr = categoryParts.join(CATEGORY_SEPARATOR);
                return rowCategoryStr === category.value;
              });
              return row ? [row] : [];
            })();

      if (contributingRows.length === 0) return;

      if (!result[category.value]) {
        result[category.value] = {};
      }
      if (!result[category.value][category.type]) {
        result[category.value][category.type] = {};
      }

      // When noSubGroups is true, the subgroup key is the same as the category
      if (noSubGroups) {
        const subGroupValue = category.value;
        const subGroupType = category.type;

        if (!result[category.value][category.type][subGroupValue]) {
          result[category.value][category.type][subGroupValue] = {};
        }

        // Get the value from the first (and only) measure
        const measureAlias = measures[0]?.alias || "value";
        const aggregated = contributingRows.reduce((sum, row) => {
          const v = row[measureAlias];
          const num =
            typeof v === "number" ? v : v == null ? 0 : Number(v) || 0;
          return sum + num;
        }, 0);

        const colorIndex = seriesColorMap[category.value]?.metadata || 0;

        result[category.value][category.type][subGroupValue][subGroupType] = {
          value: aggregated,
          colorClassName:
            COLOR_CLASSNAMES[colorIndex % COLOR_CLASSNAMES.length],
          metadata: category.value === OTHER_LABEL ? {} : contributingRows[0],
        };
      } else {
        // Multiple measures case
        subGroups.forEach((subGroup) => {
          const measureAlias = subGroup.value;
          const aggregated = contributingRows.reduce((sum, row) => {
            const v = row[measureAlias];
            const num =
              typeof v === "number" ? v : v == null ? 0 : Number(v) || 0;
            return sum + num;
          }, 0);

          const colorIndex = seriesColorMap[subGroup.value]?.metric || 0;

          if (!result[category.value][category.type][subGroup.value]) {
            result[category.value][category.type][subGroup.value] = {};
          }

          result[category.value][category.type][subGroup.value][subGroup.type] =
            {
              value: aggregated,
              colorClassName:
                COLOR_CLASSNAMES[colorIndex % COLOR_CLASSNAMES.length],
              metadata:
                category.value === OTHER_LABEL ? {} : contributingRows[0],
            };
        });
      }
    });

    return result;
  }, [
    topCategoryNameSet,
    categories,
    subGroups,
    data,
    dimAliases,
    noSubGroups,
    seriesColorMap,
    measures,
  ]);

  // Responsive container ref
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartSize = useResizeObserver(chartContainerRef);

  // Calculate responsive chart height - leave room for axis and padding
  const chartHeight = useMemo(() => {
    // Use container height minus padding for axis labels
    const containerHeight = chartSize.height;
    if (containerHeight === 0) return 240; // Default height before size is known

    // Leave room for x-axis labels and padding
    const AXIS_PADDING = 60;
    return Math.max(180, containerHeight - AXIS_PADDING);
  }, [chartSize.height]);

  // Use the grouped barplot hook with dynamic height
  const { chartProps, leftAxisProps, bottomAxisProps } = useGroupedBarplot({
    height: chartHeight,
    data: groupedData,
    categories,
    subGroups: noSubGroups ? categories : subGroups,
    noSubGroups,
    highlightState: highlightState,
    valueRenderer: () => "",
    renderTooltip: ({ group, subGroup, d }) => {
      const isNoSub = noSubGroups;
      const selection = isNoSub ? group : subGroup;
      const labelKey = isNoSub ? group.value : subGroup.value;
      const valueNum = d?.value == null ? null : Number(d.value);

      if (group.value === OTHER_LABEL) {
        const measureAlias = isNoSub
          ? measures[0]?.alias || "value"
          : subGroup.value;

        // Aggregate values by category name for all categories not in the top set
        const breakdownMap = new Map<string, number>();
        data.forEach((row) => {
          const categoryParts = dimAliases.map((alias) => {
            const value = row[alias];
            return stringifyCategoryValue(value);
          });
          const rowCategoryStr = categoryParts.join(CATEGORY_SEPARATOR);
          if (!topCategoryNameSet.has(rowCategoryStr)) {
            const v = row[measureAlias];
            const num =
              typeof v === "number" ? v : v == null ? 0 : Number(v) || 0;
            breakdownMap.set(
              rowCategoryStr,
              (breakdownMap.get(rowCategoryStr) || 0) + num,
            );
          }
        });

        const sorted = Array.from(breakdownMap.entries()).sort((a, b) => {
          const valueDiff = b[1] - a[1];
          if (valueDiff !== 0) return valueDiff;
          return a[0].localeCompare(b[0]);
        });

        const labels = sorted.map(([name]) => name);
        const values = sorted.map(([_, val]) => ({ value: val }));

        // Force all rows in the tooltip to use the same color as the bar
        const colorIndex = isNoSub
          ? (seriesColorMap[OTHER_LABEL]?.metadata ?? 0)
          : (seriesColorMap[subGroup.value]?.metric ?? 0);
        const tooltipSeriesColorMap = Object.fromEntries(
          labels.map((l) => [l, colorIndex]),
        );

        return (
          <ChartTooltip
            title={group.value}
            runDate={new Date().toISOString()}
            count={BigInt(
              Math.round(values.reduce((sum, v) => sum + (v?.value ?? 0), 0)),
            )}
            labels={labels}
            values={values}
            seriesColorMap={tooltipSeriesColorMap}
            vizType="bars"
            numberFormatter={(v) => formatValueForSelectionType(v, selection)}
          />
        );
      }

      return (
        <ChartTooltip
          title={group.value}
          runDate={new Date().toISOString()}
          count={BigInt(Math.round(valueNum == null ? 0 : valueNum))}
          labels={[labelKey]}
          values={[valueNum == null ? null : { value: valueNum }]}
          seriesColorMap={{
            [labelKey]: isNoSub
              ? (seriesColorMap[group.value]?.metadata ?? 0)
              : (seriesColorMap[subGroup.value]?.metric ?? 0),
          }}
          vizType="bars"
          numberFormatter={(v) => formatValueForSelectionType(v, selection)}
        />
      );
    },
  });

  if (!data || data.length === 0) {
    return (
      <div className="flex h-64 items-center justify-center text-sm text-primary-500">
        No data available
      </div>
    );
  }

  // Return a responsive container that fills available space
  return (
    <div ref={chartContainerRef} className="w-full flex-1">
      <Chart
        {...chartProps}
        leftAxisProps={{
          ...leftAxisProps,
          tickFormatter: (tick: number) =>
            formatValueForSelectionType(
              tick,
              subGroups[0] || { type: "metric", value: "value" },
            ),
        }}
        bottomAxisProps={{
          ...bottomAxisProps,
          tickFormatter: (tick: number | Date) => {
            // For categorical data, the tick is the category name
            const tickStr = String(tick);
            // Truncate long composite categories (show full in tooltip)
            if (tickStr.length > 30) {
              return tickStr.slice(0, 27) + "...";
            }
            return tickStr;
          },
        }}
        className="h-full"
      />
    </div>
  );
}
