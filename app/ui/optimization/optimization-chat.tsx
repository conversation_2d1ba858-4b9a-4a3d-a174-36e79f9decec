import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON>lend,
  Octagon<PERSON><PERSON><PERSON>,
  PanelRight,
  PictureInPicture2,
  Plus,
  X,
} from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { cn } from "#/utils/classnames";
import { useGlobalChat } from "./use-global-chat-context";
import { useOptimizationContext } from "#/utils/optimization/provider";
import { useAvailableModels } from "#/ui/prompts/models";
import { Messages } from "./messages";
import { toolLabels } from "./tool-message-ui";
import { PromptTextArea } from "./prompt-text-area";
import { useFeatureFlags } from "#/lib/feature-flags";
import { usePathname } from "next/navigation";
import {
  isDatasetPage,
  isExperimentPage,
  isLogsPage,
  isPlaygroundPage,
} from "#/app/app/[org]/pathname-checker";
import { useOrg } from "#/utils/user";
import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { useHotkeys } from "react-hotkeys-hook";
import { SessionsHistoryDropdown } from "#/ui/optimization/sessions-history-dropdown";
import { BasicTooltip } from "#/ui/tooltip";
import { AnimatePresence, motion } from "motion/react";
import { useUpsellContext } from "#/app/playground/upsell-dialog";
import { EmptyState } from "./empty-state";

export const OptimizationChat = ({
  hasMultipleSelectedExperiments = false,
  onBTQLFilter,
}: {
  hasMultipleSelectedExperiments?: boolean;
  onBTQLFilter?: (filterText: string) => void;
}) => {
  const {
    editTaskConfirmationData,
    setEditTaskConfirmationData,
    editDatasetConfirmationData,
    setEditDatasetConfirmationData,
    userConsentConfirmationData,
    setUserConsentConfirmationData,
    editScorersConfirmationData,
    setEditScorersConfirmationData,
    createLLMScorerConfirmationData,
    setCreateLLMScorerConfirmationData,
    createCodeScorerConfirmationData,
    setCreateCodeScorerConfirmationData,
    isChatOpen,
    setIsChatOpen,
    isDocked,
    setIsDocked,
    createNewSession,
    chatSessions,
    currentChatSessionId,
    setCurrentChatSessionId,
    currentSessionMessages,
    currentSessionContextObjects,
    model,
    currentSessionTools,
    setCurrentSessionContextObjects,
    setModel,
    setCurrentSessionTools,
    currentSessionUserMessage,
    setCurrentSessionUserMessage,
    currentSessionIsActive,
    setCurrentSessionIsActive,
    handleSendMessage,
    handleAbort,
    deleteSession,
    implementedTools,
    pageKey,
    chat,
    screenTooNarrow,
  } = useGlobalChat();

  const { name: orgName } = useOrg();

  const { configuredModelsByProvider } = useAvailableModels({ orgName });

  const {
    registerDatasetEditConfirmationHandler,
    unregisterDatasetEditConfirmationHandler,
    registerUserConsentConfirmationHandler,
    unregisterUserConsentConfirmationHandler,
    registerEditScorersConfirmationHandler,
    unregisterEditScorersConfirmationHandler,
    registerCreateLLMScorerConfirmationHandler,
    unregisterCreateLLMScorerConfirmationHandler,
    registerCreateCodeScorerConfirmationHandler,
    unregisterCreateCodeScorerConfirmationHandler,
    allowRunningWithoutConsent,
    setAllowRunningWithoutConsent,
    timeRangeSettings,
    setTimeRangeSettings,
  } = useOptimizationContext();
  const textAreaRef = useRef<HTMLTextAreaElement | null>(null);
  const tooltipContentRef = useRef<HTMLDivElement | null>(null);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);

  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [currentSessionMessages.length, isDocked, isChatOpen]);

  useEffect(() => {
    if (
      !registerDatasetEditConfirmationHandler ||
      !unregisterDatasetEditConfirmationHandler ||
      !registerUserConsentConfirmationHandler ||
      !unregisterUserConsentConfirmationHandler ||
      !registerEditScorersConfirmationHandler ||
      !unregisterEditScorersConfirmationHandler ||
      !registerCreateLLMScorerConfirmationHandler ||
      !unregisterCreateLLMScorerConfirmationHandler ||
      !registerCreateCodeScorerConfirmationHandler ||
      !unregisterCreateCodeScorerConfirmationHandler
    ) {
      return;
    }

    registerDatasetEditConfirmationHandler((editDatasetConfirmationData) => {
      setEditDatasetConfirmationData(editDatasetConfirmationData);
    });
    registerUserConsentConfirmationHandler((userConsentConfirmationData) => {
      setUserConsentConfirmationData(userConsentConfirmationData);
    });
    registerEditScorersConfirmationHandler((editScorersConfirmationData) => {
      setEditScorersConfirmationData(editScorersConfirmationData);
    });
    registerCreateLLMScorerConfirmationHandler(
      (createLLMScorerConfirmationData) => {
        setCreateLLMScorerConfirmationData(createLLMScorerConfirmationData);
      },
    );
    registerCreateCodeScorerConfirmationHandler(
      (createCodeScorerConfirmationData) => {
        setCreateCodeScorerConfirmationData(createCodeScorerConfirmationData);
      },
    );

    return () => {
      unregisterDatasetEditConfirmationHandler();
      unregisterUserConsentConfirmationHandler();
      unregisterEditScorersConfirmationHandler();
    };
  }, [
    registerDatasetEditConfirmationHandler,
    unregisterDatasetEditConfirmationHandler,
    registerUserConsentConfirmationHandler,
    unregisterUserConsentConfirmationHandler,
    setEditDatasetConfirmationData,
    setUserConsentConfirmationData,
    registerEditScorersConfirmationHandler,
    unregisterEditScorersConfirmationHandler,
    setEditScorersConfirmationData,
    registerCreateLLMScorerConfirmationHandler,
    unregisterCreateLLMScorerConfirmationHandler,
    setCreateLLMScorerConfirmationData,
    registerCreateCodeScorerConfirmationHandler,
    unregisterCreateCodeScorerConfirmationHandler,
    setCreateCodeScorerConfirmationData,
  ]);

  useEffect(() => {
    if (tooltipContentRef.current && shouldAutoScroll) {
      tooltipContentRef.current.scrollTop =
        tooltipContentRef.current.scrollHeight;
    }
  }, [currentSessionMessages, shouldAutoScroll]);

  const handleScroll = useCallback(() => {
    if (tooltipContentRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        tooltipContentRef.current;
      //4px threshold for determining if user is at the bottom.
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 4;
      setShouldAutoScroll(isAtBottom);
    }
  }, []);

  const isConfirming = Boolean(
    editDatasetConfirmationData ||
      editTaskConfirmationData ||
      userConsentConfirmationData ||
      editScorersConfirmationData ||
      createLLMScorerConfirmationData ||
      createCodeScorerConfirmationData,
  );

  const { onUpsell } = useUpsellContext();

  const handleCreateNewSession = useCallback(() => {
    if (onUpsell) {
      onUpsell();
      return;
    }

    const emptySession = chatSessions.sessions.find(
      (session) =>
        session.messages.length === 0 && session.id !== currentChatSessionId,
    );

    if (emptySession) {
      setCurrentChatSessionId(emptySession.id);
    } else if (currentSessionMessages.length > 0) {
      createNewSession();
    }

    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [
    chatSessions.sessions,
    currentChatSessionId,
    currentSessionMessages.length,
    setCurrentChatSessionId,
    createNewSession,
    onUpsell,
  ]);

  const handleAcceptConfirmation = useCallback(() => {
    if (editDatasetConfirmationData) {
      editDatasetConfirmationData.onConfirm();
      setEditDatasetConfirmationData(null);
    } else if (editTaskConfirmationData) {
      editTaskConfirmationData.onConfirm();
      setEditTaskConfirmationData(null);
    } else if (userConsentConfirmationData) {
      userConsentConfirmationData.onConfirm();
      setUserConsentConfirmationData(null);
    } else if (editScorersConfirmationData) {
      editScorersConfirmationData.onConfirm();
      setEditScorersConfirmationData(null);
    } else if (createLLMScorerConfirmationData) {
      createLLMScorerConfirmationData.onConfirm();
      setCreateLLMScorerConfirmationData(null);
    } else if (createCodeScorerConfirmationData) {
      createCodeScorerConfirmationData.onConfirm();
      setCreateCodeScorerConfirmationData(null);
    }
  }, [
    editDatasetConfirmationData,
    editTaskConfirmationData,
    userConsentConfirmationData,
    editScorersConfirmationData,
    createLLMScorerConfirmationData,
    createCodeScorerConfirmationData,
    setEditDatasetConfirmationData,
    setEditTaskConfirmationData,
    setUserConsentConfirmationData,
    setEditScorersConfirmationData,
    setCreateLLMScorerConfirmationData,
    setCreateCodeScorerConfirmationData,
  ]);

  useHotkeys(
    "Mod+I",
    () => {
      setIsChatOpen((o) => !o);
    },
    {
      preventDefault: true,
      enableOnFormTags: true,
    },
  );

  useHotkeys(
    "Mod+O",
    () => {
      handleCreateNewSession();
    },
    {
      preventDefault: true,
      enableOnFormTags: true,
      enabled: isChatOpen,
    },
  );

  useHotkeys("Mod+Enter", handleAcceptConfirmation, {
    preventDefault: true,
    enabled: isChatOpen && isConfirming,
    enableOnFormTags: true,
  });

  const finalDockedState = screenTooNarrow ? false : isDocked;

  const contents = (
    <div
      className={cn(
        "flex min-h-[360px] h-[360px] w-[400px] flex-col relative overflow-hidden",
        {
          "h-[calc(100vh-200px)]": currentSessionMessages.length > 0,
          [`rounded-none right-0 rounded-tl-md border bg-primary-50 ${HEIGHT_WITH_TOP_OFFSET} fixed top-[44px]`]:
            finalDockedState,
        },
      )}
    >
      <div
        className={cn(
          "flex w-full items-end pointer-events-none gap-1 flex-none",
          {
            "p-[5.5px] border-b": finalDockedState,
            "absolute top-0 left-0 right-0 p-2": !finalDockedState,
          },
        )}
      >
        {finalDockedState && (
          <div className="flex items-center gap-2 self-center pl-2 text-xs font-medium">
            <Blend className="size-3" />
            Loop
          </div>
        )}
        <div className="grow" />
        <BasicTooltip
          tooltipContent={
            finalDockedState
              ? "Undock chat"
              : screenTooNarrow
                ? "Increase screen width to dock chat"
                : "Dock chat"
          }
        >
          <Button
            variant={finalDockedState ? "ghost" : "border"}
            size="icon"
            Icon={finalDockedState ? PictureInPicture2 : PanelRight}
            className={cn(
              "size-7 pointer-events-auto bg-primary-50 hover:bg-primary-200 z-30",
            )}
            disabled={screenTooNarrow}
            onClick={() => {
              setIsDocked((d) => !d);
            }}
          />
        </BasicTooltip>
        <SessionsHistoryDropdown
          isDocked={finalDockedState}
          chatSessions={chatSessions}
          setCurrentChatSessionId={setCurrentChatSessionId}
          deleteSession={deleteSession}
          currentChatSessionId={currentChatSessionId}
          isConfirming={isConfirming}
          size="widget"
          className="z-30"
        />
        <BasicTooltip tooltipContent="New chat">
          <Button
            variant={finalDockedState ? "ghost" : "border"}
            size="icon"
            Icon={Plus}
            onClick={handleCreateNewSession}
            className="pointer-events-auto z-30 size-7 bg-primary-50 hover:bg-primary-200"
          />
        </BasicTooltip>
        <BasicTooltip tooltipContent="Close chat">
          <Button
            variant={finalDockedState ? "ghost" : "border"}
            size="icon"
            className={cn(
              "size-7 pointer-events-auto bg-primary-50 hover:bg-primary-200 z-30",
            )}
            Icon={X}
            onClick={() => setIsChatOpen(false)}
          />
        </BasicTooltip>
      </div>
      <div className="flex min-h-0 flex-1 flex-col">
        <div
          ref={tooltipContentRef}
          className={cn(
            "w-full flex-1 text-xs overflow-y-auto px-3 py-2 pb-10 flex flex-col gap-1",
            {
              "pt-12": !finalDockedState,
            },
          )}
          onScroll={handleScroll}
        >
          {currentSessionMessages.length > 0 ? (
            <Messages
              parsedMessages={currentSessionMessages}
              editTaskConfirmationData={editTaskConfirmationData}
              editDatasetConfirmationData={editDatasetConfirmationData}
              userConsentConfirmationData={userConsentConfirmationData}
              editScorersConfirmationData={editScorersConfirmationData}
              createLLMScorerConfirmationData={createLLMScorerConfirmationData}
              createCodeScorerConfirmationData={
                createCodeScorerConfirmationData
              }
              setEditTaskConfirmationData={setEditTaskConfirmationData}
              setEditDatasetConfirmationData={setEditDatasetConfirmationData}
              setUserConsentConfirmationData={setUserConsentConfirmationData}
              setEditScorersConfirmationData={setEditScorersConfirmationData}
              setCreateLLMScorerConfirmationData={
                setCreateLLMScorerConfirmationData
              }
              setCreateCodeScorerConfirmationData={
                setCreateCodeScorerConfirmationData
              }
              setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
              allowRunningWithoutConsent={allowRunningWithoutConsent}
              pageKey={pageKey}
              setUserMessage={setCurrentSessionUserMessage}
              handleSendMessage={handleSendMessage}
              chat={chat}
              hasMultipleSelectedExperiments={hasMultipleSelectedExperiments}
              size="widget"
              onBTQLFilter={onBTQLFilter}
            />
          ) : (
            <EmptyState
              page={pageKey}
              handleSendMessage={handleSendMessage}
              setUserMessage={setCurrentSessionUserMessage}
              hasMultipleSelectedExperiments={hasMultipleSelectedExperiments}
            />
          )}
        </div>
        <PromptTextArea
          textAreaRef={textAreaRef}
          allowRunningWithoutConsent={allowRunningWithoutConsent}
          setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
          currentModel={model}
          setCurrentModel={setModel}
          currentTool={currentSessionTools}
          setCurrentTool={setCurrentSessionTools}
          configuredModelsByProvider={configuredModelsByProvider}
          contextObjects={currentSessionContextObjects}
          setContextObject={setCurrentSessionContextObjects}
          userMessage={currentSessionUserMessage}
          setUserMessage={setCurrentSessionUserMessage}
          isChatActive={currentSessionIsActive}
          setIsChatActive={setCurrentSessionIsActive}
          isConfirming={isConfirming}
          handleSendMessage={handleSendMessage}
          handleAbort={handleAbort}
          implementedTools={implementedTools}
          setShouldAutoScroll={setShouldAutoScroll}
          pageKey={pageKey}
          hasMultipleSelectedExperiments={hasMultipleSelectedExperiments}
          timeRangeSettings={timeRangeSettings}
          setTimeRangeSettings={setTimeRangeSettings}
        />
      </div>
    </div>
  );

  const lastMessage = currentSessionMessages[currentSessionMessages.length - 1];
  const status =
    lastMessage?.type === "tool_interaction"
      ? toolLabels[pageKey]["pending_output"][lastMessage.functionName]
      : "Generating...";

  return (
    <>
      {isChatOpen && finalDockedState && contents}
      <Popover open={isChatOpen} onOpenChange={setIsChatOpen}>
        <PopoverTrigger asChild>
          <Button
            size="xs"
            className={cn(
              "relative group bg-transparent hover:bg-accent-50 transition-all duration-200 w-16 justify-start overflow-hidden",
              {
                hidden: finalDockedState && isChatOpen,
                "bg-accent-50 hover:bg-accent-100 w-32":
                  !isChatOpen && currentSessionIsActive && !isConfirming,
                "border-accent-200": isConfirming && currentSessionIsActive,
              },
            )}
            onClick={() => {
              setIsChatOpen((o) => !o);
            }}
          >
            {isConfirming && currentSessionIsActive ? (
              <OctagonAlert className="size-3 rounded-full text-accent-500" />
            ) : (
              <Blend
                className={cn(
                  "size-3 transition-all duration-500 group-hover:rotate-90 group-hover:text-accent-500",
                  {
                    "animate-smooth-spin will-change-transform transition-opacity duration-400":
                      currentSessionIsActive && !isConfirming && !isChatOpen,
                  },
                )}
              />
            )}
            <AnimatePresence mode="wait">
              {!isChatOpen && currentSessionIsActive && !isConfirming ? (
                <motion.div
                  key={status}
                  initial={{ opacity: 0, filter: "blur(2px)", translateY: 10 }}
                  animate={{ opacity: 1, filter: "blur(0px)", translateY: 0 }}
                  exit={{ opacity: 0.5, filter: "blur(2px)", translateY: -10 }}
                  transition={{ duration: 0.2 }}
                  className="flex-1 animate-textShimmer truncate bg-gradient-to-r from-primary-300 via-primary-600 to-primary-300 bg-clip-text text-start text-xs text-transparent"
                >
                  {status}
                </motion.div>
              ) : (
                <motion.span
                  initial={{ opacity: 0, filter: "blur(2px)", translateY: 10 }}
                  animate={{ opacity: 1, filter: "blur(0px)", translateY: 0 }}
                  exit={{ opacity: 0.5, filter: "blur(2px)", translateY: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  Loop
                </motion.span>
              )}
            </AnimatePresence>
          </Button>
        </PopoverTrigger>
        {!finalDockedState && (
          <PopoverContent
            onOpenAutoFocus={(e) => {
              e.preventDefault();
              if (textAreaRef.current) {
                textAreaRef.current.focus();
              }
            }}
            align="start"
            collisionPadding={12}
            className="w-[400px] p-0 bg-primary-50"
          >
            {contents}
          </PopoverContent>
        )}
      </Popover>
    </>
  );
};

export const useIsLoopEnabled = () => {
  const pathname = usePathname();
  const { flags, isLoading } = useFeatureFlags();

  const isPlayground = isPlaygroundPage(pathname ?? "");
  const isExperiment = isExperimentPage(pathname ?? "");
  const isLogs = isLogsPage(pathname ?? "");
  const isDataset = isDatasetPage(pathname ?? "");
  const matches = isPlayground || isExperiment || isDataset || isLogs;

  return Boolean(!isLoading && flags.loop && matches);
};
