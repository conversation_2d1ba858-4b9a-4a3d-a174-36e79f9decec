import { type ToolName } from "@braintrust/local/optimization/tools";
import {
  type ContextObject,
  type UserMessage,
} from "#/ui/optimization/use-global-chat-context";
import { <PERSON><PERSON> } from "#/ui/button";
import TextArea from "#/ui/text-area";
import { StopCircle, CircleArrowUp } from "lucide-react";
import { OptimizationModelsDropdown } from "./optimization-models-dropdown";
import { type ModelDetails } from "#/ui/prompts/models";
import { SettingsDropdown } from "./settings-dropdown";
import { AnimatePresence, motion } from "motion/react";
import { type PageKey } from "@braintrust/local/optimization";
import { cn } from "#/utils/classnames";
import { ContextObjectBadgeSection } from "./messages";
import { type TimeRangeFilter } from "#/utils/view/use-view";

export const PromptTextArea = ({
  currentModel,
  currentTool,
  setCurrentModel,
  setCurrentTool,
  configuredModelsByProvider,
  contextObjects,
  setContextObject,
  userMessage,
  setUserMessage,
  isChatActive,
  handleAbort,
  allowRunningWithoutConsent,
  setAllowRunningWithoutConsent,
  isConfirming,
  handleSendMessage,
  textAreaRef,
  implementedTools,
  setShouldAutoScroll,
  pageKey,
  hasMultipleSelectedExperiments,
  size = "widget",
  minRows = 1,
  timeRangeSettings,
  setTimeRangeSettings,
}: {
  allowRunningWithoutConsent: boolean;
  setAllowRunningWithoutConsent: (consent: boolean) => void;
  currentModel: string;
  currentTool?: ToolName[];
  setCurrentModel: (model: string) => void;
  setCurrentTool: (
    tools: ToolName[] | ((prev: ToolName[]) => ToolName[]),
  ) => void;
  configuredModelsByProvider: Record<string, ModelDetails[]>;
  contextObjects: Record<string, ContextObject>;
  setContextObject: (contextObject: Record<string, ContextObject>) => void;
  userMessage: string;
  setUserMessage: (userMessage: string) => void;
  isChatActive: boolean;
  setIsChatActive: (isChatActive: boolean) => void;
  isConfirming?: boolean;
  handleSendMessage: (
    userMessage: UserMessage,
    options?: {
      clearContextObjects?: boolean;
      clearUserMessage?: boolean;
    },
  ) => void;
  handleAbort: () => void;
  textAreaRef: React.RefObject<HTMLTextAreaElement | null>;
  implementedTools: ToolName[];
  setShouldAutoScroll: (shouldAutoScroll: boolean) => void;
  pageKey: PageKey;
  hasMultipleSelectedExperiments?: boolean;
  size?: "widget" | "full";
  minRows?: number;
  timeRangeSettings: TimeRangeFilter;
  setTimeRangeSettings: (timeRange: TimeRangeFilter) => void;
}) => {
  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // this means the user is confirming a user consent tool interaction. Early return to avoid sending a message.
    if (
      event.key === "Enter" &&
      (event.metaKey || event.ctrlKey) &&
      isConfirming
    ) {
      return;
    }
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      setShouldAutoScroll(true);
      handleSendMessage(
        {
          id: crypto.randomUUID(),
          type: "user_message",
          message: userMessage,
          contextObjects: contextObjects,
        },
        {
          clearContextObjects: true,
          clearUserMessage: true,
        },
      );
    }
  };

  return (
    <div className="relative m-2 mt-auto flex max-h-48 flex-none flex-col">
      <AnimatePresence>
        {isChatActive && !isConfirming && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute inset-x-0 -top-10 flex h-10 items-end justify-center bg-gradient-to-t from-primary-50 from-50% to-transparent"
          >
            <span className="animate-textShimmer bg-gradient-to-r from-primary-300 via-primary-600 to-primary-300 bg-clip-text pb-1 text-xs text-transparent">
              Generating...
            </span>
          </motion.div>
        )}
      </AnimatePresence>
      <div className="flex flex-1 flex-col overflow-auto rounded-md border transition-colors bg-background focus-within:border-primary-400/80">
        {Object.values(contextObjects).length > 0 && (
          <div className="px-2 pt-2">
            <ContextObjectBadgeSection
              contextObjects={contextObjects}
              onDelete={(contextObject) => {
                const { [contextObject.id]: _, ...newContextObject } =
                  contextObjects;
                setContextObject(newContextObject);
              }}
              onClearAll={() => {
                setContextObject({});
              }}
            />
          </div>
        )}
        <div className="flex items-center gap-1 rounded-md">
          <TextArea
            minRows={minRows}
            className={cn(
              "flex-1 resize-none border-0 pt-3 text-xs bg-background placeholder:text-primary-500/90 focus-visible:ring-0 dark:bg-primary-50",
              size === "full" && "text-sm",
            )}
            placeholder={
              pageKey === "experiments"
                ? `Ask anything about ${hasMultipleSelectedExperiments ? "selected experiments" : "the experiment"}`
                : pageKey === "loop"
                  ? "What would you like to know about your project?"
                  : "What would you like to optimize or generate?"
            }
            value={userMessage}
            onChange={(e) => {
              setUserMessage(e.target.value);
            }}
            onKeyDown={handleKeyDown}
            ref={textAreaRef}
          />
        </div>
        <div className="flex gap-1 pb-1 pl-2 pr-1">
          <SettingsDropdown
            allowRunningWithoutConsent={allowRunningWithoutConsent}
            setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
            currentTool={currentTool}
            setCurrentTool={setCurrentTool}
            implementedTools={implementedTools}
            size={size}
            pageKey={pageKey}
            timeRangeSettings={timeRangeSettings}
            setTimeRangeSettings={setTimeRangeSettings}
          />
          <OptimizationModelsDropdown
            currentModel={currentModel}
            setCurrentModel={setCurrentModel}
            configuredModelsByProvider={configuredModelsByProvider}
            size={size}
          />
          {isChatActive ? (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "ml-auto size-7 hover:bg-primary-200",
                size === "full" && "size-8",
              )}
              iconClassName="size-4"
              Icon={StopCircle}
              onClick={handleAbort}
            />
          ) : (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "ml-auto size-7 hover:bg-primary-200",
                size === "full" && "size-8",
              )}
              iconClassName={cn("size-4", size === "full" && "size-5")}
              Icon={CircleArrowUp}
              disabled={!userMessage.trim()}
              onClick={() => {
                setShouldAutoScroll(true);
                handleSendMessage(
                  {
                    id: crypto.randomUUID(),
                    type: "user_message",
                    message: userMessage,
                    contextObjects: contextObjects,
                  },
                  {
                    clearContextObjects: true,
                    clearUserMessage: true,
                  },
                );
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};
