import {
  createContext,
  type Dispatch,
  type SetStateAction,
  useContext,
} from "react";
import {
  type ToolExecution,
  type Chat<PERSON>ontext,
  UserRejectedError,
  type <PERSON>Key,
} from "@braintrust/local/optimization";
import {
  type TaskEditConfirmationData,
  type DatasetEditConfirmationData,
  type UserConsentConfirmationData,
  type EditScorersConfirmationData,
  type CreateLLMScorerConfirmationData,
  type CreateCodeScorerConfirmationData,
} from "#/utils/optimization/provider";
import {
  isValidToolName,
  type ToolName,
} from "@braintrust/local/optimization/tools";
import { newId } from "#/utils/btapi/btapi";
import { type ChatLogger } from "@braintrust/local/optimization";
import {
  type StreamingTextDelta,
  type StreamingToolDelta,
} from "@braintrust/local/functions";

export type ContextObject =
  | TaskContextObject
  | DatasetContextObject
  | ScorerContextObject
  | TraceContextObject;

export interface TraceContextObject {
  id: string;
  resource: "trace";
  name: string;
  created?: number;
  error?: string | null;
  expected?: string;
  input?: string;
  metadata?: Record<string, unknown> | null;
  metrics?: Record<string, unknown>;
  origin?: string;
  output?: string;
  scores?: Record<string, number>;
  tags?: string[] | null;
}

export interface ScorerContextObject {
  id: string;
  resource: "scorer";
  name: string;
  description: string;
}

export interface TaskContextObject {
  id: string;
  resource: "task";
  name: string;
  description: string;
  index: number;
  promptData: object;
  functionData: object;
  metadata: object;
}

export interface DatasetContextObject {
  id: string;
  resource: "dataset";
  name: string;
  datasetId: string;
  numberOfRecords?: number;
  comparison_key?: string;
  created?: number;
  error?: string | null;
  expected?: string;
  input?: string;
  metadata?: Record<string, unknown> | null;
  metrics?: Record<string, unknown>;
  origin?: string;
  output?: string;
  scores?: Record<string, number>;
  tags?: string[] | null;
}

export type Mode = "agent" | "crazy" | "tame";

export interface ChatSession {
  id: string;
  name?: string;
  createdAt: string;
  updatedAt: string;
  messages: ParsedMessage[];
  contextObjects: Record<string, ContextObject>;
  tools: ToolName[];
  mode: Mode;
  userMessage: string;
  isActive: boolean;
}

export interface ChatSessions {
  sessions: ChatSession[];
}

export interface GlobalChatContextType {
  chat: ChatContext | null;
  isChatOpen: boolean;
  setIsChatOpen: Dispatch<SetStateAction<boolean>>;
  isAwaitingEditConfirmation: boolean;
  setIsAwaitingEditConfirmation: (awaiting: boolean) => void;
  editTaskConfirmationData: TaskEditConfirmationData | null;
  setEditTaskConfirmationData: (data: TaskEditConfirmationData | null) => void;
  editDatasetConfirmationData: DatasetEditConfirmationData | null;
  setEditDatasetConfirmationData: (
    data: DatasetEditConfirmationData | null,
  ) => void;
  editScorersConfirmationData: EditScorersConfirmationData | null;
  setEditScorersConfirmationData: (
    data: EditScorersConfirmationData | null,
  ) => void;
  userConsentConfirmationData: UserConsentConfirmationData | null;
  setUserConsentConfirmationData: (
    data: UserConsentConfirmationData | null,
  ) => void;
  createLLMScorerConfirmationData: CreateLLMScorerConfirmationData | null;
  setCreateLLMScorerConfirmationData: (
    data: CreateLLMScorerConfirmationData | null,
  ) => void;
  createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null;
  setCreateCodeScorerConfirmationData: (
    data: CreateCodeScorerConfirmationData | null,
  ) => void;
  isDocked: boolean;
  setIsDocked: Dispatch<SetStateAction<boolean>>;
  createNewSession: () => void;
  chatSessions: ChatSessions;
  setChatSessions: (
    sessions: ChatSessions | ((prev: ChatSessions) => ChatSessions),
  ) => void;

  currentChatSessionId: string;
  setCurrentChatSessionId: (id: string) => void;
  currentSessionUserMessage: string;
  currentSessionMessages: ParsedMessage[];
  currentSessionContextObjects: Record<string, ContextObject>;
  model: string;
  currentSessionTools: ToolName[];
  currentSessionMode: Mode;
  currentSessionIsActive: boolean;
  setCurrentSessionMessages: (messages: ParsedMessage[]) => void;
  setCurrentSessionContextObjects: (
    contextObjects:
      | Record<string, ContextObject>
      | ((
          prev: Record<string, ContextObject>,
        ) => Record<string, ContextObject>),
  ) => void;
  setModel: (model: string) => void;
  setCurrentSessionTools: (
    tools: ToolName[] | ((prev: ToolName[]) => ToolName[]),
  ) => void;
  setCurrentSessionMode: (mode: Mode) => void;
  setCurrentSessionIsActive: (isActive: boolean) => void;
  setCurrentSessionUserMessage: (userMessage: string) => void;
  handleSendMessage: (
    userMessage: UserMessage,
    options?: {
      clearContextObjects?: boolean;
      clearUserMessage?: boolean;
    },
  ) => Promise<void>;
  handleAbort: () => void;
  deleteSession: (sessionId: string) => void;
  implementedTools: ToolName[];
  pageKey: PageKey;
  screenTooNarrow: boolean;
}

export const GlobalChatContext = createContext<GlobalChatContextType>({
  chat: null,
  isChatOpen: false,
  setIsChatOpen: () => {},
  isAwaitingEditConfirmation: false,
  setIsAwaitingEditConfirmation: () => {},
  editTaskConfirmationData: null,
  setEditTaskConfirmationData: () => {},
  editDatasetConfirmationData: null,
  setEditDatasetConfirmationData: () => {},
  editScorersConfirmationData: null,
  setEditScorersConfirmationData: () => {},
  userConsentConfirmationData: null,
  setUserConsentConfirmationData: () => {},
  createLLMScorerConfirmationData: null,
  setCreateLLMScorerConfirmationData: () => {},
  createCodeScorerConfirmationData: null,
  setCreateCodeScorerConfirmationData: () => {},
  isDocked: false,
  setIsDocked: () => {},
  createNewSession: () => {
    throw new Error(
      "createNewSession must be used within a GlobalChatProvider",
    );
  },
  chatSessions: { sessions: [] },
  setChatSessions: () => {},
  currentChatSessionId: "",
  setCurrentChatSessionId: () => {},
  currentSessionUserMessage: "",
  currentSessionMessages: [],
  currentSessionContextObjects: {},
  model: "",
  currentSessionTools: [],
  currentSessionMode: "agent",
  currentSessionIsActive: false,
  setCurrentSessionMessages: () => {},
  setCurrentSessionContextObjects: () => {},
  setModel: () => {},
  setCurrentSessionTools: () => {},
  setCurrentSessionMode: () => {},
  setCurrentSessionIsActive: () => {},
  setCurrentSessionUserMessage: () => {},
  handleSendMessage: async () => {
    throw new Error(
      "handleSendMessage must be used within a GlobalChatProvider",
    );
  },
  handleAbort: () => {
    throw new Error("handleAbort must be used within a GlobalChatProvider");
  },
  deleteSession: () => {
    throw new Error("deleteSession must be used within a GlobalChatProvider");
  },
  implementedTools: [],
  pageKey: "unknown",
  screenTooNarrow: false,
});

export function useGlobalChat() {
  return useContext(GlobalChatContext);
}

export interface UserMessage {
  id: string;
  type: "user_message";
  message: string;
  contextObjects?: Record<string, ContextObject>;
}

export interface LLMMessage {
  id: string;
  type: "llm_message";
  llmContent: string;
  isLastMessageOfTurn?: boolean;
}

export interface SystemMessage {
  id: string;
  type: "system_message";
  message: string;
  variant?: "error" | "info";
  isLastMessageOfTurn?: boolean;
}
export enum ToolInteractionStatus {
  PENDING_OUTPUT = "pending_output",
  COMPLETED = "completed",
  ERROR_EXECUTING_TOOL = "error_executing_tool",
  REJECTED = "rejected",
}

export type ToolInteraction = {
  id: string;
  type: "tool_interaction";
  toolCallId: string;
  functionName: string;
  arguments: unknown;
  status: ToolInteractionStatus;
  isLastMessageOfTurn?: boolean;
} & (
  | { status: ToolInteractionStatus.PENDING_OUTPUT }
  | { status: ToolInteractionStatus.COMPLETED; toolOutput: unknown }
  | {
      status:
        | ToolInteractionStatus.ERROR_EXECUTING_TOOL
        | ToolInteractionStatus.REJECTED;
      toolOutput: unknown;
      error: { error: string };
    }
);

export type ParsedMessage =
  | UserMessage
  | LLMMessage
  | SystemMessage
  | ToolInteraction;

export class UIStateUpdatingLogger implements ChatLogger {
  private onStreamingTextUpdate: (parsedMessages: ParsedMessage[]) => void;

  private currentTool: StreamingToolDelta = {};
  private parsedMessages: ParsedMessage[] = [];

  constructor(
    onStreamingTextUpdate: (parsedMessages: ParsedMessage[]) => void,
  ) {
    this.onStreamingTextUpdate = onStreamingTextUpdate;
  }

  protected publishMessages(): void {
    this.onStreamingTextUpdate(Array.from(this.parsedMessages));
  }

  public addUserMessage(
    content: string,
    contextObjects?: Record<string, ContextObject>,
  ): void {
    this.parsedMessages.push({
      id: newId(),
      type: "user_message",
      message: content,
      contextObjects: contextObjects,
    });

    this.publishMessages();
  }

  public addSystemMessage(
    content: string,
    variant: "error" | "info" = "info",
  ): void {
    this.parsedMessages.push({
      id: newId(),
      type: "system_message",
      message: content,
      variant: variant,
    });

    this.publishMessages();
  }

  onTextDelta(text: StreamingTextDelta): void {
    const lastMessage = this.parsedMessages[this.parsedMessages.length - 1];
    if (lastMessage && lastMessage.type === "llm_message") {
      lastMessage.llmContent += text;
    } else {
      this.parsedMessages.push({
        id: newId(),
        type: "llm_message",
        llmContent: text,
      });
    }

    this.publishMessages();
  }

  onToolDelta(toolDelta: StreamingToolDelta): void {
    if (
      toolDelta.tool_call_id !== undefined &&
      this.currentTool.tool_call_id !== toolDelta.tool_call_id
    ) {
      this.flushCurrentTool();
      this.publishMessages();
      this.currentTool = {
        tool_call_id: toolDelta.tool_call_id,
      };
    }

    if (toolDelta.function_name) {
      this.currentTool.function_name = toolDelta.function_name;
    }
    if (toolDelta.arguments) {
      this.currentTool.arguments =
        (this.currentTool.arguments ?? "") + toolDelta.arguments;
    }
  }

  onToolExecution(toolExecution: ToolExecution): void {
    const interactionIndex = this.parsedMessages.findIndex(
      (msg) => msg.id === toolExecution.tool_call_id,
    );
    if (interactionIndex === -1) {
      // TODO: should these be louder errors? Can we show errors in the UI?
      console.warn(
        `Tool execution for unknown tool call ${toolExecution.tool_call_id}`,
        toolExecution,
      );
      return;
    }
    const existingInteraction = this.parsedMessages[interactionIndex];
    if (existingInteraction.type !== "tool_interaction") {
      console.warn("Tool execution for non-tool interaction", toolExecution);
      return;
    }

    if (toolExecution.error) {
      const isUserRejection =
        toolExecution.error instanceof UserRejectedError ||
        toolExecution.error.name === "AbortError";

      const newInteraction: ToolInteraction = {
        ...existingInteraction,
        status: isUserRejection
          ? ToolInteractionStatus.REJECTED
          : ToolInteractionStatus.ERROR_EXECUTING_TOOL,
        toolOutput: toolExecution.output,
        error: { error: toolExecution.error.message },
      };
      this.parsedMessages[interactionIndex] = newInteraction;
    } else {
      const updatedInteraction: ToolInteraction = {
        ...existingInteraction,
        status: ToolInteractionStatus.COMPLETED,
        toolOutput: toolExecution.output,
      };
      this.parsedMessages[interactionIndex] = updatedInteraction;
    }
    this.publishMessages();
  }

  private flushCurrentTool(): void {
    if (
      this.currentTool.tool_call_id === undefined ||
      this.currentTool.function_name === undefined
    ) {
      if (this.currentTool.tool_call_id || this.currentTool.function_name) {
        console.warn("Invalid partial tool call", this.currentTool);
      }
      return;
    }
    const toolName = this.currentTool.function_name;
    if (!isValidToolName(toolName)) {
      console.warn(
        `Invalid tool name "${toolName}". Skipping`,
        this.currentTool,
      );
      return;
    }
    let parsedArguments;
    try {
      parsedArguments = this.currentTool.arguments
        ? JSON.parse(this.currentTool.arguments)
        : {};
    } catch {
      parsedArguments = this.currentTool.arguments;
    }
    const newInteraction: ToolInteraction = {
      id: this.currentTool.tool_call_id,
      type: "tool_interaction",
      toolCallId: this.currentTool.tool_call_id,
      functionName: toolName,
      status: ToolInteractionStatus.PENDING_OUTPUT,
      arguments: parsedArguments,
    };

    this.currentTool = {};

    this.parsedMessages.push(newInteraction);
  }

  onTurnComplete(): void {
    if (this.parsedMessages.length > 0) {
      for (let i = this.parsedMessages.length - 1; i >= 0; i--) {
        const message = this.parsedMessages[i];
        if (
          message.type === "llm_message" ||
          message.type === "tool_interaction" ||
          message.type === "system_message"
        ) {
          message.isLastMessageOfTurn = true;
          break;
        }
      }

      this.publishMessages();
    }
  }

  flush(): void {
    this.flushCurrentTool();
    this.publishMessages();
  }

  getParsedMessages(): ParsedMessage[] {
    return [...this.parsedMessages];
  }

  clearMessages(): void {
    this.parsedMessages = [];
  }
}
