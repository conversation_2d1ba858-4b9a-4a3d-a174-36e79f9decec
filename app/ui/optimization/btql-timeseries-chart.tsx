"use client";

import { useMemo } from "react";
import { type ParsedQuery } from "@braintrust/btql/parser";
import { useHighlightState } from "#/ui/charts/highlight";
import { formatValueForSelectionType } from "#/ui/charts/selectionTypes";
import { type TimeseriesData } from "#/ui/charts/timeseries-data/chart-data.types";
import { TimeseriesChart } from "#/ui/charts/timeseries/timeseries-chart";
import { type ChartTimeFrame } from "#/app/app/[org]/monitor/time-controls/time-range";
import { getSeriesAggregates } from "#/ui/charts/timeseries-data/get-series-aggregates";

interface TimeseriesChartFromAstProps {
  ast: ParsedQuery;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any[];
  forcedVizType?: "lines" | "bars";
}

export function TimeseriesChartFromAst({
  ast,
  data,
  forcedVizType,
}: TimeseriesChartFromAstProps) {
  const highlightState = useHighlightState();

  // Transform BTQL data into TimeseriesData format
  const { timeseriesData, chartTimeFrame, bucketCount } = useMemo(() => {
    if (!data || data.length === 0) {
      const emptyData: TimeseriesData<
        { count: bigint },
        { name: string; selectionType: { type: "metric"; value: string } }
      > = {
        timestamps: [],
        timeBucketDuration: 0,
        timeMetadata: [],
        seriesValues: [],
        seriesMetadata: [],
        aggregates: [],
      };
      const emptyTimeFrame: ChartTimeFrame = {
        start: Date.now(),
        end: Date.now(),
      };
      return {
        timeseriesData: emptyData,
        chartTimeFrame: emptyTimeFrame,
        bucketCount: 0,
      };
    }

    // Get time dimension
    const timeDimension = (ast.dimensions || []).find((dim) => {
      if (dim.expr && "op" in dim.expr && dim.expr.op === "function") {
        const funcNameObj = dim.expr.name;
        let funcNameStr = "";
        if (
          funcNameObj &&
          funcNameObj.name &&
          Array.isArray(funcNameObj.name)
        ) {
          funcNameStr = funcNameObj.name[0]?.toString() || "";
        }
        return ["minute", "hour", "day", "week", "month", "year"].includes(
          funcNameStr,
        );
      }
      return false;
    });

    const timeField = timeDimension?.alias || "time";

    // Get grouping dimensions (non-time dimensions)
    const groupDimensions = (ast.dimensions || []).filter((dim) => {
      if (dim.expr && "op" in dim.expr && dim.expr.op === "function") {
        const funcNameObj = dim.expr.name;
        let funcNameStr = "";
        if (
          funcNameObj &&
          funcNameObj.name &&
          Array.isArray(funcNameObj.name)
        ) {
          funcNameStr = funcNameObj.name[0]?.toString() || "";
        }
        return !["minute", "hour", "day", "week", "month", "year"].includes(
          funcNameStr,
        );
      }
      return true;
    });

    // Get measures
    const measures = ast.measures || [];

    // Create a map to store all series data
    const seriesDataMap = new Map<string, Map<number, number>>();
    const allTimes = new Set<number>();

    // Count data points per timestamp for metadata
    const timeCounts = new Map<number, number>();

    // If we have multiple measures, each measure becomes a series
    // If we have grouping dimensions, we create series for each group+measure combination
    data.forEach((row) => {
      const timestamp = new Date(row[timeField]).getTime();
      allTimes.add(timestamp);

      // Increment count for this timestamp
      timeCounts.set(timestamp, (timeCounts.get(timestamp) || 0) + 1);

      // Get grouping key if we have grouping dimensions
      let groupKey = "";
      if (groupDimensions.length > 0) {
        const groupValues = groupDimensions.map(
          (dim) => row[dim.alias || "group"] || "null",
        );
        groupKey = groupValues.join(" - ");
      }

      // Create a series for each measure
      measures.forEach((measure) => {
        const measureAlias = measure.alias || "value";
        const value = row[measureAlias];

        // Series key is either just the measure name, or group + measure
        const seriesKey = groupKey
          ? `${groupKey} - ${measureAlias}`
          : measureAlias;

        if (!seriesDataMap.has(seriesKey)) {
          seriesDataMap.set(seriesKey, new Map());
        }

        if (typeof value === "number") {
          seriesDataMap.get(seriesKey)!.set(timestamp, value);
        }
      });
    });

    // Get all series names and sort them for consistent ordering
    const seriesArray = Array.from(seriesDataMap.keys()).sort();

    // Convert to TimeseriesData format
    const sortedTimes = Array.from(allTimes).sort((a, b) => a - b);

    // Calculate time bucket duration (use median of differences)
    let timeBucketDuration = 60000; // Default to 1 minute
    if (sortedTimes.length > 1) {
      const diffs = [];
      for (let i = 1; i < sortedTimes.length; i++) {
        diffs.push(sortedTimes[i] - sortedTimes[i - 1]);
      }
      diffs.sort((a, b) => a - b);
      timeBucketDuration = diffs[Math.floor(diffs.length / 2)];
    }

    // Create Float64Arrays for series values
    const seriesValues = seriesArray.map((seriesName) => {
      const values = new Float64Array(sortedTimes.length);
      sortedTimes.forEach((timestamp, i) => {
        const value = seriesDataMap.get(seriesName)?.get(timestamp);
        values[i] = value !== undefined ? value : NaN;
      });
      return values;
    });

    // Create time metadata
    const timeMetadata = sortedTimes.map((timestamp) => ({
      count: BigInt(timeCounts.get(timestamp) || 0),
    }));

    // Create series metadata with SelectionType
    const seriesMetadata = seriesArray.map((name) => ({
      name,
      selectionType: {
        type: "metric" as const,
        value: name,
      },
    }));

    // Calculate aggregates
    const aggregates = getSeriesAggregates(seriesValues);

    const timeseriesData: TimeseriesData<
      { count: bigint },
      { name: string; selectionType: { type: "metric"; value: string } }
    > = {
      timestamps: sortedTimes,
      timeBucketDuration,
      timeMetadata,
      seriesValues,
      seriesMetadata,
      aggregates,
    };

    // Create chart time frame
    const chartTimeFrame: ChartTimeFrame = {
      start: sortedTimes[0] || Date.now(),
      end: sortedTimes[sortedTimes.length - 1] || Date.now(),
    };

    return { timeseriesData, chartTimeFrame, bucketCount: sortedTimes.length };
  }, [ast, data]);

  if (!data || data.length === 0) {
    return (
      <div className="flex h-64 items-center justify-center text-sm text-primary-500">
        No data available
      </div>
    );
  }

  // Determine visualization type: respect explicit override, else auto-select by bucket count
  const AUTO_BAR_BUCKET_THRESHOLD = 60;
  const resolvedVizType: "lines" | "bars" =
    forcedVizType ??
    (bucketCount <= AUTO_BAR_BUCKET_THRESHOLD ? "bars" : "lines");

  return (
    <TimeseriesChart
      timeseriesData={timeseriesData}
      chartTimeFrame={chartTimeFrame}
      highlightState={highlightState}
      aggregateFormatter={(value: number) =>
        formatValueForSelectionType(value, {
          type: "metric",
          value: "value",
        })
      }
      vizType={resolvedVizType}
    />
  );
}
