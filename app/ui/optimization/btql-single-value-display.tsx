//TO-DO vibe-coded the crap out of this.. Need to verify and clean up
"use client";

import { useMemo } from "react";
import { type ParsedQuery } from "@braintrust/btql/parser";
import { cn } from "#/utils/classnames";
import {
  formatValueForSelectionType,
  type SelectionType,
} from "#/ui/charts/selectionTypes";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

interface BTQLSingleValueDisplayProps {
  ast: ParsedQuery;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any[];
  explanation?: string;
  className?: string;
}

interface SingleValueCardProps {
  title: string;
  value: number | string;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  selectionType?: SelectionType;
  className?: string;
}

// Reusable KPI Card component
export function SingleValueCard({
  title,
  value,
  subtitle,
  trend,
  selectionType,
  className,
}: SingleValueCardProps) {
  const formattedValue = useMemo(() => {
    if (selectionType && typeof value === "number") {
      return formatValueForSelectionType(value, selectionType);
    }
    if (typeof value === "number") {
      return value.toLocaleString();
    }
    return value;
  }, [value, selectionType]);

  const TrendIcon = trend
    ? trend.value > 0
      ? TrendingUp
      : trend.value < 0
        ? TrendingDown
        : Minus
    : null;

  return (
    <div
      className={cn(
        "flex flex-col gap-1 border bg-background p-2 rounded-md flex-1",
        className,
      )}
    >
      <div className="truncate font-mono text-xs text-primary-500">{title}</div>
      <div className="text-xs font-medium text-primary-900">
        {formattedValue}
      </div>
      {subtitle && <div className="text-xs text-primary-400">{subtitle}</div>}
      {trend && TrendIcon && (
        <div
          className={cn(
            "flex items-center gap-1 text-xs",
            trend.isPositive ? "text-good-600" : "text-bad-600",
          )}
        >
          <TrendIcon className="size-3" />
          <span>{Math.abs(trend.value).toFixed(1)}%</span>
        </div>
      )}
    </div>
  );
}

export function BTQLSingleValueDisplay({
  ast,
  data,
  explanation,
  className,
}: BTQLSingleValueDisplayProps) {
  // Extract measures from AST
  const measures = useMemo(() => {
    return ast.measures || [];
  }, [ast]);

  // Get KPI values from data
  const kpiData = useMemo((): Array<{
    title: string;
    value: number | string;
    selectionType: SelectionType;
  }> => {
    if (!data || data.length === 0) return [];

    // For multiple measures, show them all as separate KPIs
    if (measures.length > 1) {
      return measures.map((measure) => {
        const alias = measure.alias || "value";
        const value = data[0]?.[alias] ?? 0;
        const selectionType: SelectionType = { type: "metric", value: alias };

        return {
          title: alias,
          value,
          selectionType,
        };
      });
    }

    // For single measure, show it as one KPI
    const measure = measures[0];
    const alias = measure?.alias || "value";
    const value = data[0]?.[alias] ?? 0;
    const selectionType: SelectionType = { type: "metric", value: alias };

    return [
      {
        title: explanation || alias,
        value,
        selectionType,
      },
    ];
  }, [data, measures, explanation]);

  if (kpiData.length === 0) {
    return (
      <div className="flex h-64 items-center justify-center text-sm text-primary-500">
        No data available
      </div>
    );
  }

  return (
    <div className={"flex flex-wrap gap-2"}>
      {kpiData.map((kpi, index) => (
        <SingleValueCard key={index} {...kpi} />
      ))}
    </div>
  );
}
