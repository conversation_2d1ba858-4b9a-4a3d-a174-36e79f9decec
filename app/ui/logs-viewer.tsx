// This file is very similar to, but not exactly the same as, summary-logs-viewer.tsx. It is being deprecated in favor
// of summary-logs-viewer.tsx. If you make changes to it, make sure you make the same changes to summary-logs-viewer.tsx.
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useAnalytics } from "#/ui/use-analytics";
import { type Clause } from "#/utils/search/search";
import {
  type SetStateAction,
  type Dispatch,
  useCallback,
  useContext,
  useMemo,
  useRef,
  useState,
} from "react";
import { type SavingState } from "./saving";
import { TableEmptyState } from "./table/TableEmptyState";
import { singleQuote } from "#/utils/sql-utils";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { deriveScoreFields, useLogScoreSummary } from "./use-log-summary";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "./resizable";
import { MainContentWrapper } from "./layout/main-content-wrapper";
import { LogsProgressInsight } from "#/app/app/[org]/p/[project]/(ExperimentsChart)/logs-progress-insight";
import { Bubble } from "./table/bubble";
import { formatFullDateTime } from "#/utils/date";
import { usePanelSize } from "./use-panel-size";
import {
  INITIALLY_VISIBLE_COLUMNS,
  usePaginatedObjectViewerDataComponents,
  usePaginatedObjectViewerVizComponents,
} from "./paginated-object-viewer";
import { type TraceViewParams } from "./trace/trace";
import { ObjectPermissionsDialog } from "#/app/app/[org]/p/[project]/permissions/object-permissions-dialog";
import { TableRowHeightToggle } from "./table-row-height-toggle";
import { useTraceFullscreen } from "./trace/use-trace-fullscreen";
import { TableSkeleton } from "./table/table-skeleton";
import { Skeleton } from "./skeleton";
import { type ViewType } from "@braintrust/core/typespecs";
import { useViewStates, type ViewParams } from "#/utils/view/use-view";
import { useClauseChecker } from "#/utils/search-btql";
import {
  BUILT_IN_CUSTOM_COLUMNS,
  useCustomColumns,
} from "#/utils/custom-columns/use-custom-columns";
import { type Table as ArrowTable, type TypeMap } from "apache-arrow";
import { useGetRowsForExport } from "#/utils/data-object";
import Footer from "./landing/footer";
import columnReorderer from "#/app/app/[org]/p/[project]/experiments/[experiment]/table-column-reorderer";
import { type RowId } from "#/utils/diffs/diff-objects";
import {
  BodyWrapper,
  HEIGHT_WITH_DOUBLE_TOP_OFFSET,
} from "#/app/app/body-wrapper";
import { cn } from "#/utils/classnames";
import { LogsHeader } from "#/app/app/[org]/p/[project]/logs/logs-header";
import { LogsSparklines } from "#/app/app/[org]/p/[project]/logs-sparklines";
import { ALL_TIME_RANGE } from "./time-range-select/get-time-range-options";
import { type Permission } from "@braintrust/core/typespecs";
import { LogsEmptyState } from "./logs-empty-state";

export default function LogsViewer({
  logType,
  logId,
  setSavingState,
  defaultPanelLayout,
  permissions,
}: {
  logType: "project_logs";
  logId: string | null;
  extraLeftControls?: React.ReactNode;
  setSavingState: Dispatch<SetStateAction<SavingState>>;
  defaultPanelLayout: LogsPanelLayout;
  permissions: Permission[];
}) {
  useAnalytics({
    page: {
      category: "logs",
      props: {
        project_id: logId,
      },
    },
  });

  const {
    orgName,
    projectId,
    projectName,
    config: projectConfig,
  } = useContext(ProjectContext);

  const [logBucket, setLogBucket] = useEntityStorage({
    entityType: "project",
    entityIdentifier: projectId ?? "",
    key: "logBucket",
  });

  const objectType = "project_logs";
  const clauseCheckerProps = useClauseChecker(objectType, true);
  const viewType: ViewType | null = "logs";
  const viewParams: ViewParams | undefined = useMemo(
    () =>
      projectId && viewType
        ? {
            objectType: "project",
            objectId: projectId,
            viewType,
          }
        : undefined,
    [projectId],
  );

  const pageIdentifier = objectType + "-" + logId;

  const viewProps = useViewStates({
    viewParams,
    clauseChecker: clauseCheckerProps.clauseChecker,
    pageIdentifier,
  });

  const customColumnState = useCustomColumns({
    scope: projectId
      ? {
          object_type: "project",
          object_id: projectId,
          subtype: "project_log",
          variant: "project_log",
        }
      : undefined,
    projectedColumns: BUILT_IN_CUSTOM_COLUMNS[objectType],
  });

  const {
    logSummaryQuery,
    logSummarySignals,
    logSummarySchema,
    logSummaryBtqlSchema,
    logSummaryLoading,
    tooExpensive,
  } = useLogScoreSummary(
    logBucket,
    projectId ?? null,
    viewProps,
    customColumnState.customColumnDefinitions,
  );

  const { scoreFields } = deriveScoreFields({
    summarySchema: logSummarySchema,
    btqlSchema: logSummaryBtqlSchema,
    scoreConfig: projectConfig.scores,
  });

  const [permissionsOpen, setPermissionsOpen] = useState(false);
  const [selectedBucket, setSelectedBucket] = useState<
    Clause<"filter"> | undefined
  >(undefined);

  const minSidePanelWidth = usePanelSize(640);
  const minMainPanelWidth = usePanelSize(400);

  const mainPanelScrollContainer = useRef<HTMLDivElement>(null);

  const traceViewParamArgs = useMemo(
    (): Partial<TraceViewParams> => ({
      title: "log",
    }),
    [],
  );

  const paginatedObjectViewerDataComponents =
    usePaginatedObjectViewerDataComponents({
      objectType: logType,
      objectId: logId,
      objectName: projectName,
      pageSize: 10,
      setSavingState,
      selectedBucket,
      traceViewParamArgs,
      viewProps: {
        ...viewProps,
        // disable time range in non-summary logs viewer
        timeRangeFilter: ALL_TIME_RANGE,
      },
      viewParams,
      disableGrouping: true,
      useClauseCheckerProps: clauseCheckerProps,
    });

  const extraLeftControls = useMemo(
    () => (
      <TableRowHeightToggle
        tableRowHeight={viewProps.rowHeight ?? "compact"}
        onSetRowHeight={viewProps.setRowHeight}
      />
    ),
    [viewProps.rowHeight, viewProps.setRowHeight],
  );

  const { traceViewParams, projectedPaths, filters } =
    paginatedObjectViewerDataComponents;
  const [rowIds, setRowIds] = useState<RowId[]>([]);

  const vizQueryRef = useRef<{
    data: ArrowTable<TypeMap> | null;
  }>(null);

  const exportedColumns = useMemo(
    () =>
      projectedPaths
        .concat(
          (paginatedObjectViewerDataComponents.customColumns.columns || []).map(
            ({ name }) => name,
          ),
        )
        .filter((path) => path !== "span_type_info"),
    [projectedPaths, paginatedObjectViewerDataComponents.customColumns.columns],
  );

  const getRowsForExport = useGetRowsForExport({
    objectType,
    objectId: projectId,
    filters,
    layout: viewProps.layout,
    exportedColumns,
    // Only export the currently loaded data, since the logs page can be
    // infinitely long.
    shouldRefetchObject: false,
    vizQueryRef,
  });

  const isReadOnly =
    !permissions.includes("update") && !permissions.includes("delete");

  const vizQueryPropsOverride = useMemo(
    () => ({
      isHumanReviewModeEnabled: true,
      scrollContainerRef: mainPanelScrollContainer,
      multilineRow: viewProps.rowHeight === "tall" ? { numRows: 5 } : undefined,
      showRowNumber: true,
      vizQueryRef,
      columnReorderer,
      isSortable: false,
      filterSuggestions: [
        {
          label: "Duration > 3s",
          btql: "metrics.duration > 3000",
        },
        {
          label: "Cost > $1",
          btql: "metrics.estimated_cost > 1",
        },
      ],
    }),
    [viewProps.rowHeight],
  );

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#how-can-i-use-persistent-layouts-with-ssr
  const onPanelLayout = useCallback(
    (sizes: number[]) => {
      const layoutCookie: LogsPanelLayout = {
        ...defaultPanelLayout,
        main: sizes[0],
        trace: sizes[1],
      };
      document.cookie = `react-resizable-panels:logs-layout=${JSON.stringify(
        layoutCookie,
      )}; path=/`;
    },
    [defaultPanelLayout],
  );

  const paginatedObjectViewerObjectVizComponents =
    usePaginatedObjectViewerVizComponents(
      {
        ...paginatedObjectViewerDataComponents,
        traceViewParams,
      },
      {
        rowIds,
        setRowIds,
        aiSearchType: "logs",
        includeExperimentSelectionSection: true,
        extraLeftControls,
        vizQueryPropsOverride,
      },
    );

  const { isTraceFullscreen } = useTraceFullscreen();

  const columnVisibility = useMemo(() => {
    return {
      ...INITIALLY_VISIBLE_COLUMNS,
      ...viewProps.columnVisibility,
    };
  }, [viewProps.columnVisibility]);

  if (paginatedObjectViewerObjectVizComponents.status === "loading_initial") {
    return (
      <MainContentWrapper>
        <Skeleton className="mb-2 h-[200px]" />
        <TableSkeleton />
      </MainContentWrapper>
    );
  } else if (
    paginatedObjectViewerObjectVizComponents.status === "loaded_empty"
  ) {
    return (
      <>
        <LogsHeader
          projectId={projectId ?? ""}
          projectName={projectName}
          orgName={orgName}
          getRowsForExport={getRowsForExport}
          hideActions
          columnVisibility={columnVisibility}
          isReadOnly={isReadOnly}
        />
        <BodyWrapper outerClassName={HEIGHT_WITH_DOUBLE_TOP_OFFSET}>
          <MainContentWrapper>
            <LogsEmptyState orgName={orgName} projectName={projectName} />
          </MainContentWrapper>
        </BodyWrapper>
      </>
    );
  }

  const { tableViewComponents, tracePanelComponents } =
    paginatedObjectViewerObjectVizComponents;

  if (isTraceFullscreen && tracePanelComponents) {
    return (
      <>
        <LogsHeader
          projectId={projectId ?? ""}
          projectName={projectName}
          orgName={orgName}
          getRowsForExport={getRowsForExport}
          columnVisibility={columnVisibility}
          isReadOnly={isReadOnly}
        />
        <BodyWrapper
          outerClassName={HEIGHT_WITH_DOUBLE_TOP_OFFSET}
          innerClassName="flex overflow-hidden flex-1"
        >
          {tracePanelComponents}
        </BodyWrapper>
      </>
    );
  }

  return (
    <>
      <LogsHeader
        projectId={projectId ?? ""}
        projectName={projectName}
        orgName={orgName}
        getRowsForExport={getRowsForExport}
        columnVisibility={columnVisibility}
        isReadOnly={isReadOnly}
      />
      <BodyWrapper outerClassName={HEIGHT_WITH_DOUBLE_TOP_OFFSET}>
        <ResizablePanelGroup
          direction="horizontal"
          autoSaveId="logsPanelLayout"
          className="flex !flex-row-reverse overflow-hidden"
          onLayout={onPanelLayout}
        >
          {tracePanelComponents && (
            <>
              <ResizablePanel
                order={1}
                defaultSize={Math.max(
                  defaultPanelLayout.trace,
                  minSidePanelWidth,
                )}
                minSize={minSidePanelWidth}
                id="trace"
                className="flex flex-col overflow-hidden"
              >
                {tracePanelComponents}
              </ResizablePanel>
              <ResizableHandle />
            </>
          )}
          <ResizablePanel
            order={0}
            minSize={minMainPanelWidth}
            defaultSize={defaultPanelLayout.main}
            id="main"
            className="relative"
          >
            <div
              className={cn(
                "flex flex-col overflow-auto px-3",
                HEIGHT_WITH_DOUBLE_TOP_OFFSET,
              )}
              ref={mainPanelScrollContainer}
            >
              <ObjectPermissionsDialog
                objectType="project_log"
                objectId={projectId ?? ""}
                objectName={projectName}
                orgName={orgName}
                projectName={projectName}
                open={permissionsOpen}
                onOpenChange={setPermissionsOpen}
              />

              <div className="sticky left-0 w-full pb-6 pt-4">
                <div className="mb-2 text-sm text-primary-600">
                  In the past 7 days
                </div>
                <LogsSparklines context="logs-page" />
              </div>

              {tooExpensive ? (
                <TableEmptyState
                  className="sticky left-0 mb-2 w-full pt-2"
                  label="Scores chart unavailable when self-hosting with Postgres-only"
                />
              ) : (
                <LogsProgressInsight
                  className="sticky left-0 mb-2 w-full pt-2"
                  experimentsQuery={logSummaryQuery}
                  experimentsSignals={logSummarySignals}
                  onExperimentClick={({ id }) => {
                    // Add an hour to the date
                    const bucket = new Date(id);
                    let nextBucket: Date;
                    switch (logBucket) {
                      case "hour":
                        nextBucket = new Date(
                          bucket.getTime() + 60 * 60 * 1000,
                        );
                        break;
                      case "day":
                        nextBucket = new Date(
                          bucket.getTime() + 24 * 60 * 60 * 1000,
                        );
                        break;
                      case "week":
                        nextBucket = new Date(
                          bucket.getTime() + 7 * 24 * 60 * 60 * 1000,
                        );
                        break;
                      case "month":
                        nextBucket = new Date(bucket);
                        nextBucket.setMonth(bucket.getMonth() + 1);
                        break;
                      default:
                        nextBucket = new Date();
                        console.assert(false, "Unknown log bucket");
                    }
                    const filter = `created >= ${singleQuote(
                      bucket.toISOString(),
                    )} AND created <= ${singleQuote(nextBucket.toISOString())}`;
                    setSelectedBucket({
                      type: "filter",
                      text: filter,
                      bubble: new Bubble({
                        type: "filter",
                        label: `${formatFullDateTime(bucket)}-${formatFullDateTime(
                          nextBucket,
                        )}`,
                        isReadonly: true,
                        clear: () => {
                          setSelectedBucket(undefined);
                        },
                      }),
                      tree: [
                        {
                          type: "sql_filter",
                          expr: filter,
                        },
                      ],
                    });
                  }}
                  title="Scores over time"
                  bucket={logBucket}
                  setBucket={setLogBucket}
                  insightIdentifier={"project-log-score-progress" + projectId}
                  scoreNames={scoreFields}
                  scoresLoading={logSummaryLoading}
                />
              )}
              {tableViewComponents}
              <div className="grow" />
              <Footer
                className="sticky left-0 w-full pb-4 sm:pb-4 lg:pb-4"
                inApp
                orgName={orgName}
              />
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </BodyWrapper>
    </>
  );
}

export type LogsPanelLayout = {
  trace: number;
  main: number;
};
