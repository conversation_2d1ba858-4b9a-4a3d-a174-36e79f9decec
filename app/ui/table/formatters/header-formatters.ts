import type { FormatterMap } from "#/ui/field-to-column";
import { BT_ASSIGNMENTS } from "#/utils/assign";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@braintrust/local/api-schema";
import {
  ArrowUpRight,
  ArrowDownRight,
  Equal,
  PercentIcon,
  Tag,
  CurlyBraces,
  Hash,
  User2Icon,
  TextIcon,
  Clock,
  DollarSign,
  GitBranch,
  Calendar,
  Fingerprint,
  CircleAlert,
  Beaker,
  Database,
  ScrollText,
  Blocks,
  Timer,
  FunctionSquare,
  MessageSquareText,
  Cuboid,
  Shapes,
  UserRound,
} from "lucide-react";

// These are still hardcoded for now. But theoretically, we could let users pick an icon and save it as
// part of the metric's configuration.
export const HeaderIcons: Record<string, FormatterMap["string"]["headerIcon"]> =
  {
    description: TextIcon,
    prompt_data: Cuboid,
    function_data: FunctionSquare,
    num_examples: Hash,
    num_errors: CircleAlert,
    error_rate: <PERSON><PERSON><PERSON><PERSON>,
    creator: User<PERSON><PERSON><PERSON>,
    source: <PERSON><PERSON><PERSON>ran<PERSON>,
    scores: PercentIcon,
    tags: Tag,
    [BT_ASSIGNMENTS]: UserRound,
    name: TextIcon,
    span_type_info: TextIcon,
    id: Fingerprint,
    input: ArrowDownRight,
    output: ArrowUpRight,
    expected: Equal,
    metadata: CurlyBraces,
    created: Calendar,
    last_updated: Calendar,
    error: CircleAlert,
    experiments: Beaker,
    playgrounds: Shapes,
    datasets: Database,
    dataset: Database,
    Logs: ScrollText,
    duration: Clock,
    llm_duration: Clock,
    time_to_first_token: Clock,
    total_tokens: Blocks,
    prompt_tokens: Blocks,
    prompt_cached_tokens: Blocks,
    prompt_cache_creation_tokens: Blocks,
    completion_tokens: Blocks,
    completion_reasoning_tokens: Blocks,
    completion_accepted_prediction_tokens: Blocks,
    completion_rejected_prediction_tokens: Blocks,
    completion_audio_tokens: Blocks,
    estimated_cost: DollarSign,
    "Logs (7d)": ScrollText,
    "Tokens (7d)": Blocks,
    "TTF token (avg 7d)": Timer,
    "Duration (avg 7d)": Timer,
    "Duration (p95 7d)": Timer,
    "Errors (7d)": CircleAlert,
    comments: MessageSquareText,
    start: Calendar,
    end: Calendar,
    errors: CircleAlert,
    tool_errors: CircleAlert,
    llm_errors: CircleAlert,
  };

export const MetricHeaderIcons: Record<
  string,
  FormatterMap["string"]["headerIcon"]
> = {};

export const HeaderFormatters: FormatterMap = Object.fromEntries(
  Array.from(
    new Set(Object.keys(HeaderAliases).concat(Object.keys(HeaderIcons))),
  ).map((key) => [
    key,
    {
      headerLabel: HeaderAliases[key],
      headerIcon: HeaderIcons[key],
    },
  ]),
);

export function makeFormatterMap<TsTable = {}, TsValue = "">(
  ...formatters: FormatterMap<TsTable, TsValue>[]
): FormatterMap<TsTable, TsValue> {
  return mergeFormatterMaps(
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    ...[HeaderFormatters as unknown as FormatterMap<TsTable, TsValue>].concat(
      formatters,
    ),
  );
}

function mergeFormatterMaps<TsTable = {}, TsValue = "">(
  ...formatters: FormatterMap<TsTable, TsValue>[]
): FormatterMap<TsTable, TsValue> {
  return formatters.reduce((acc, formatter) => {
    for (const key in formatter) {
      acc[key] = { ...acc[key], ...formatter[key] };
    }
    return acc;
  }, {});
}
