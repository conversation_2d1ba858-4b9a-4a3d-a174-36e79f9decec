import React from "react";
import { type FormatterProps } from "#/ui/arrow-table";
import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import type { AggregationType } from "#/utils/queries/aggregations";
import { cn } from "#/utils/classnames";
import {
  getColumnType,
  type useSummaryBreakdown,
} from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { type RegressionFilter } from "#/app/app/[org]/p/[project]/experiments/[experiment]/regressions-query";
import { HeaderSummary } from "#/ui/table/header-summary";
import { SUMMARY_CACHED_METRIC_NAME } from "@braintrust/local/query";
import { GROUP_BY_INPUT_VALUE } from "#/ui/table/grouping/controls";
import { getGroupValue } from "./grouping/grouping-values";
import { BT_IS_GROUP } from "#/ui/table/grouping/queries";
import { type CustomColumnDefinition } from "#/utils/custom-columns/use-custom-columns";
import { type MetricDefinition } from "@braintrust/local/api-schema";

export type SummaryProps = {
  groupKey: string;
  aggregationExperimentId: string;
  experimentSummaryData: ReturnType<typeof useSummaryBreakdown>["summary"];
  addRegressionFilter: (f: RegressionFilter) => void;
  numGroupRows?: number;
};

export type GroupAggregationProps = {
  groupAggregationTypes: Record<string, AggregationType>;
  setGroupAggregationType: (
    f: "scores" | "metrics",
    v: AggregationType,
    experimentId?: string,
  ) => void;
  isGrouping?: boolean;
  summaryData?: SummaryProps;
  summaryEnabled?: boolean;
  scoreNames?: string[];
  customColumns?: CustomColumnDefinition[];
  metricDefinition?: MetricDefinition;
};

type GroupAggregationFormatterFactoryProps = {
  supportedAggregationTypes?: AggregationType[];
  render: <T extends { [BT_IS_GROUP]?: boolean }, V>(
    props: FormatterProps<T, V>,
  ) => React.ReactNode;
  renderForGroup: <T extends { [BT_IS_GROUP]?: boolean }, V>(
    props: FormatterProps<T, V>,
  ) => React.ReactNode;
} & GroupAggregationProps;

export function GroupAggregationFormatterFactory({
  supportedAggregationTypes = ["sum", "avg", "min", "max"],
  render,
  renderForGroup,
  groupAggregationTypes,
  setGroupAggregationType,
  summaryData,
  scoreNames,
  customColumns,
  metricDefinition,
}: GroupAggregationFormatterFactoryProps) {
  return function GroupAggregationFormatter<
    TsData extends {
      [BT_IS_GROUP]?: boolean;
    },
    TsValue,
  >(props: FormatterProps<TsData, TsValue>) {
    const { cell, renderForTooltip, groupBy } = props;

    if (!cell.row.original[BT_IS_GROUP]) {
      return render<TsData, TsValue>(props);
    }

    if (summaryData) {
      const {
        groupKey,
        experimentSummaryData,
        aggregationExperimentId,
        addRegressionFilter,
      } = summaryData;
      if (experimentSummaryData.experiments.length === 0) {
        return null;
      }

      const selectedExperiment =
        experimentSummaryData.experiments.find(
          ({ experiment }) => experiment.id === aggregationExperimentId,
        ) ?? experimentSummaryData.experiments[0];

      const meta = cell.column.columnDef.meta;
      const columnName = meta?.name ?? "";
      const isScore =
        meta?.path?.[0] === "scores" || meta?.name === "error_rate";
      const columnType = isScore ? ("scores" as const) : ("metrics" as const);
      const HeaderComponent =
        cell.column.columnDef.meta?.header ?? React.Fragment;

      const groupValue = getGroupValue(cell.row.original, groupKey);
      const summaryGroupValue =
        groupBy === GROUP_BY_INPUT_VALUE
          ? getGroupValue(cell.row.original, "comparison_key")
          : groupValue;

      const showAllSummaryRows =
        summaryData.numGroupRows && summaryData.numGroupRows > 1;

      return (
        <div className="pointer-events-auto z-20 flex flex-col px-3">
          {(showAllSummaryRows
            ? experimentSummaryData.experiments
            : [selectedExperiment]
          ).map((s, idx) => {
            const customColumn = customColumns?.find(
              ({ name }) => name === groupBy,
            );
            const cachedKey =
              s.experiment.type === "base" ? "sum" : "compareSum";
            return (
              <HeaderSummary
                key={s.experiment.id}
                title={<HeaderComponent />}
                isGrouping
                showAggregationType={idx === 0}
                metricDefinition={metricDefinition}
                generateRegressionFilter={(comparisonType, id) => ({
                  comparisonType,
                  field: {
                    type: getColumnType(meta?.path ?? []),
                    value: columnName,
                  },
                  group:
                    groupBy === GROUP_BY_INPUT_VALUE
                      ? { type: "input", value: summaryGroupValue }
                      : customColumn
                        ? {
                            type: "custom",
                            expr: customColumn.expr,
                            name: customColumn.name,
                            value: groupValue,
                          }
                        : {
                            type: "metadata",
                            path: JSON.parse(groupBy ?? "[]"),
                            value: groupValue,
                          },
                  experimentId: id ?? "any",
                })}
                addRegressionFilter={addRegressionFilter}
                fieldName={columnName}
                aggregations={{
                  type: showAllSummaryRows
                    ? "experiments"
                    : "selectedExperiment",
                  aggregationType: groupAggregationTypes[columnType],
                  setAggregationType: (v, id) => {
                    setGroupAggregationType(columnType, v, id);
                  },
                }}
                summary={{
                  ...s.groupedSummary?.[summaryGroupValue]?.[columnName],
                  experiment: s.experiment,
                }}
                summaryData={experimentSummaryData.experiments}
                cachedCount={
                  s.groupedSummary?.[summaryGroupValue]?.[
                    SUMMARY_CACHED_METRIC_NAME
                  ]?.[cachedKey]
                }
                columnType={columnType}
                columnMeta={meta}
              />
            );
          })}
        </div>
      );
    }

    const meta = cell.column.columnDef.meta;
    const isScore =
      meta?.path?.[0] === "scores" ||
      meta?.name === "error_rate" ||
      scoreNames?.includes(meta?.path?.join(".") ?? "");
    const columnType = isScore ? "scores" : "metrics";
    const groupAggregationType = groupAggregationTypes[columnType];
    const selectedAggregationType =
      groupAggregationType ?? supportedAggregationTypes[0];

    const aggPicker = (
      <AggregationTypePicker
        buttonClassName={cn({
          hidden: renderForTooltip,
        })}
        selectedValue={selectedAggregationType}
      >
        {supportedAggregationTypes.map((v) => (
          <DropdownMenuCheckboxItem
            key={v}
            checked={selectedAggregationType === v}
            className="capitalize"
            onClick={(e) => {
              e.stopPropagation();
              setGroupAggregationType(columnType, v);
            }}
          >
            {v}
          </DropdownMenuCheckboxItem>
        ))}
      </AggregationTypePicker>
    );
    return (
      <span className={cn("flex items-center gap-1 px-3 w-full")}>
        <span className="grow truncate font-medium">
          {renderForGroup({ ...props, hideNulls: false })}
        </span>
        {aggPicker}
      </span>
    );
  };
}

type AggregationTypePickerProps = {
  buttonClassName?: string;
  selectedValue: React.ReactNode;
  children: React.ReactNode;
  disabled?: boolean;
};

const AggregationTypePicker = ({
  buttonClassName,
  children,
  selectedValue,
}: AggregationTypePickerProps) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          className={cn(
            "flex h-5 flex-none items-center gap-0.5 px-0.5 text-[10px] uppercase tracking-wide text-primary-400 disabled:opacity-100 disabled:bg-transparent",
            buttonClassName,
          )}
          transparent
          size="inline"
          onClick={(e) => e.stopPropagation()}
        >
          {selectedValue}
          <ChevronDown className="size-[10px]" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">{children}</DropdownMenuContent>
    </DropdownMenu>
  );
};
