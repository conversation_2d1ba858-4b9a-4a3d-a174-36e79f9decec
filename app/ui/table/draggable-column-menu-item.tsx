import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { type ReactNode } from "react";
import { Button } from "#/ui/button";
import { GripVerticalIcon } from "lucide-react";

type Props = {
  disabled: boolean;
  id: string;
  renderer: (handle?: ReactNode) => ReactNode;
};

export default function DraggableColumnMenuItem({
  disabled,
  id,
  renderer,
}: Props) {
  if (disabled) {
    return <div className="group flex w-full">{renderer()}</div>;
  }

  return <DraggableColumnMenuItemInner id={id} renderer={renderer} />;
}

function DraggableColumnMenuItemInner({
  renderer,
  id,
}: Omit<Props, "disabled">) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id,
  });

  const style = {
    transform: CSS.Transform.toString({
      y: transform?.y ?? 0,
      x: 0,
      scaleX: 1,
      scaleY: 1,
    }),
    zIndex: isDragging ? 1 : 0,
    transition,
  };

  const handle = (
    <Button
      variant="ghost"
      size="inline"
      Icon={GripVerticalIcon}
      className="ml-auto cursor-grab opacity-0 text-primary-400 group-hover:opacity-100"
      {...listeners}
    />
  );

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className="group flex w-full flex-row"
      key={id}
    >
      {renderer(handle)}
    </div>
  );
}
