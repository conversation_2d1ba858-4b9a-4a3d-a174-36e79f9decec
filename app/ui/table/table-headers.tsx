"use-client";

import React, { memo } from "react";
import { DeadCell } from "#/ui/dead-cell";
import { DraggableColumnHeader } from "#/ui/table/draggable-column-header";
import {
  SortableContext,
  horizontalListSortingStrategy,
} from "@dnd-kit/sortable";
import { cn } from "#/utils/classnames";
import { type Column, type Header } from "@tanstack/react-table";
import { isEmpty } from "#/utils/object";
import { type RegressionFilter } from "#/app/app/[org]/p/[project]/experiments/[experiment]/regressions-query";
import { type ScoreSummary } from "@braintrust/local/query";
import { type ScoreSummaryExperiment } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { Loading } from "#/ui/loading";
import { Button } from "#/ui/button";
import { Plus } from "lucide-react";
import { type SortComparison } from "#/utils/search/search";
import { BasicTooltip } from "#/ui/tooltip";
import { useResizeObserver } from "#/ui/charts/padding/use-resize-observer";
import { type MetricDefinition } from "@braintrust/local/api-schema";

interface TableHeadersProps<TsData> {
  headers: Header<TsData, unknown>[];
  allColumnsHidden?: boolean;
  tableHeaderRefs: React.RefObject<(HTMLDivElement | null)[] | null>;
  toolbarContainerRef: React.RefObject<HTMLDivElement | null>;
  addRegressionFilter?: (filter: RegressionFilter) => void;
  summaryData?:
    | { scores: ScoreSummary; experiment: ScoreSummaryExperiment }[]
    | null;
  filtersEnabled: boolean;
  filterableColIds: Set<string>;
  setFilterState: (state: {
    isOpen: boolean;
    defaultColumnId: string | undefined;
  }) => void;
  onEdit: (colId?: string) => void;
  onDelete: (col: { id: string; name: string }) => void;
  onAddColumn: VoidFunction;
  showAddCustomColumn: boolean;
  customColumnsLoading: boolean;
  isGridLayout: boolean | null;
  setColumnSort: (
    col: Column<TsData, unknown>,
    desc: boolean,
    comparison?: SortComparison,
  ) => void;
  groupBy?: string;
  customColumnsEnabled: boolean;
  isSortable: boolean;
  isReadOnly?: boolean;
  summarySlots?: Record<string, React.ReactNode>;
  metricDefinitions: MetricDefinition[] | undefined;
}

const TableHeadersComponent = <TsData,>({
  headers,
  allColumnsHidden,
  tableHeaderRefs,
  toolbarContainerRef,
  filtersEnabled,
  filterableColIds,
  setFilterState,
  summaryData,
  addRegressionFilter,
  onEdit,
  onDelete,
  onAddColumn,
  showAddCustomColumn,
  customColumnsLoading,
  isGridLayout,
  setColumnSort,
  groupBy,
  customColumnsEnabled,
  isSortable,
  isReadOnly,
  summarySlots,
  metricDefinitions,
}: TableHeadersProps<TsData>) => {
  const { height: toolbarHeight } = useResizeObserver(
    toolbarContainerRef,
    true,
  );
  return (
    <div
      className={cn(
        "sticky z-10 min-w-full from-white to-white/50 bg-gradient-to-b dark:from-black dark:to-black/50 backdrop-blur border-b mb-1 border-primary-200/50",
        {
          hidden: allColumnsHidden,
        },
      )}
      // Tailwind can't dynamically create css classes, so we need to use inline style
      style={{
        top: toolbarHeight,
      }}
    >
      <SortableContext
        items={
          isGridLayout
            ? []
            : headers
                .filter(
                  (header) =>
                    isEmpty(header.column.columnDef.meta?.pinnedColumnIndex) &&
                    !header.column.columnDef.meta?.internalType,
                )
                .map((header) => header.id)
        }
        strategy={horizontalListSortingStrategy}
      >
        <div className="flex min-w-full items-stretch">
          {headers.map((header, i) => (
            <DraggableColumnHeader
              key={header.id}
              header={header}
              headerRefFn={(e) => {
                if (tableHeaderRefs.current) {
                  // eslint-disable-next-line react-compiler/react-compiler
                  tableHeaderRefs.current[i] = e ?? null;
                }
              }}
              summaryData={summaryData}
              addRegressionFilter={addRegressionFilter}
              onFilter={
                filtersEnabled && filterableColIds.has(header.column.id)
                  ? () => {
                      setFilterState({
                        defaultColumnId: header.id,
                        isOpen: true,
                      });
                    }
                  : undefined
              }
              onEdit={onEdit}
              onDelete={onDelete}
              isPinned={header.column.columnDef.meta?.pinnedColumnIndex != null}
              setColumnSort={setColumnSort}
              groupBy={groupBy}
              layout={isGridLayout ? "grid" : "list"}
              isSortable={isSortable}
              isReadOnly={isReadOnly}
              summarySlot={summarySlots?.[header.id]}
              metricDefinitions={metricDefinitions}
            />
          ))}
          {showAddCustomColumn && (
            <div className="py-2">
              <div className="flex h-full items-start border-l px-2">
                {customColumnsLoading ? (
                  <Loading />
                ) : (
                  <BasicTooltip
                    tooltipContent={
                      !customColumnsEnabled
                        ? "Update your stack to enable custom columns on this table"
                        : undefined
                    }
                  >
                    <Button
                      title={
                        customColumnsEnabled ? "Add custom column" : undefined
                      }
                      Icon={Plus}
                      variant="ghost"
                      className={cn("h-6 p-0", {
                        "opacity-60 bg-primary-50 cursor-auto":
                          !customColumnsEnabled,
                      })}
                      size="xs"
                      onClick={
                        customColumnsEnabled ? () => onAddColumn() : undefined
                      }
                    />
                  </BasicTooltip>
                )}
              </div>
            </div>
          )}
          <DeadCell
            className="flex-1 bg-background"
            isHeader
            hideBorder={showAddCustomColumn}
          />
        </div>
      </SortableContext>
    </div>
  );
};

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
export const TableHeaders = memo(
  TableHeadersComponent,
) as typeof TableHeadersComponent;
