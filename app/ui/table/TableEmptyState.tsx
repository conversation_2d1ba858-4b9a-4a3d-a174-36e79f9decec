import { type LucideIcon } from "lucide-react";
import { type PropsWithChildren, type ReactNode } from "react";
import { cn } from "#/utils/classnames";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "#/ui/tabs";
import { <PERSON><PERSON> } from "#/ui/button";
import CodeToCopy from "#/ui/code-to-copy";
import { PythonLogo, TypescriptLogo } from "#/app/app/[org]/onboarding-logos";

export const TableEmptyState = ({
  className,
  children,
  label,
  Icon,
  labelClassName,
}: PropsWithChildren<{
  className?: string;
  label: ReactNode;
  labelClassName?: string;
  Icon?: LucideIcon;
}>) => (
  <div
    className={cn(
      "flex flex-col gap-4 items-center rounded-md bg-primary-50 p-8 border border-primary-100",
      className,
    )}
  >
    {Icon && <Icon className="text-primary-400" />}
    <div
      className={cn(
        "max-w-lg text-pretty text-center text-base text-primary-500",
        labelClassName,
      )}
    >
      {label}
    </div>
    {children}
  </div>
);

export const EmptyStateCodeSnippets = ({
  ts,
  py,
  label,
}: {
  ts: string;
  py: string;
  label?: ReactNode;
}) => (
  <Tabs
    defaultValue="ts"
    className="flex w-full max-w-screen-sm flex-col items-center"
  >
    <div
      className={cn(
        label ? "w-full items-center flex gap-3 justify-between" : "",
      )}
    >
      {label}
      <TabsList className="h-auto p-1">
        <TabsTrigger asChild value="ts">
          <Button
            size="xs"
            className="rounded border-0 text-xs text-primary-500"
          >
            <TypescriptLogo className="size-3" /> TypeScript
          </Button>
        </TabsTrigger>
        <TabsTrigger asChild value="py">
          <Button
            size="xs"
            className="rounded border-0 text-xs text-primary-500"
          >
            <PythonLogo className="size-3" /> Python
          </Button>
        </TabsTrigger>
      </TabsList>
    </div>
    <TabsContent value="ts" className="w-full">
      <CodeToCopy
        className="w-full"
        highlighterClassName="bg-background"
        language="typescript"
        data={ts}
      />
    </TabsContent>
    <TabsContent value="py" className="w-full">
      <CodeToCopy
        className="w-full"
        highlighterClassName="bg-background"
        language="python"
        data={py}
      />
    </TabsContent>
  </Tabs>
);

// Some orgs have custom init wrappers around the SDK and the code
// snippets don't work for them. Adding the orgs here will hide
// the code snippets for them.
export const ORGS_WITH_DISABLED_EMPTY_STATE_CODE_SNIPPETS = ["Netflix"];
