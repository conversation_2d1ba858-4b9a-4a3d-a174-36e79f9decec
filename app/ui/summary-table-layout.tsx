import {
  getColumnType,
  type ScoreSummaryExperiment,
} from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import {
  SUMMARY_CACHED_METRIC_NAME,
  type ScoreSummary,
} from "@braintrust/local/query";
import { HeaderSummary } from "./table/header-summary";
import { type TypeMap } from "apache-arrow";
import { flexRender, type Header } from "@tanstack/react-table";
import { type RegressionFilter } from "#/app/app/[org]/p/[project]/experiments/[experiment]/regressions-query";
import { useOrg } from "#/utils/user";
import { isEmpty } from "#/utils/object";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { type MetricDefinition } from "@braintrust/local/api-schema";

export const SummaryTableLayout = <_TData extends TypeMap, TsData>({
  headers,
  summaryData,
  addRegressionFilter,
  metricDefinitions,
}: {
  headers: Header<TsData, unknown>[];
  addRegressionFilter?: (filter: RegressionFilter) => void;
  summaryData?:
    | { scores: ScoreSummary; experiment: ScoreSummaryExperiment }[]
    | null;
  metricDefinitions?: MetricDefinition[];
}) => {
  const org = useOrg();
  const [aggregationTypes, setAggregationType] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: org.id ?? "",
    key: "aggregationTypes",
  });

  if (!summaryData) {
    return null;
  }

  return (
    <div className="grid grid-cols-2 gap-4 py-4 xl:grid-cols-3">
      {headers.map((header) => {
        const meta = header.column.columnDef.meta;

        const isMetric = meta?.path?.[0] === "metrics";
        const isScore = meta?.path?.[0] === "scores";
        const summaryEnabled =
          !isEmpty(summaryData) &&
          (isMetric || isScore) &&
          meta?.name !== "start" &&
          meta?.name !== "end";
        const aggregationTypeKey = isMetric ? "metrics" : "scores";
        const aggregationType = aggregationTypes[aggregationTypeKey];

        if (!meta || !summaryEnabled) {
          return null;
        }
        const content = flexRender(
          header.column.columnDef.header,
          header.getContext(),
        );

        return (
          <div
            key={header.id}
            className="rounded-md border px-4 py-3 bg-primary-50 border-primary-100"
          >
            <div className="mb-2 flex items-center gap-2 text-xs text-primary-600">
              {content}
            </div>
            {summaryData.map((s, idx) => {
              const columnType = getColumnType(meta.path);
              const columnName = meta.name;

              const cachedKey =
                s.experiment.type === "base" ? "sum" : "compareSum";
              return (
                <HeaderSummary
                  key={s.experiment.id}
                  showAggregationType={idx === 0}
                  isBig
                  title={content}
                  generateRegressionFilter={(comparisonType, id) => ({
                    comparisonType,
                    field: {
                      type: columnType,
                      value: columnName,
                    },
                    experimentId: id ?? "any",
                  })}
                  addRegressionFilter={addRegressionFilter}
                  fieldName={columnName}
                  aggregations={{
                    type: "experiments",
                    aggregationType,
                    setAggregationType: (v) => {
                      setAggregationType({
                        ...aggregationTypes,
                        [aggregationTypeKey]: v,
                      });
                    },
                  }}
                  summary={{
                    ...s.scores?.[columnName],
                    experiment: s.experiment,
                  }}
                  errorsSummary={s.scores?.error}
                  summaryData={summaryData}
                  cachedCount={
                    s.scores?.[SUMMARY_CACHED_METRIC_NAME]?.[cachedKey]
                  }
                  columnType={aggregationTypeKey}
                  columnMeta={meta}
                  metricDefinition={
                    isMetric
                      ? metricDefinitions?.find(
                          (d) => d.field_name === columnName,
                        )
                      : undefined
                  }
                />
              );
            })}
          </div>
        );
      })}
    </div>
  );
};
