"use client";

import { useState } from "react";
import { cn } from "#/utils/classnames";
import { Button } from "#/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { ComboboxCommand } from "#/ui/combobox/combobox-command";
import { ListFilter, XIcon } from "lucide-react";

interface CookbookTagFilterProps {
  availableTags: string[];
  availableLanguages?: string[];
  selectedTags: string[];
  onSelectedTagsChange: (tags: string[]) => void;
  className?: string;
}

export function CookbookTagFilter({
  availableTags,
  availableLanguages = [],
  selectedTags,
  onSelectedTagsChange,
  className,
}: CookbookTagFilterProps) {
  const [open, setOpen] = useState(false);

  const tagOptions = availableTags.map((tag) => ({
    value: tag,
    label: tag,
    type: "tag" as const,
  }));

  const languageOptions = availableLanguages.map((language) => ({
    value: `language:${language}`,
    label: `${language === "typescript" ? "TypeScript" : language === "python" ? "Python" : language}`,
    type: "language" as const,
  }));

  const allOptions = [...languageOptions, ...tagOptions];

  const handleTagToggle = (value: string | undefined) => {
    if (!value) return;

    if (selectedTags.includes(value)) {
      // Remove tag
      onSelectedTagsChange(selectedTags.filter((tag) => tag !== value));
    } else {
      // Add tag
      onSelectedTagsChange([...selectedTags, value]);
    }
  };

  const clearAllTags = () => {
    onSelectedTagsChange([]);
  };

  return (
    <div className={cn("flex flex-col gap-3", className)}>
      <div className="flex items-center gap-2">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="border"
              size="xs"
              className="border-dashed"
              aria-expanded={open}
            >
              <ListFilter className="size-3" />
              Filter
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-64 p-0" align="start">
            <ComboboxCommand
              options={allOptions}
              selectedValues={selectedTags}
              onChange={handleTagToggle}
              searchPlaceholder="Search tags and languages..."
              noResultsLabel="No tags or languages found"
              className="max-h-80"
            />
          </PopoverContent>
        </Popover>

        {selectedTags.length > 0 && (
          <Button
            variant="ghost"
            size="xs"
            onClick={clearAllTags}
            className="text-primary-500"
          >
            Clear all
          </Button>
        )}
      </div>

      {/* Selected tags display */}
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedTags.map((tag) => {
            const isLanguage = tag.startsWith("language:");
            const displayValue = isLanguage
              ? (() => {
                  const lang = tag.replace("language:", "");
                  return lang === "typescript"
                    ? "TypeScript"
                    : lang === "python"
                      ? "Python"
                      : lang;
                })()
              : tag;

            return (
              <div
                key={tag}
                className={cn(
                  "inline-flex items-center gap-1 rounded-md border px-2 py-1 text-xs font-medium",
                  isLanguage
                    ? "bg-blue-50 border-blue-100 text-blue-700"
                    : "bg-primary-50 border-primary-100 text-primary-700",
                )}
              >
                <span className="font-suisse text-[11px] font-medium uppercase">
                  {displayValue}
                </span>
                <XIcon
                  className={cn(
                    "size-3 cursor-pointer transition-colors",
                    isLanguage
                      ? "hover:text-blue-900"
                      : "hover:text-primary-900",
                  )}
                  onClick={() => handleTagToggle(tag)}
                />
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
