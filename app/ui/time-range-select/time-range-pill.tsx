import { cn } from "#/utils/classnames";

export const TimeRangePill = ({
  children,
  isLive,
}: {
  children?: React.ReactNode;
  isLive?: boolean;
}) => {
  return (
    <span
      className={cn(
        "text-normal mr-0.5 w-8 rounded-[3px] py-px text-center text-[11px] transition-colors bg-primary-200 group-hover:bg-primary-300 text-primary-600",
        {
          "bg-accent-100 text-accent-700 group-hover:bg-accent-200 text-[10px]":
            isLive,
        },
      )}
    >
      {isLive ? "LIVE" : children}
    </span>
  );
};
