import {
  isArray,
  isObject,
  safeDeserializePlainStringAsJSON,
} from "#/utils/object";
import { type chatCompletionMessageToolCallSchema } from "@braintrust/core/typespecs";
import { useMemo, useState } from "react";
import { z } from "zod";
import { TreeViewer } from "./tree";
import { Bolt } from "lucide-react";
import { SyntaxHighlight } from "./syntax-highlighter";
import { MarkdownViewer } from "./markdown";
import { ControlledCollapsibleSection } from "./collapsible-section";
import { type Virtualizer } from "@tanstack/react-virtual";

export type ChatCompletionMessageToolCall = z.infer<
  typeof chatCompletionMessageToolCallSchema
>;

export function ToolsViewer({
  toolCalls,
  toolResponses,
  idPrefix,
  virtualizer,
}: {
  toolCalls: ChatCompletionMessageToolCall[];
  toolResponses?: Map<string, unknown>;
  idPrefix?: string;
  virtualizer?: Virtualizer<HTMLDivElement, Element>;
}) {
  return (
    <div className="flex flex-col gap-2">
      {toolCalls.map((toolCall) => (
        <ToolViewer
          key={toolCall.id}
          toolCall={toolCall}
          toolResponse={toolResponses?.get(toolCall.id)}
          id={idPrefix ? `${idPrefix}-${toolCall.id}` : undefined}
          virtualizer={virtualizer}
        />
      ))}
    </div>
  );
}

export function ToolViewer({
  toolCall,
  toolResponse,
  id,
  virtualizer,
}: {
  toolCall: ChatCompletionMessageToolCall;
  toolResponse?: unknown;
  id?: string;
  virtualizer?: Virtualizer<HTMLDivElement, Element>;
}) {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const parsedArguments = useMemo(
    () =>
      z
        .record(z.unknown())
        .safeParse(
          safeDeserializePlainStringAsJSON(toolCall.function.arguments),
        ),
    [toolCall.function.arguments],
  );

  const emptyArgs = useMemo(
    () =>
      (parsedArguments.success &&
        Object.keys(parsedArguments.data).length === 0) ||
      toolCall.function.arguments.trim() === "",
    [parsedArguments, toolCall.function.arguments],
  );

  return (
    <div
      className="flex flex-col rounded-md border px-2 bg-primary-100/50"
      id={id}
    >
      <ControlledCollapsibleSection
        className="text-xs"
        defaultCollapsed
        isCollapsed={isCollapsed}
        setIsCollapsed={(c) => {
          if (virtualizer) {
            // https://github.com/TanStack/virtual/issues/562#issuecomment-2065858040
            // eslint-disable-next-line react-compiler/react-compiler
            virtualizer.shouldAdjustScrollPositionOnItemSizeChange = () =>
              false;
          }
          setIsCollapsed(c);
          setTimeout(() => {
            if (!virtualizer) return;
            virtualizer.shouldAdjustScrollPositionOnItemSizeChange = undefined;
          }, 0);
        }}
        expandedClassName="-mx-2 px-2 hover:px-2"
        collapsedClassName="-mx-2 px-2 bg-transparent"
        title={
          <div className="inline-flex items-center text-xs">
            <span className="mr-1.5 inline-block rounded p-0.5 bg-amber-600 text-amber-100 dark:bg-amber-400 dark:text-amber-900">
              <Bolt className="size-2.5" />
            </span>
            <span className="font-medium">{toolCall.function.name}</span>
          </div>
        }
      >
        <div className="flex flex-col gap-2 pb-2">
          <span className="font-mono text-[11px] text-primary-500">
            {toolCall.id}
          </span>
          <div>
            {emptyArgs ? (
              <div className="text-xs text-primary-400">No parameters</div>
            ) : parsedArguments.success ? (
              <ArgumentsViewer args={parsedArguments.data} />
            ) : (
              <SyntaxHighlight
                language="json"
                content={toolCall.function.arguments}
                className="bg-transparent"
              />
            )}
          </div>

          {toolResponse !== undefined && (
            <div>
              <div className="my-1 text-xs font-medium text-primary-600">
                Result
              </div>
              <div className="text-xs">
                <ValueViewer value={toolResponse} />
              </div>
            </div>
          )}
        </div>
      </ControlledCollapsibleSection>
    </div>
  );
}

function ArgumentsViewer({ args }: { args: Record<string, unknown> }) {
  const entries = Object.entries(args);
  return (
    <div className="flex flex-col gap-2 text-xs text-primary-600">
      {entries.length > 0 && <div className="font-medium">Parameters</div>}
      {entries.map(([key, value]) => (
        <div key={key} className="flex flex-col gap-0.5">
          <div className="font-mono text-primary-500">{key}</div>
          <div>
            <ValueViewer value={value} />
          </div>
        </div>
      ))}
    </div>
  );
}

export function ValueViewer({ value }: { value: unknown }) {
  if (typeof value === "string") {
    return (
      <MarkdownViewer
        className="prose-xs m-0 w-full break-all p-0 text-xs prose-pre:!p-0"
        value={value}
      />
    );
  } else if (isObject(value) || isArray(value)) {
    return <TreeViewer value={value} />;
  } else {
    return JSON.stringify(value);
  }
}
