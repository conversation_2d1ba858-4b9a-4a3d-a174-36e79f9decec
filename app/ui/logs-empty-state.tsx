import {
  EmptyStateCodeSnippets,
  ORGS_WITH_DISABLED_EMPTY_STATE_CODE_SNIPPETS,
  TableEmptyState,
} from "./table/TableEmptyState";
import { Spinner } from "./icons/spinner";
import { ExternalLink } from "./link";

export const LogsEmptyState = ({
  orgName,
  projectName,
}: {
  orgName: string;
  projectName: string;
}) => {
  return (
    <TableEmptyState
      label={
        <>
          <div className="mb-3 flex items-center justify-center gap-2 text-xs text-primary-900">
            <div className="mb-4 flex items-center gap-2 rounded px-2 py-1 bg-primary-900 text-primary-100">
              <Spinner className="size-3" /> Waiting for logs
            </div>
          </div>
          There are no logs in this project yet. To get started, try this code
          snippet or{" "}
          <ExternalLink href={"/docs/guides/logging"}>
            learn&nbsp;more
          </ExternalLink>
          .
        </>
      }
    >
      {ORGS_WITH_DISABLED_EMPTY_STATE_CODE_SNIPPETS.includes(orgName) ? null : (
        <EmptyStateCodeSnippets
          ts={`import { initLogger, traced } from "braintrust";

const logger = initLogger({
  projectName: "${projectName}",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

async function someLLMFunction(input: string) {
  return traced(async (span) => {
    // Replace with your LLM call
    const output = await invokeLLM(input);
    span.log({ input, output });
  });
}`}
          py={`from braintrust import init_logger, traced

logger = init_logger(project="${projectName}")

@traced
def some_llm_function(input):
    return invoke_llm(input)


def my_route_handler(req):
    return some_llm_function(req.body)`}
        />
      )}
    </TableEmptyState>
  );
};
