import { invoke } from "#/app/app/[org]/prompt/[prompt]/scorers/invoke";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { BraintrustStream } from "braintrust";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useState,
} from "react";
import { toast } from "sonner";
import { type PromptData } from "#/ui/prompts/schema";
import { type chatCompletionMessageParamSchema } from "@braintrust/core/typespecs";
import z from "zod";
import { parallelToolCallSchema } from "@braintrust/local/functions";

export type ChatCompletionMessage = z.infer<
  typeof chatCompletionMessageParamSchema
>;

export function useRunPrompt({
  promptData,
  runData,
  onStreamChunk,
  onStreamDone,
  onError,
  setToolDefinitions,
  extraMessages,
}: {
  promptData?: PromptData;
  runData?: {
    input?: unknown;
    expected?: unknown;
    metadata?: Record<string, unknown>;
  } | null;
  onStreamChunk: (text: string) => void;
  onStreamDone?: (messages: ChatCompletionMessage[]) => void;
  onError?: (error: string) => void;
  setToolDefinitions?: Dispatch<SetStateAction<Map<string, string>>>;
  extraMessages?: ChatCompletionMessage[];
}) {
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();

  const [isGenerating, setIsGenerating] = useState(false);

  const runPrompt = useCallback(
    async (runTimeMessages?: ChatCompletionMessage[]) => {
      if (!promptData) {
        toast.error("Prompt not loaded");
        return;
      }

      setIsGenerating(true);
      let result: Response | null = null;

      const promptDataMessages =
        promptData.prompt?.type === "chat"
          ? promptData.prompt.messages
          : [
              {
                content: promptData.prompt?.content ?? "",
                role: "assistant" as const,
              },
            ];
      // Include extra messages at the end of the prompt but before the run time messages, rather than passing them as `messages`. This matches the UI in chat mode.
      const additionalMessages = (extraMessages ?? []).concat(
        runTimeMessages ?? [],
      );
      const inlinePrompt =
        additionalMessages.length > 0
          ? {
              ...promptData,
              prompt: {
                ...promptData.prompt,
                type: "chat" as const,
                messages: promptDataMessages.concat(additionalMessages),
              },
            }
          : promptData;

      try {
        result = await invoke({
          orgName: org.name,
          sessionToken: await getOrRefreshToken(),
          proxyUrl: org.proxy_url,
          functionId: {
            inline_prompt: inlinePrompt,
          },
          input: runData?.input,
          expected: runData?.expected,
          metadata: runData?.metadata,
          stream: true,
        });
      } catch (e) {
        console.error(e);
        const errorMessage = e instanceof Error ? e.message : String(e);
        toast.error("Failed to run prompt", {
          description: errorMessage,
        });
        onError?.(errorMessage);
        setIsGenerating(false);
        return;
      }

      if (!result) {
        console.warn("No result");
        setIsGenerating(false);
        return;
      }

      const body = result.body;
      if (!body) {
        toast.error("No response body");
        setIsGenerating(false);
        return;
      }

      await processResultStream({
        stream: body,
        onStreamChunk,
        onStreamDone,
        setToolDefinitions,
      });
      setIsGenerating(false);
    },
    [
      promptData,
      onStreamChunk,
      onStreamDone,
      org.name,
      org.proxy_url,
      getOrRefreshToken,
      runData?.input,
      runData?.expected,
      runData?.metadata,
      setToolDefinitions,
      extraMessages,
      onError,
    ],
  );

  return {
    runPrompt,
    isGenerating,
  };
}

async function processResultStream({
  stream: rawStream,
  onStreamChunk,
  onStreamDone,
  setToolDefinitions,
}: {
  stream: ReadableStream<Uint8Array>;
  onStreamChunk: (text: string) => void;
  onStreamDone?: (messages: ChatCompletionMessage[]) => void;
  setToolDefinitions?: Dispatch<SetStateAction<Map<string, string>>>;
}) {
  const textChunks: string[] = [];
  const jsonChunks: string[] = [""];
  const errors: string[] = [];

  let previewText = "";

  const stream = new BraintrustStream(rawStream);
  const reader = stream.toReadableStream().getReader();
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    switch (value.type) {
      case "text_delta":
        textChunks.push(value.data);
        previewText += value.data;
        break;
      case "json_delta":
        jsonChunks.push(value.data);
        previewText += value.data;
        break;
      case "progress":
        switch (value.data.event) {
          case "json_delta":
            jsonChunks[jsonChunks.length - 1] += value.data.data;
            break;
          case "start":
            jsonChunks.push("");
            break;
          default:
            console.warn("Unknown progress event", value.data.data);
        }
        break;
      case "error":
        errors.push("ERROR! " + value.data);
        break;
      case "done":
        break;
      default:
        console.warn("Unknown stream type", value);
    }
    onStreamChunk(previewText);
  }

  const maybeToolCalls = jsonChunks.map((chunk) => {
    try {
      return JSON.parse(chunk);
    } catch (e) {
      return null;
    }
  });

  const toolCalls: Array<{
    tool_call_id: string;
    function_name: string;
    arguments: Record<string, unknown>;
  }> = [];
  const toolResults: unknown[] = [];

  for (let i = 0; i < maybeToolCalls.length; i++) {
    const toolCallCandidate = maybeToolCalls[i];
    const safeParsed = z
      .array(parallelToolCallSchema)
      .safeParse(toolCallCandidate);
    if (safeParsed.success && safeParsed.data.length > 0) {
      toolCalls.push(safeParsed.data[0]);
      // The next entry (if exists) is the tool result
      const toolResult = maybeToolCalls[i + 1];
      toolResults.push(JSON.stringify(toolResult));
      i++; // Skip the result in the next iteration
    }
  }

  if (toolCalls.length > 0) {
    setToolDefinitions?.((prev) => {
      const newMap = new Map(prev);
      toolCalls.forEach((toolCall) => {
        newMap.set(toolCall.tool_call_id, toolCall.function_name);
      });
      return newMap;
    });

    onStreamDone?.([
      {
        content: textChunks.length > 0 ? textChunks.join("") : "",
        role: "assistant" as const,
        tool_calls: toolCalls.map((toolCall) => ({
          id: toolCall.tool_call_id,
          type: "function" as const,
          function: {
            name: toolCall.function_name,
            arguments: JSON.stringify(toolCall.arguments),
          },
        })),
      },
      {
        role: "tool" as const,
        content: toolResults.join(""),
        tool_call_id: toolCalls[0].tool_call_id,
      },
    ]);
  } else {
    onStreamDone?.([
      {
        content:
          textChunks.length > 0 ? textChunks.join("") : jsonChunks.join(""),
        role: "assistant" as const,
      },
    ]);
  }
}
