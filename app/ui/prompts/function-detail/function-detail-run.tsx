import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import { FunctionRunSection } from "#/ui/prompts/function-editor/function-run-section";
import { useMemo, useRef, useState } from "react";
import { useAvailableModels } from "#/ui/prompts/models";
import { type CompletionBlockHandle } from "#/app/app/[org]/prompt/[prompt]/completion-block";
import useEvent from "react-use-event-hook";
import { toast } from "sonner";
import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { makeFunctionEditorFunctionId } from "#/ui/prompts/hooks";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";

export function FunctionDetailRun({
  getOutputPrompt,
  type,
  projectId,
  orgName,
  func,
  isUpdate,
}: {
  func: UIFunction;
  getOutputPrompt: (prompt: SyncedPlaygroundBlock) => UIFunction;
  type: FunctionObjectType;
  projectId: string;
  orgName: string;
  isUpdate: boolean;
}) {
  const { sortedSyncedPromptsAtom_ROOT } = useSyncedPrompts();
  const prompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0]),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );
  const [dataEditorValue, setDataEditorValue] = useState<
    Record<string, unknown>
  >(
    prompt.function_data.type === "code"
      ? { input: "", expected: "", output: "", metadata: {} }
      : {},
  );

  const { noConfiguredSecrets } = useAvailableModels({ orgName });
  const cellRef = useRef<CompletionBlockHandle>(null);
  const runPrompt = useEvent(async () => {
    if (!cellRef.current) {
      return;
    }

    const outputPrompt = getOutputPrompt(prompt);
    const functionId = makeFunctionEditorFunctionId(outputPrompt, isUpdate);
    if (!functionId) {
      toast.warning(`No ${type} to run`);
      return;
    }

    cellRef.current.submit(
      {
        type: "function",
        functionId,
        input: dataEditorValue,
        parent: {
          object_type: "project_logs",
          object_id: projectId,
        },
      },
      orgName,
    );
  });

  return (
    <div className="flex-1 overflow-y-scroll px-3 py-4 @container">
      <div className="max-w-[900px]">
        <FunctionRunSection
          showNoConfiguredSecretsMessage={noConfiguredSecrets}
          dataEditorValue={dataEditorValue}
          setDataEditorValue={setDataEditorValue}
          promptData={prompt.prompt_data}
          initialFunction={func}
          runPrompt={runPrompt}
          ref={cellRef}
          shouldInitializeMissingPromptVariables
        />
      </div>
    </div>
  );
}
