import { useOrg } from "#/utils/user";
import {
  literalValueSchema,
  type SortExpr,
  type Expr as ParsedExpr,
  type ParsedQuery,
} from "@braintrust/btql/parser";
import {
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
  useCallback,
} from "react";
import { type Field as ArrowField } from "apache-arrow";
import {
  type PageParams,
  useFilters,
  type PaginatedObjectViewerDataComponents,
  type PaginatedObjectViewerDataComponentsArgs,
  type LoadingStatusType,
} from "./paginated-object-viewer";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useBtqlFlags, useIsFeatureEnabled } from "#/lib/feature-flags";
import {
  addClause,
  type Clause,
  useScoreMetricsTopLevelFields,
} from "#/utils/search/search";
import {
  BUILT_IN_CUSTOM_COLUMNS,
  useCustomColumns,
} from "#/utils/custom-columns/use-custom-columns";
import { type DataObjectSearch } from "#/utils/btapi/btapi";
import {
  ComputedDurationMetricFields,
  TransactionIdField,
  TagsField,
  extractLogicalSchemaItemsObject,
  doubleQuote,
} from "@braintrust/local/query";
import {
  dataObjectPageShape,
  fetchBtql,
  mergeRealtimeStates,
  parseBtqlSchema,
  type RowWithIds,
  rowWithIdsSchema,
} from "#/utils/btql/btql";
import { useQueries, useQueryClient } from "@tanstack/react-query";
import { isEmpty, jsonSerializableDeepEqual, strMax } from "#/utils/object";
import { type TraceViewParams, type ExpandedRowParams } from "./trace/trace";
import { setTagSearchFn, useTagsFormatter } from "./trace/tags";
import { useTableGroupingControl } from "./table/grouping/controls";
import { GROUP_BY_NONE_VALUE } from "./charts/selectionTypes";
import { makeFormatterMap } from "./table/formatters/header-formatters";
import { GroupKeyFormatterWithCell } from "./table/formatters/group-key-formatter";
import { SpanTypeInfoFormatter } from "./table/formatters/span-info-formatter";
import { InputFormatter } from "./table/formatters/input-formatter";
import { PercentWithAggregationFormatter } from "./table/formatters/percent-formatter";
import { DurationWithAggregationFormatter } from "./table/formatters/duration-formatter";
import { MetricAggregationFormatter } from "./table/formatters/default-formatter";
import { StartEndFormatter } from "./table/formatters/start-end-formatter";
import { ErrorCellWithFormatter } from "./table/formatters/error-formatter";
import { CurlyBraces } from "lucide-react";
import { type FormatterMap } from "./field-to-column";
import { useTableSelection } from "./table/useTableSelection";
import { useSessionToken } from "#/utils/auth/session-token";
import { combineResults, keepPreviousQueries } from "#/utils/react-query";
import { useJSONMemo } from "#/utils/memo";
import { type ChannelSpec, type TransactionId } from "#/utils/duckdb";
import { useMutableObject } from "#/utils/mutable-object";
import { type RowData } from "./arrow-table";
import { useRealtimeChannel } from "#/utils/simple-channel";

import { type ResponseSchema } from "@braintrust/btql/binder";
import { type LogicalSchema, mergeSchemas } from "@braintrust/btql/schema";
import { useViewStates } from "#/utils/view/use-view";
import { useBtqlClauseChecker } from "#/utils/search-btql";
import { useBtqlObjectIdResolver } from "./use-object-id-resolver";
import { useInferCustomColumnPaths } from "#/utils/custom-columns/use-infer-custom-column-paths";
import { CommentsFormatterFactory } from "./table/formatters/comments-formatter";
import { OrgUsersContext } from "#/utils/org-users-context";
import {
  type BtqlQueryBuilder,
  useBtqlQueryBuilder,
} from "#/utils/btql/use-query-builder";
import { Mutex } from "async-mutex";
import { useTempDuckDataTable } from "#/utils/queries/useTempDuckTable";
import {
  modelArrowSchema,
  modelTableDefinition,
  useAvailableModels,
} from "./prompts/models";
import {
  auditLogBaseEventSchema,
  type CustomColumnScope,
} from "@braintrust/local/api-schema";
import { throttle } from "throttle-debounce";
import useEvent from "react-use-event-hook";
import { getObjValueByPath } from "@braintrust/core";
import { type Filter } from "#/utils/search/simple-tree";
import { withErrorTiming } from "#/utils/btapi/type-error";
import { MAX_NEW_ROWS } from "./live-button";
import {
  setAssignmentsSearchFn,
  useAssignmentsFormatter,
} from "./trace/assign";
import { BT_ASSIGNMENTS, BT_ASSIGNMENTS_META_FIELD } from "#/utils/assign";
import { type RealtimeState } from "@braintrust/local/app-schema";

export const TABLE_BLACKLIST = [
  "comparison_key",
  "root_span_id",
  "origin",
  "span_attributes",
  "project_id",
  "dataset_id",
  "is_root",
  "_pagination_key",
];

// Note: keep in sync with projectedPaths in paginated-object-viewer.tsx
const COLUMN_ORDER = [
  "span_type_info",
  TransactionIdField,
  "input",
  "output",
  "error",
  "expected",
  TagsField,
  "scores",
  "metrics",
  "metadata",
  "created",
  "id",
];

const EMPTY_ARRAY: string[][] = [];

const MAX_REALTIME_TRACES_PAGE = 1000;

interface UpdatedRow {
  root_span_id: string;
  xact_id: TransactionId;
  pending_xact_id?: TransactionId; // Track pending updates
  data: RowWithIds & Record<string, unknown>;
  schema: ResponseSchema | undefined;
}

interface RefreshParams {
  paginationKeyFloor: string | null;
  onLoaded: () => void;
  onError: (error: Error) => void;
}

// TODO before we can remove the feature flag:
// - We should be able to have a real live button?
// - Comments in table
// - Add docs on new summary view and update btql sandbox, etc.

// Key open problems / differences from the normal paginated object viewer:
// 1. We don't load row id searches in the table, which means that if you paginate to a row, it'll show up
//    in the trace viewer, but not in the table.

export type SummaryPaginatedObjectViewerTableQuery<
  TsTable extends RowData,
  TsValue,
> = {
  type: "tableData";
  data: TsTable[] | null;
  fields: ArrowField[] | undefined;
  formatters?: FormatterMap<TsTable, TsValue>;
  queryErrors?: Error[];
};

export function useSummaryPaginatedObjectViewerDataComponents<
  TsTable extends RowData,
  TsValue,
>({
  objectType,
  objectId,
  objectName,
  selectedBucket,
  setSavingState,
  traceViewParamArgs,
  pageSize,
  viewParams,
  enableStarColumn,
}: Omit<
  PaginatedObjectViewerDataComponentsArgs,
  "viewProps" | "useClauseCheckerProps"
>): Omit<
  PaginatedObjectViewerDataComponents<TsTable, TsValue>,
  "tableQuery"
> & {
  tableQuery: SummaryPaginatedObjectViewerTableQuery<TsTable, TsValue>;
  realtimeState: RealtimeState;
} {
  const org = useOrg();
  const { config: projectConfig } = useContext(ProjectContext);
  const { projectId } = useContext(ProjectContext);
  const { orgUsers } = useContext(OrgUsersContext);

  const btqlFlags = useBtqlFlags();
  const brainstore = useIsFeatureEnabled("brainstore");
  const fastExperimentSummary = useIsFeatureEnabled("fastExperimentSummary");

  const extraBubbleFilters = useMemo(
    () => (selectedBucket ? [selectedBucket] : []),
    [selectedBucket],
  );
  const [clauseCheckerTable, setClauseCheckerTable] =
    useState<LogicalSchema | null>(null);

  const clauseCheckerProps = useBtqlClauseChecker(clauseCheckerTable);
  const pageIdentifier = objectType + "-" + objectId;

  const viewProps = useViewStates({
    viewParams,
    clauseChecker: clauseCheckerProps.clauseChecker,
    pageIdentifier,
    bypassClauseChecker: true,
  });

  const { clauseChecker, setTopLevelFields } = clauseCheckerProps;

  const search = useMemo(
    () =>
      (extraBubbleFilters ?? []).reduce(
        (acc, f) => addClause(acc, f),
        viewProps.search,
      ),
    [extraBubbleFilters, viewProps.search],
  );
  const searchDebounced = useMemo(
    () =>
      (extraBubbleFilters ?? []).reduce(
        (acc, f) => addClause(acc, f),
        viewProps.searchDebounced,
      ),
    [extraBubbleFilters, viewProps.searchDebounced],
  );

  const scope: CustomColumnScope | undefined = useMemo(() => {
    const colsObjectType = objectType === "dataset" ? objectType : "project";
    const colsObjectId = objectType === "dataset" ? objectId : projectId;
    const colsSubtype = objectType === "project_logs" ? "project_log" : "";
    return colsObjectId
      ? {
          object_type: colsObjectType,
          object_id: colsObjectId,
          subtype: colsSubtype,
          variant: colsSubtype || colsObjectType,
        }
      : undefined;
  }, [objectType, objectId, projectId]);

  const {
    customColumnDefinitions,
    customColumnsEnabled,
    createCustomColumn,
    updateCustomColumn,
    deleteCustomColumn,
    isLoading: customColumnsLoading,
  } = useCustomColumns({ scope });

  const builder = useBtqlQueryBuilder({});

  // NOTE: We seem to change the filters quite often through various effects that I don't understand.
  // At the end of the day, they turn into a simple JSON-serializable object, that does not change often,
  // so we might as well json memoize them.
  const filters = useJSONMemo(
    useFilters({
      search: searchDebounced,
      brainstore,
      selectedBucket,
      objectType,
      timeSpan: viewProps.timeRangeFilter,
    }),
  );

  // Note: keep these initial values in sync with the reset values in
  // `rowPageSearchParams`.
  const [desiredNumTraces, setDesiredNumTraces] = useState<number>(pageSize);
  const [pages, setPages] = useState<PageParams[]>([{}]);

  const [refreshes, setRefreshes] = useState<RefreshParams[]>([]);
  const newRowIds = useRef<Set<string>>(new Set());
  const [numNewRowIds, setNumNewRowIds] = useState<number>(0);

  const setNewRowIds = useCallback(
    (ids: Set<string> | ((prev: Set<string>) => Set<string>)) => {
      let unclippedIds;
      if (typeof ids === "function") {
        unclippedIds = ids(newRowIds.current);
      } else {
        unclippedIds = ids;
      }
      unclippedIds = Array.from(unclippedIds).slice(0, MAX_NEW_ROWS);
      newRowIds.current = new Set(unclippedIds);
      setNumNewRowIds(newRowIds.current.size);
    },
    [],
  );

  const queryClient = useQueryClient();

  // The PageParams we accumulate over time are dependent on the exact
  // DataObjectSearches we are conducting, because the pagination cursors will
  // change whenever the search params change. This means we have to reset the
  // pages whenever the search params change.
  const rowPageSearchParams = useMemo(() => {
    // Each time we recompute these, reset the list of pages. Also reset the
    // desired number of traces so we don't try to load too many results if you
    // have already scrolled a lot.
    setPages([{}]);
    setDesiredNumTraces(pageSize);

    setRefreshes([]);
    // TODO: Fix?
    // eslint-disable-next-line react-compiler/react-compiler
    setNewRowIds(new Set());

    queryClient.invalidateQueries({
      queryKey: ["summaryPaginatedObjectViewer"],
    });
    const sortSpec = viewProps.search.sort?.[0];
    const sort: SortExpr[] = (
      sortSpec?.spec
        ? [
            {
              expr: { btql: sortSpec.spec.col.id },
              dir: sortSpec.spec.col.desc
                ? ("desc" as const)
                : ("asc" as const),
            },
          ]
        : []
    ).concat([{ expr: { btql: "_pagination_key" }, dir: "desc" }]);

    return {
      objectType,
      objectId,
      pageSize,
      filters,
      sort,
      sortSpec,
      builder,
    };
  }, [
    pageSize,
    setNewRowIds,
    queryClient,
    objectType,
    objectId,
    filters,
    viewProps.search.sort,
    builder,
  ]);

  const rowSearches = useMemo((): (DataObjectSearch & { floor?: string })[] => {
    const { objectId, pageSize, filters, sort, sortSpec, builder } =
      rowPageSearchParams;
    return objectId
      ? refreshes
          .slice()
          .reverse()
          .map((r): DataObjectSearch & { floor?: string } => ({
            id: objectId,
            limit: MAX_REALTIME_TRACES_PAGE,
            filters: {
              sql: filters.sql,
              btql: filters.btql.concat(
                r.paginationKeyFloor
                  ? [
                      {
                        op: "gt" as const,
                        left: { btql: "_pagination_key" },
                        right: { op: "literal", value: r.paginationKeyFloor },
                      },
                    ]
                  : [],
              ),
            },
            btqlCursor: undefined,
            floor: r.paginationKeyFloor ?? undefined,
          }))
          .concat(
            pages.map(
              (p): DataObjectSearch => ({
                id: objectId,
                limit: p.limit ?? pageSize,
                filters: formatFiltersWithSort({
                  sort: sortSpec,
                  filters,
                  pageParams: p,
                  builder,
                }),
                btqlCursor: p.cursor,
                sort,
              }),
            ),
          )
      : [];
    // Note: do not let the set of dependencies grow. Instead, add dependencies to
    // `rowPageSearchParams`.
  }, [pages, refreshes, rowPageSearchParams]);

  const isTraceView = ["experiment", "project_logs"].includes(objectType);

  // START BRAINSTORE BASED QUERY FETCHER

  const { getOrRefreshToken } = useSessionToken();

  const shape = useMemo(() => dataObjectPageShape(objectType), [objectType]);

  const {
    data: allTableQueryData,
    isPending,
    pending,
    errors: isQueryError,
  } = useQueries({
    queries: rowSearches.map((s, i) => ({
      queryKey: [
        "summaryPaginatedObjectViewer",
        objectType,
        s.id,
        shape,
        s.limit,
        s.btqlCursor,
        customColumnDefinitions ?? [],
        s.filters,
        s.floor, // NOTE: If you change this, you need to update the predicate in the refresh logic (search for invalidateQueries)
        s.sort,
      ],
      queryFn: withErrorTiming(
        async ({ signal }: { signal: AbortSignal }) =>
          await fetchBtql({
            args: {
              query: {
                filter: builder.and(...(s.filters?.btql ?? [])),
                from: builder.from(objectType, [s.id], shape),
                select: [{ op: "star" }],
                sort:
                  shape !== "summary"
                    ? s.sort?.map((s) => {
                        const sortCol =
                          "btql" in s.expr ? s.expr.btql : undefined;
                        const customCol = sortCol
                          ? customColumnDefinitions?.find(
                              (c) => c.name === sortCol,
                            )
                          : undefined;
                        return customCol
                          ? { ...s, expr: { btql: customCol.expr } }
                          : s;
                      })
                    : s.sort,
                limit: s.limit,
                cursor: s.btqlCursor,
                custom_columns: [
                  {
                    alias: BT_ASSIGNMENTS,
                    expr: {
                      btql: `metadata.${doubleQuote(BT_ASSIGNMENTS_META_FIELD)}`,
                    },
                  },
                ].concat(
                  shape === "summary"
                    ? []
                    : (customColumnDefinitions || []).map((c) => ({
                        expr: { btql: c.expr },
                        alias: c.name,
                      })),
                ),
              },
              brainstoreRealtime: true,
            },
            btqlFlags,
            apiUrl: org.api_url,
            getOrRefreshToken,
            schema: rowWithIdsSchema,
            signal,
          }),
        `Log search query (${objectType}:${s.id} ${shape}) org=${org.name}`,
        {
          objectType,
          page: `${i}`,
        },
      ),
      throwOnError: false,
      placeholderData: keepPreviousQueries<
        Awaited<ReturnType<typeof fetchBtql<RowWithIds>>>
      >(queryClient, {
        queryKey: [
          "summaryPaginatedObjectViewer",
          objectType,
          s.id,
          shape,
          s.limit,
          s.btqlCursor,
        ],
      }),
      enabled:
        !!objectId &&
        !!objectType &&
        (shape === "summary" || !customColumnsLoading),
      queryClient,
    })),
    combine: combineResults,
  });

  const [updatedIds, setUpdatedIds] = useState<Map<string, UpdatedRow>>(
    new Map(),
  );

  // Create a table coordinator outside React lifecycle for pure coordination
  const tableCoordinator = useMemo(() => {
    const pendingRows = new Set<string>();
    return {
      lockRows: (ids: string[]) => ids.forEach((id) => pendingRows.add(id)),
      unlockRows: (ids: string[]) =>
        ids.forEach((id) => pendingRows.delete(id)),
      isLocked: (id: string) => pendingRows.has(id),
    };
  }, []); // Empty deps - created once and stable forever

  const mergedSchema = useJSONMemo(
    useMemo(() => {
      const tables = allTableQueryData
        .map((result) => result?.data)
        .filter(Boolean);

      if (tables.length === 0) {
        return undefined;
      }

      let fullSchema: ResponseSchema | undefined;

      for (let i = 0; i < tables.length; i++) {
        const table = tables[i];
        if (!table) {
          continue;
        }

        const currSchema = allTableQueryData[i]?.schema;
        if (!fullSchema) {
          fullSchema = JSON.parse(JSON.stringify(currSchema));
        } else if (currSchema) {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          fullSchema.items = mergeSchemas(
            fullSchema.items,
            currSchema.items,
          ) as typeof fullSchema.items;
        }

        for (let i = 0; i < table.length; i++) {
          const updated = updatedIds.get(table[i].id);
          if (updated) {
            if (updated.data._object_delete) {
              // skip deleted rows
              continue;
            }

            if (fullSchema && updated.schema) {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              fullSchema.items = mergeSchemas(
                fullSchema.items,
                updated.schema.items,
              ) as typeof fullSchema.items;
            }
          }
        }
      }

      if (fullSchema) {
        return { ...fullSchema };
      } else {
        return undefined;
      }
    }, [allTableQueryData, updatedIds]),
  );

  const realtimeState = useMemo(() => {
    const states = allTableQueryData.reduce(
      (acc: RealtimeState, result) => {
        if (!result?.realtime_state) {
          return acc;
        }

        return mergeRealtimeStates(acc, result.realtime_state);
      },
      {
        type: "on",
        minimum_xact_id: null,
        read_bytes: 0,
        actual_xact_id: null,
      },
    );

    return states;
  }, [allTableQueryData]);

  const tableQueryFields = useMemo(() => {
    const fieldNames = new Set<string>();
    for (const property of Object.keys(mergedSchema?.items.properties ?? {})) {
      fieldNames.add(property);
    }
    return fieldNames;
  }, [mergedSchema]);

  useEffect(() => {
    if (mergedSchema) {
      const logicalSchema = extractLogicalSchemaItemsObject(objectType);
      const merged = mergeSchemas(logicalSchema, mergedSchema.items);
      setClauseCheckerTable(merged ?? null);
    }
  }, [mergedSchema, objectType]);

  const projectedPaths = useMemo(
    () =>
      Array.from(tableQueryFields ?? [])
        .filter((f) => !TABLE_BLACKLIST.includes(f))
        .sort((a, b) => {
          const aIndex = COLUMN_ORDER.indexOf(a);
          const bIndex = COLUMN_ORDER.indexOf(b);
          if (aIndex !== -1 && bIndex !== -1) {
            return aIndex - bIndex;
          }
          if (aIndex !== -1) return -1;
          if (bIndex !== -1) return 1;
          return 0;
        }),
    [tableQueryFields],
  );

  const realtimeSpec: ChannelSpec | null = useMemo(
    () =>
      objectId
        ? {
            objectType,
            id: objectId,
            audit_log: false,
            shape,
          }
        : null,
    [objectId, objectType, shape],
  );

  const auditLogRealtimeSpec: ChannelSpec | null = useMemo(
    () =>
      objectId
        ? {
            objectType,
            id: objectId,
            audit_log: true,
            shape: "summary",
          }
        : null,
    [objectId, objectType],
  );

  const currentlyVisibleRootSpanIds = useRef<Set<string>>(new Set());
  const currentVisibleIds = useRef<Set<string>>(new Set());

  useEffect(() => {
    currentlyVisibleRootSpanIds.current = new Set(
      allTableQueryData
        .map((result) => result?.data)
        .filter(Boolean)
        .flatMap((table) => table?.map((row) => row.root_span_id) ?? []),
    );
    currentVisibleIds.current = new Set(
      allTableQueryData
        .map((result) => result?.data)
        .filter(Boolean)
        .flatMap((table) => table?.map((row) => row.id) ?? []),
    );
    // Subtract any fetched ids from the set of new ids.
    setNewRowIds(difference(newRowIds.current, currentVisibleIds.current));
  }, [allTableQueryData, setNewRowIds]);

  const maxPaginationKey = useRef<string | null>(null);
  useEffect(() => {
    maxPaginationKey.current = allTableQueryData.reduce(
      (acc: string | null, result) => {
        const maxPaginationKey: string | null =
          result?.data.reduce(
            (acc: string | null, row) => strNullMax(acc, row._pagination_key),
            null,
          ) ?? null;
        return strNullMax(acc, maxPaginationKey);
      },
      null,
    );
  }, [allTableQueryData]);

  const idsToRefetch = useRef<Set<string>>(new Set());

  const refetchObjectIds = useEvent(() => {
    (async () => {
      // Snapshot the current IDs and only clear the ones we're about to process
      const idsToProcess = Array.from(idsToRefetch.current);

      if (!objectId || idsToProcess.length === 0) {
        return;
      }

      // Clear all IDs at once since we already have a snapshot
      idsToRefetch.current.clear();

      const result = await fetchBtql({
        args: {
          query: {
            filter: builder.or(
              ...idsToProcess.map(
                (id): ParsedExpr => ({
                  op: "eq" as const,
                  left: { btql: "id" },
                  right: { op: "literal", value: id },
                }),
              ),
            ),
            from: builder.from(objectType, [objectId], shape),
            select: [{ op: "star" }],
            sort: [{ expr: { btql: "_pagination_key" }, dir: "desc" }],
            custom_columns: [
              {
                alias: BT_ASSIGNMENTS,
                expr: {
                  btql: `metadata.${doubleQuote(BT_ASSIGNMENTS_META_FIELD)}`,
                },
              },
            ].concat(
              shape === "summary"
                ? []
                : (customColumnDefinitions || []).map((c) => ({
                    expr: { btql: c.expr },
                    alias: c.name,
                  })),
            ),
          },
          brainstoreRealtime: true,
          disableLimit: true,
        },
        btqlFlags,
        apiUrl: org.api_url,
        getOrRefreshToken,
        schema: rowWithIdsSchema,
      });

      setUpdatedIds((prev) => {
        const newMap = new Map(prev);
        if (!result.data) {
          return prev;
        }
        for (const rowObj of result.data) {
          const row = rowObj;
          const existing = newMap.get(row.id);
          // NOTE: Keep this logic in sync with ...experiments/[experiment]/(queries)/use-summary-realtime.ts
          // update if the incoming row has a higher _xact_id or a different number of comments because
          // comments are appended to the row in the api and don't affect the _xact_id
          if (
            existing &&
            (existing.pending_xact_id || // Skip if we have a pending update
              (existing.xact_id >= row._xact_id &&
                Array.isArray(existing.data.comments) &&
                Array.isArray(row.comments) &&
                existing.data.comments.length === row.comments.length))
          ) {
            continue;
          }

          newMap.set(row.id, {
            root_span_id: row.root_span_id,
            xact_id: row._xact_id,
            data: row,
            schema: result.schema,
          });
        }
        return newMap;
      });

      // If more IDs were added during processing, trigger another refetch
      if (idsToRefetch.current.size > 0) {
        throttledRefetchIds();
      }
    })().catch(console.error);
  });
  const throttledRefetchIds = useMemo(
    () => throttle(1000, refetchObjectIds),
    [refetchObjectIds],
  );

  const onEvent = useCallback(
    (event: unknown) => {
      const row = rowWithIdsSchema.safeParse(event);
      if (row.success) {
        if (row.data._object_delete) {
          // skip deleted rows
          setUpdatedIds((prev) => {
            const newMap = new Map(prev);
            const existing = newMap.get(row.data.id);
            if (existing && existing.xact_id >= row.data._xact_id) {
              return prev;
            }

            newMap.set(row.data.id, {
              root_span_id: row.data.root_span_id,
              xact_id: row.data._xact_id,
              data: row.data,
              schema: undefined,
            });
            return newMap;
          });
        } else {
          if (
            row.data._pagination_key &&
            (maxPaginationKey.current === null ||
              row.data._pagination_key > maxPaginationKey.current)
          ) {
            setNewRowIds((prev) => {
              const newSet = new Set(prev);
              if (
                row.data.span_id === row.data.root_span_id &&
                !currentlyVisibleRootSpanIds.current.has(
                  row.data.root_span_id,
                ) &&
                !currentVisibleIds.current.has(row.data.id)
              ) {
                newSet.add(row.data.id);
              }
              return difference(newSet, currentVisibleIds.current);
            });
          }

          if (
            (currentlyVisibleRootSpanIds.current.has(row.data.root_span_id) ||
              currentVisibleIds.current.has(row.data.id)) &&
            !tableCoordinator.isLocked(row.data.id)
          ) {
            idsToRefetch.current.add(row.data.id);
          }
          throttledRefetchIds();
        }
      }
    },
    [throttledRefetchIds, setNewRowIds, tableCoordinator],
  );
  useRealtimeChannel({
    spec: realtimeSpec,
    onEvent,
  });

  const onAuditLogEvent = useCallback(
    (event: unknown) => {
      const row = auditLogBaseEventSchema.safeParse(event);
      if (row.success && row.data.comment?.text) {
        if (currentVisibleIds.current.has(row.data.origin.id)) {
          idsToRefetch.current.add(row.data.origin.id);
        }
        throttledRefetchIds();
      }
    },
    [throttledRefetchIds],
  );
  useRealtimeChannel({
    spec: auditLogRealtimeSpec,
    onEvent: onAuditLogEvent,
  });

  const schemaFields = useMemo(() => {
    if (mergedSchema) {
      const fullArrowSchema = parseBtqlSchema(null /*ignored*/, mergedSchema);

      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const projectedFields = projectedPaths
        .map((field) => fullArrowSchema.fields.find((f) => f.name === field))
        .filter(Boolean) as ArrowField[];

      return projectedFields;
    }
  }, [mergedSchema, projectedPaths]);

  const [concatenatedTableQueryData, setConcatenatedTableQueryData] = useState<
    TsTable[] | null
  >(null);

  const suggestedFields = useInferCustomColumnPaths({
    objectType,
    objectId,
    rowId: concatenatedTableQueryData?.[0]?.id ?? null,
  });

  useEffect(() => {
    setConcatenatedTableQueryData((prev) => {
      const tables = allTableQueryData
        .map((result) => result?.data)
        .filter(Boolean);

      if (tables.length === 0 || tableQueryFields.size === 0) {
        return prev;
      }

      const seenIds = new Set<string>();

      // Concatenate multiple Arrow tables together
      let concatenatedTable: TsTable[] | null = null;
      for (let i = 0; i < tables.length; i++) {
        const table = tables[i];
        if (!table) {
          continue;
        }

        const projectedRows: TsTable[] = [];
        for (let i = 0; i < table.length; i++) {
          let element = table[i];

          // There are cases where duplicates will show up, and if so, we pretty much always want to prefer
          // the most recent version of the row. We could try to solve all such cases (there are some tricky cases),
          // or just accept that they will happen and skip them.
          if (seenIds.has(element.id)) {
            continue;
          }
          seenIds.add(element.id);

          const updated = updatedIds.get(table[i].id);
          if (updated) {
            if (updated.data._object_delete) {
              // skip deleted rows
              continue;
            }

            element = updated.data;
          }

          projectedRows.push(
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            Object.fromEntries(
              projectedPaths.map((field) => [field, element[field]]),
            ) as TsTable,
          );
        }

        if (!concatenatedTable) {
          concatenatedTable = projectedRows;
        } else {
          concatenatedTable.push(...projectedRows);
        }
      }
      return concatenatedTable;
    });
  }, [allTableQueryData, projectedPaths, tableQueryFields.size, updatedIds]);

  const lastIsEmpty = useMemo(() => {
    return !isPending && allTableQueryData?.some((r) => r.data.length === 0);
  }, [allTableQueryData, isPending]);

  const numTraces = useMemo(() => {
    return concatenatedTableQueryData?.length ?? undefined;
  }, [concatenatedTableQueryData]);

  const numLoadedPages = useMemo(() => {
    return allTableQueryData.reduce((acc, result) => {
      if (isEmpty(result?.data)) {
        return acc;
      }
      return acc + 1;
    }, 0);
  }, [allTableQueryData]);

  const {
    cursor: lastRowSearchCursor,
    minPaginationKey: lastRowSearchMinPaginationKey,
    lastPageValue: lastRowSearchLastPageValue,
  } = useMemo(() => {
    const sort = viewProps.search.sort?.[0];
    const lastTableData = allTableQueryData[allTableQueryData.length - 1];
    const lastRow =
      lastTableData?.data && lastTableData.data[lastTableData.data.length - 1];
    const minPaginationKey = lastRow?._pagination_key ?? undefined;
    const lastPageValue =
      (sort?.spec &&
        lastRow &&
        getObjValueByPath(lastRow, sort.spec.path.path)) ??
      undefined;
    const parsed = literalValueSchema.safeParse(lastPageValue);
    return {
      cursor: sort ? undefined : lastTableData?.cursor,
      minPaginationKey,
      lastPageValue: parsed.success ? parsed.data : undefined,
    };
  }, [allTableQueryData, viewProps.search.sort]);

  const scoreFields = useMemo(() => {
    const scoreFields = new Set<string>();
    for (const property of Object.keys(
      mergedSchema?.items.properties?.scores?.properties ?? {},
    )) {
      scoreFields.add(property);
    }
    return Array.from(scoreFields);
  }, [mergedSchema]);

  useEffect(() => {
    // We know that if there are any refreshes, then they are at the beginning of
    // "pending" and in reverse order.
    for (let i = 0; i < refreshes.length; i++) {
      if (isQueryError[i]) {
        refreshes[refreshes.length - 1 - i].onError(isQueryError[i]);
      } else if (!pending[i]) {
        refreshes[refreshes.length - 1 - i].onLoaded();
      }
    }
  }, [pending, refreshes, isQueryError]);

  const refreshMutex = useRef<Mutex>(new Mutex());
  const onRefreshLiveRows = useMemo(() => {
    return async () =>
      refreshMutex.current.runExclusive(async () => {
        const newRowIdsSnapshot = new Set(newRowIds.current);
        await new Promise((resolve, reject) => {
          setRefreshes((prev) => {
            // Do not insert duplicate pages
            if (
              prev.find(
                (r) => r.paginationKeyFloor === maxPaginationKey.current,
              )
            ) {
              // This likely means that we tried refreshing the data before any rows were
              // available, so instead just refresh the most recent page.
              queryClient.invalidateQueries({
                queryKey: ["summaryPaginatedObjectViewer"],
                predicate: (query) =>
                  // This [8] corresponds to the s.floor argument in the `queryKey`
                  // defined above in `useQueries`.
                  query.queryKey[8] === maxPaginationKey.current,
              });
              resolve(null);
              return prev;
            }

            let resolved = false;
            return [
              ...prev,
              {
                paginationKeyFloor: maxPaginationKey.current,
                onLoaded: () => {
                  if (resolved) {
                    return;
                  }
                  resolved = true;
                  setNewRowIds(
                    new Set(difference(newRowIds.current, newRowIdsSnapshot)),
                  );
                  resolve(null);
                },
                onError: (error) => {
                  if (resolved) {
                    return;
                  }
                  resolved = true;
                  reject(error);
                },
              },
            ];
          });
        });
      });
  }, [setNewRowIds, queryClient]);

  // END BRAINSTORE BASED QUERY FETCHER

  useScoreMetricsTopLevelFields({
    scoreFields,
    setTopLevelFields,
    include: isTraceView,
  });

  const isScanSummary = useMemo(
    () => ["experiment", "project_logs"].includes(objectType),
    [objectType],
  );

  const loadingStatus = useMemo((): LoadingStatusType => {
    // Don't make any updates until all of our queries have returned results
    // corresponding to the latest state of all the searches.
    if (numLoadedPages < pages.length || isPending) {
      return "pending";
    }
    if (lastIsEmpty) {
      return "reached_end";
    }

    if (numTraces !== undefined && numTraces < desiredNumTraces) {
      return "desired_num_traces_unmet";
    }
    return "done_loading";
  }, [
    numLoadedPages,
    pages.length,
    isPending,
    lastIsEmpty,
    numTraces,
    desiredNumTraces,
  ]);

  const initialQueryLoading = useMemo(
    () => isPending && !allTableQueryData.length,
    [isPending, allTableQueryData],
  );

  useEffect(() => {
    if (["desired_num_traces_unmet"].includes(loadingStatus)) {
      setPages((pages) => {
        const newPage = {
          cursor: lastRowSearchCursor,
          min_pagination_key: lastRowSearchMinPaginationKey,
          last_page_value: lastRowSearchLastPageValue,
        };
        if (
          !pages.length ||
          jsonSerializableDeepEqual(pages[pages.length - 1], newPage)
        ) {
          return pages;
        }
        return [
          ...pages,
          {
            cursor: lastRowSearchCursor,
            min_pagination_key: lastRowSearchMinPaginationKey,
            last_page_value: lastRowSearchLastPageValue,
          },
        ];
      });
    }
  }, [
    lastRowSearchCursor,
    lastRowSearchMinPaginationKey,
    lastRowSearchLastPageValue,
    loadingStatus,
  ]);

  useBtqlObjectIdResolver({ objectType, objectId });

  const baseDml = useMutableObject({
    objectType: objectType,
    setSavingState,
    objectId: objectId ?? undefined,
  });

  // Handles optimistic updates for the table data.
  const dml = useMemo(
    () => ({
      ...baseDml,
      // The type of these rows is intentionally not specified to allow flexibility in handling
      // various data structures. This approach simplifies the implementation and avoids
      // unnecessary constraints on the data format.
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      upsert: async (rows: any[], opts?: any) => {
        const rowIds = rows.map((r) => r.id).filter(Boolean);

        // Lock the specific rows we're updating
        tableCoordinator.lockRows(rowIds);

        // Generate a unique batch ID for this entire update operation
        const batchId = `batch_${Date.now()}_${Math.random()}`;

        // Track which rows belong to this batch operation
        const batchRowIds = new Set(rowIds);

        try {
          const result = await baseDml.upsert(rows, {
            ...opts,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onOptimisticDataUpdate: (updatedRows: any[]) => {
              // Apply optimistic updates for this batch
              setUpdatedIds((prev) => {
                const newMap = new Map(prev);
                for (const row of updatedRows) {
                  if (row.id && batchRowIds.has(row.id)) {
                    const existing = newMap.get(row.id);
                    // Only update if we don't have a newer pending update or this is our batch
                    if (
                      !existing ||
                      !existing.pending_xact_id ||
                      existing.pending_xact_id.startsWith(batchId) ||
                      existing.pending_xact_id <= batchId
                    ) {
                      newMap.set(row.id, {
                        root_span_id: row.root_span_id,
                        xact_id: row._xact_id || "0", // Temporary xact_id
                        pending_xact_id: batchId,
                        data: row,
                        schema: undefined,
                      });
                    }
                  }
                }
                return newMap;
              });

              // Call the original callback if provided
              opts?.onOptimisticDataUpdate?.(updatedRows);
            },
          });

          // For batched operations, result might be the highest xact_id across all batches
          // Update all rows that were part of this batch operation
          if (result && rowIds.length > 0) {
            setUpdatedIds((prev) => {
              const newMap = new Map(prev);
              for (const rowId of rowIds) {
                const existing = newMap.get(rowId);
                // Only update if this row was part of our batch operation
                if (existing && existing.pending_xact_id === batchId) {
                  newMap.set(rowId, {
                    ...existing,
                    xact_id: result, // Use the highest xact_id returned
                    pending_xact_id: undefined, // Clear pending state
                  });
                }
              }
              return newMap;
            });
          }

          return result;
        } finally {
          // Unlock the rows
          tableCoordinator.unlockRows(rowIds);
        }
      },
    }),
    [baseDml, tableCoordinator],
  );

  const updateRow = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    async (row: any, path: string[], newValue: any) =>
      await dml.update([row], [{ path, newValue }]),
    [dml],
  );
  const batchUpdateRow = useCallback(
    async (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      row: any,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      updates: { path: string[]; newValue: any }[],
    ) => await dml.update([row], updates),
    [dml],
  );

  const { modelInserts, allAvailableModelCosts } = useAvailableModels({
    orgName: org.name,
  });
  const { tableName: modelSpecTableName } = useTempDuckDataTable({
    tableNameHashKey: objectId ? `model_specs_${objectType}_${objectId}` : null,
    tableDefinition: modelTableDefinition,
    schema: modelArrowSchema,
    data: modelInserts,
    insertionCategory: "model_costs",
  });

  const modelSpecScan = useMemo(() => {
    return (
      modelSpecTableName && `(SELECT * FROM ${doubleQuote(modelSpecTableName)})`
    );
  }, [modelSpecTableName]);

  const expandedRowParams: ExpandedRowParams = useMemo(
    () => ({
      primaryScan: null,
      primaryScanReady: [],
      primaryDynamicObjectId: objectId,
      comparisonScan: null,
      comparisonScanReady: [],
      auditLogScan: null,
      auditLogScanReady: [],
      customColumnsParams: {
        customColumns: customColumnDefinitions,
      },
      isFastSummaryEnabled: fastExperimentSummary,
      modelSpecScan,
      allAvailableModelCosts,
    }),
    [
      objectId,
      customColumnDefinitions,
      fastExperimentSummary,
      modelSpecScan,
      allAvailableModelCosts,
    ],
  );

  const { tableGrouping, groupAggregationTypes, setGroupAggregationType } =
    useTableGroupingControl({
      baseQuery: null /* disable grouping */,
      signals: [],
      tableIdentifier: objectType + "-" + objectId,
      customFieldNames: [],
      viewProps,
    });

  const onTagClick = useMemo(
    () => setTagSearchFn(clauseChecker, viewProps.setSearch),
    [clauseChecker, viewProps.setSearch],
  );

  const tagsFormatter = useTagsFormatter({
    tagConfig: projectConfig.tags,
    onTagClick,
  });

  const onAssignmentClick = useMemo(
    () => setAssignmentsSearchFn(clauseChecker, viewProps.setSearch),
    [clauseChecker, viewProps.setSearch],
  );

  const assignmentsFormatter = useAssignmentsFormatter({
    orgUsers,
    onAssignmentClick,
  });

  const tableFormatters = useMemo(() => {
    const isGrouping = tableGrouping !== GROUP_BY_NONE_VALUE;
    const groupAggregationProps = {
      groupAggregationTypes,
      setGroupAggregationType,
      isGrouping,
    };
    const pinnedColumnIndex = [
      [
        [{}, {}],
        [
          {
            input: 1,
          },
          {
            input: 2,
          },
        ],
      ],
      [
        [{}, {}],
        [
          {
            span_type_info: 1,
          },
          {
            span_type_info: 2,
          },
        ],
      ],
      // scan summary includes span_type_info
    ][Number(!!isScanSummary)][Number(!!isGrouping)][
      Number(!!enableStarColumn)
    ];
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- copy from other places for now
    const formatters = makeFormatterMap({
      span_type_info: {
        ...GroupKeyFormatterWithCell({
          Formatter: SpanTypeInfoFormatter,
          groupKey: "span_type_info",
        }),
        pinnedColumnIndex: pinnedColumnIndex.span_type_info,
      },
      input: isScanSummary
        ? {}
        : {
            ...GroupKeyFormatterWithCell({
              Formatter: InputFormatter,
              groupKey: "input",
            }),
            pinnedColumnIndex: pinnedColumnIndex.input,
          },
      scores: PercentWithAggregationFormatter(groupAggregationProps),
      ...Object.fromEntries(
        ComputedDurationMetricFields.map((f) => [
          f,
          DurationWithAggregationFormatter(groupAggregationProps),
        ]),
      ),
      ...Object.fromEntries(
        projectConfig.metricDefinitions.map((m) => [
          m.field_name,
          MetricAggregationFormatter({
            ...groupAggregationProps,
            metricDefinition: m,
          }),
        ]),
      ),
      tags: {
        cell: tagsFormatter,
      },
      start: {
        cell: StartEndFormatter,
      },
      end: {
        cell: StartEndFormatter,
      },
      error: ErrorCellWithFormatter,
      ...Object.fromEntries(
        (customColumnDefinitions || []).map(({ name }) => [
          name,
          { headerLabel: name, headerIcon: CurlyBraces },
        ]),
      ),
      comments: {
        cell: CommentsFormatterFactory(orgUsers),
      },
      [BT_ASSIGNMENTS]: {
        cell: assignmentsFormatter,
        headerLabel: "Assigned to",
      },
    } as FormatterMap<TsTable, TsValue>);

    return formatters;
  }, [
    tableGrouping,
    groupAggregationTypes,
    setGroupAggregationType,
    isScanSummary,
    enableStarColumn,
    projectConfig.metricDefinitions,
    tagsFormatter,
    customColumnDefinitions,
    orgUsers,
    assignmentsFormatter,
  ]);

  const tableQueryObject = useMemo(() => {
    return {
      type: "tableData" as const,
      data: concatenatedTableQueryData ?? null,
      fields: schemaFields,
      formatters: tableFormatters,
      queryErrors: isQueryError,
    };
  }, [concatenatedTableQueryData, tableFormatters, schemaFields, isQueryError]);

  const refetchDataQueryFn: (rowIds?: string[]) => ParsedQuery | undefined =
    useCallback(
      (rowIds?: string[]) => {
        return objectId
          ? {
              filter: builder.or(
                ...(rowIds ?? []).map(
                  (id): ParsedExpr => ({
                    op: "eq" as const,
                    left: { btql: "id" },
                    right: { op: "literal", value: id },
                  }),
                ),
              ),
              from: builder.from(objectType, [objectId], shape),
              select: [{ op: "star" }],
              sort: [{ expr: { btql: "_pagination_key" }, dir: "desc" }],
              ...(shape === "summary" ? { preview_length: -1 } : {}),
              custom_columns: BUILT_IN_CUSTOM_COLUMNS[objectType]?.map(
                ({ name, expr }) => ({
                  alias: name,
                  expr: { btql: expr },
                }),
              ),
            }
          : undefined;
      },
      [objectId, builder, objectType, shape],
    );

  const selectionProps = useTableSelection(refetchDataQueryFn);

  const traceViewParams = useMemo(
    (): TraceViewParams => ({
      title: objectType,
      objectType: objectType,
      objectId: objectId ?? undefined,
      objectName: objectName ?? "",
      expandedRowParams,
      editableFields: ["expected"],
      updateRow,
      batchUpdateRow,
      commentFn: dml.commentOn,
      deleteCommentFn: dml.deleteComment,
      roster: [],
      ...traceViewParamArgs,
    }),
    [
      objectType,
      objectId,
      objectName,
      expandedRowParams,
      updateRow,
      batchUpdateRow,
      dml.commentOn,
      dml.deleteComment,
      traceViewParamArgs,
    ],
  );

  return useMemo(
    () => ({
      objectType,
      objectId,
      objectName,
      scoreFields,
      pageSize,
      clauseChecker,
      viewProps,
      viewParams,
      pageIdentifier,
      search,
      searchDebounced,
      hideMatchQueries: brainstore,
      setSearch: viewProps.setSearch,
      hasTags: true,
      selectionProps,
      dml,
      loadingStatus,
      initialQueryLoading,
      urlRowSearches: [],
      rowSchema: null,
      rowScanNumTraces: numTraces,
      rowChannel: null,
      projectedPaths,
      tableQuery: tableQueryObject,
      traceViewParams,
      setDesiredNumTraces,
      rowDataError: undefined,
      selectedBucket,
      customColumns: {
        options: {
          input: suggestedFields?.input ?? EMPTY_ARRAY,
          output: suggestedFields?.output ?? EMPTY_ARRAY,
          expected: suggestedFields?.expected ?? EMPTY_ARRAY,
          metadata: suggestedFields?.metadata ?? EMPTY_ARRAY,
        },
        isEnabled: customColumnsEnabled,
        columns: customColumnDefinitions,
        createColumn: createCustomColumn,
        deleteColumn: deleteCustomColumn,
        updateColumn: updateCustomColumn,
        loading: false,
        error: undefined,
      },
      filters,
      refetchDataQueryFn,
      newRows: numNewRowIds,
      onRefreshLiveRows,
      realtimeState,
    }),
    [
      objectType,
      objectId,
      objectName,
      scoreFields,
      pageSize,
      clauseChecker,
      viewProps,
      viewParams,
      pageIdentifier,
      search,
      searchDebounced,
      brainstore,
      selectionProps,
      dml,
      loadingStatus,
      initialQueryLoading,
      numTraces,
      projectedPaths,
      tableQueryObject,
      traceViewParams,
      selectedBucket,
      suggestedFields?.input,
      suggestedFields?.output,
      suggestedFields?.expected,
      suggestedFields?.metadata,
      customColumnsEnabled,
      customColumnDefinitions,
      createCustomColumn,
      deleteCustomColumn,
      updateCustomColumn,
      filters,
      refetchDataQueryFn,
      numNewRowIds,
      onRefreshLiveRows,
      realtimeState,
    ],
  );
}

function strNullMax(
  a: string | null | undefined,
  b: string | null | undefined,
): string | null {
  a = a ?? null;
  b = b ?? null;
  return a !== null && b !== null ? strMax(a, b) : (a ?? b ?? null);
}

function difference<T>(a: Set<T>, b: Set<T>): Set<T> {
  if (typeof a.difference === "function") {
    return a.difference(b);
  }

  // Fallback for older browsers
  const result = new Set(a);
  for (const elem of b) {
    result.delete(elem);
  }
  return result;
}

function formatFiltersWithSort({
  sort,
  filters,
  pageParams,
  builder,
}: {
  sort?: Clause<"sort">;
  filters: {
    sql: Filter[];
    btql: ParsedExpr[];
  };
  pageParams: PageParams;
  builder: BtqlQueryBuilder;
}) {
  const sortSpec = sort?.spec;
  const { min_pagination_key, last_page_value } = pageParams;

  return sortSpec
    ? {
        sql: filters.sql,
        btql: filters.btql.concat(
          sortSpec.path.path && last_page_value !== undefined
            ? (() => {
                const cmpExpr = {
                  op: sortSpec.col?.desc ? "lt" : "gt",
                  left: {
                    op: "ident",
                    name: sortSpec.path.path,
                  },
                  right: {
                    op: "literal",
                    value: last_page_value,
                  },
                } as const;

                if (min_pagination_key) {
                  return builder.or(
                    {
                      op: "and" as const,
                      left: {
                        op: "lt",
                        left: { btql: "_pagination_key" },
                        right: {
                          op: "literal",
                          value: min_pagination_key,
                        },
                      },
                      right: {
                        op: "eq" as const,
                        left: {
                          op: "ident",
                          name: sortSpec.path.path,
                        },
                        right: {
                          op: "literal",
                          value: last_page_value,
                        },
                      },
                    },
                    cmpExpr,
                  );
                }

                return cmpExpr;
              })()
            : [],
        ),
      }
    : filters;
}
