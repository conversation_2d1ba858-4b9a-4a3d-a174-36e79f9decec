import { type Span } from "@braintrust/local";
import { parsePromptFromSpan } from "@braintrust/local";
import { fetchBtql } from "#/utils/btql/btql";
import * as Query from "#/utils/btql/query-builder";
import { promptSchema } from "#/ui/prompts/schema";
import { type FetchCachedBtSessionTokenFn } from "#/utils/auth/session-token";
import { MessagesSquare, Percent } from "lucide-react";
import { type FormatOption } from "./dataset-action-button";
import { BlueLink } from "#/ui/link";
import { type BTQLFeatureFlags } from "#/lib/feature-flag-config";

export const createScorerFormatOption = (isMultiple = false): FormatOption => ({
  id: "scorer",
  label: `Add scorer-calibration formatted ${isMultiple ? "spans" : "span"}`,
  icon: Percent,
  hotkey: "S",
  formatter: scorerFormatter,
  tooltip: (
    <p className="whitespace-pre-wrap text-xs text-primary-500">
      Nest {isMultiple ? "each span" : "this span"}&apos;s{" "}
      <span className="font-mono text-primary-700">input</span>,{" "}
      <span className="font-mono text-primary-700">output</span>,{" "}
      <span className="font-mono text-primary-700">expected</span>, and{" "}
      <span className="font-mono text-primary-700">metadata</span> into the
      dataset row&apos;s{" "}
      <span className="font-mono text-primary-700">input</span>. This is useful
      when creating a dataset to be used for scorer evaluation.{" "}
      <BlueLink
        href="/docs/guides/playground#for-scorers-as-task"
        target="_blank"
      >
        Learn more
      </BlueLink>
    </p>
  ),
});

export const createPlaygroundFormatOption = ({
  spanPromptMeta,
  btqlFlags,
  apiUrl,
  getOrRefreshToken,
  abortController,
  shouldShow = true,
}: {
  spanPromptMeta?: {
    id?: string | null;
    project_id: string | null;
    variables?: Record<string, unknown>;
  } | null;
  btqlFlags: BTQLFeatureFlags;
  apiUrl: string;
  getOrRefreshToken: FetchCachedBtSessionTokenFn;
  abortController: React.RefObject<AbortController>;
  shouldShow?: boolean;
}): FormatOption => ({
  id: "playground",
  label: "Add playground prompt formatted span",
  icon: MessagesSquare,
  hotkey: "P",
  shouldShow,
  formatter: createPlaygroundFormatter({
    spanPromptMeta,
    btqlFlags,
    apiUrl,
    getOrRefreshToken,
    abortController,
  }),
  tooltip: (
    <p className="whitespace-pre-wrap text-xs text-primary-500">
      Move{" "}
      <span className="font-mono text-primary-700">
        metadata.prompt.variables
      </span>{" "}
      into <span className="font-mono text-primary-700">input</span> and the
      extra multi-turn messages into{" "}
      <span className="font-mono text-primary-700">input.appendedMessages</span>
      .
    </p>
  ),
});

export const createPlaygroundFormatter = ({
  spanPromptMeta,
  btqlFlags,
  apiUrl,
  getOrRefreshToken,
  abortController,
}: {
  spanPromptMeta?: {
    id?: string | null;
    project_id: string | null;
    variables?: Record<string, unknown>;
  } | null;
  btqlFlags: BTQLFeatureFlags;
  apiUrl: string;
  getOrRefreshToken: FetchCachedBtSessionTokenFn;
  abortController: React.RefObject<AbortController>;
}) => {
  return async (spans: Span[]): Promise<Span[]> => {
    const formattedSpans = await Promise.all(
      spans.map(async (span) => {
        const parsedPrompt = parsePromptFromSpan(span) ?? undefined;
        const isLLMSpan = !!parsedPrompt && parsedPrompt.success;

        if (!isLLMSpan || !spanPromptMeta?.id || !spanPromptMeta.project_id) {
          return span;
        }

        try {
          const result = await fetchBtql({
            args: {
              query: {
                from: Query.from("project_prompts", [
                  spanPromptMeta.project_id,
                ]),
                filter: {
                  op: "eq",
                  left: { op: "ident", name: ["id"] },
                  right: { op: "literal", value: spanPromptMeta.id },
                },
                select: [{ op: "star" }],
                limit: 1,
              },
              brainstoreRealtime: true,
              disableLimit: false,
            },
            btqlFlags,
            apiUrl,
            getOrRefreshToken,
            signal: abortController.current.signal,
          });

          const maybePrompt = result.data[0];
          const prompt = promptSchema.safeParse(maybePrompt);
          if (!prompt.success) {
            return span;
          }

          const savedPromptMessages = prompt.data.prompt_data?.prompt;
          const parsedPromptMessages = parsedPrompt.data.prompt;
          const isChat =
            savedPromptMessages?.type === "chat" &&
            parsedPromptMessages?.type === "chat";
          const appendedMessages = isChat
            ? parsedPromptMessages?.messages?.slice(
                savedPromptMessages.messages.length,
              )
            : [];

          const formattedSpan: Span = {
            ...span,
            data: {
              ...span.data,
              input: {
                ...(spanPromptMeta.variables ?? {}),
                ...(appendedMessages.length > 0 ? { appendedMessages } : {}),
              },
            },
          };
          return formattedSpan;
        } catch (e) {
          return span;
        }
      }),
    );

    return formattedSpans;
  };
};

export const scorerFormatter = (spans: Span[]): Span[] => {
  return spans.map((span) => {
    const newSpan: Span = {
      ...span,
      data: {
        ...span.data,
      },
    };

    if (span.data.input) {
      newSpan.data.input = {
        input: span.data.input,
      };
    }

    if (span.data.output) {
      newSpan.data.input ??= {};
      newSpan.data.input.output = span.data.output;
      delete newSpan.data.output;
    }

    if (span.data.expected) {
      newSpan.data.input ??= {};
      newSpan.data.input.expected = span.data.expected;
      delete newSpan.data.expected;
    }

    if (span.data.metadata) {
      newSpan.data.input ??= {};
      newSpan.data.input.metadata = span.data.metadata;
      delete newSpan.data.metadata;
    }

    return newSpan;
  });
};
