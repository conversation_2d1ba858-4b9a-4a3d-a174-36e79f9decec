import { Database, Ellipsis } from "lucide-react";
import { useState, useRef } from "react";
import { type Span } from "@braintrust/local";
import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "#/ui/dropdown-menu";
import { BasicTooltip } from "#/ui/tooltip";
import { renderHotkey } from "#/utils/hotkeys";
import { DatasetDropdown } from "#/ui/dataset-dropdown";
import { useHotkeys } from "react-hotkeys-hook";
import { type ProjectContextDataset } from "#/app/app/[org]/p/[project]/project-actions";

export type SpanFormatter = (spans: Span[]) => Promise<Span[]> | Span[];

export type FormatOption = {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  tooltip?: React.ReactNode;
  formatter: SpanFormatter;
  hotkey?: string;
  shouldShow?: boolean;
};

type DatasetActionButtonProps = {
  orgDatasets: ProjectContextDataset[];
  getSpans: () => Span[] | Promise<Span[]>;
  performAddRowsToDataset: (params: {
    datasetName: string;
    datasetId: string;
    spans: Span[];
    selectedProjectId: string;
    selectedProjectName: string;
  }) => void;
  openCreateDatasetDialog: (
    name: string,
    getRows?: () => Promise<Span[]>,
  ) => void;
  buttonText: string;
  formatOptions?: FormatOption[];
  scrollTargetRef?: React.RefObject<HTMLElement | null>;
  enableHotkeys?: boolean;
};

export const DatasetActionButton = ({
  orgDatasets,
  getSpans,
  performAddRowsToDataset,
  openCreateDatasetDialog,
  buttonText,
  formatOptions = [],
  scrollTargetRef,
  enableHotkeys = false,
}: DatasetActionButtonProps) => {
  const [datasetDropdownOpen, setDatasetDropdownOpen] = useState(false);
  const [selectedFormatId, setSelectedFormatId] = useState<string>("raw");

  const scrollToTarget = () => {
    scrollTargetRef?.current?.scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  };

  const openDropdownWithFormat = (formatId: string) => {
    setDatasetDropdownOpen(true);
    setSelectedFormatId(formatId);
    scrollToTarget();
  };

  const hotkeys = ["D"];
  const hotkeyMap: Record<string, string> = { D: "raw" };
  formatOptions.forEach((option) => {
    if (option.hotkey) {
      hotkeys.push(option.hotkey);
      hotkeyMap[option.hotkey] = option.id;
    }
  });
  useHotkeys(
    hotkeys,
    (_, { keys }) => {
      const pressedKey = keys?.join("") || "";
      const formatId = hotkeyMap[pressedKey];
      if (formatId) {
        openDropdownWithFormat(formatId);
      }
    },
    {
      preventDefault: true,
      enabled: enableHotkeys,
    },
  );

  const getFormattedSpans = async () => {
    const spans = await getSpans();
    const formatOption = formatOptions.find(
      (opt) => opt.id === selectedFormatId,
    );

    if (!formatOption) {
      return spans;
    }

    return formatOption.formatter(spans);
  };

  const spanRef = useRef<HTMLSpanElement>(null);
  const hasFormatOptions = formatOptions.length > 0;

  const triggerButton = (
    <Button
      size="xs"
      className={
        hasFormatOptions ? "truncate rounded-r-none border-r-0" : "truncate"
      }
      Icon={Database}
      onClick={() => openDropdownWithFormat("raw")}
    >
      <span className="flex-1 truncate" ref={spanRef}>
        {buttonText}
      </span>
    </Button>
  );

  return (
    <DatasetDropdown
      datasets={orgDatasets}
      onSelectDataset={async (dataset) => {
        const formattedSpans = await getFormattedSpans();
        performAddRowsToDataset({
          datasetName: dataset.name,
          datasetId: dataset.id,
          spans: formattedSpans,
          selectedProjectId: dataset.project_id,
          selectedProjectName: dataset.project_name,
        });
      }}
      onCreateNewDataset={(name: string) =>
        openCreateDatasetDialog(
          name,
          selectedFormatId === "raw" ? undefined : getFormattedSpans,
        )
      }
      open={datasetDropdownOpen}
      setOpen={(newOpen) => {
        if (!datasetDropdownOpen) {
          return;
        }
        setDatasetDropdownOpen(newOpen);
      }}
    >
      <div>
        {enableHotkeys ? (
          <BasicTooltip
            side="left"
            tooltipContent={
              <div className="flex items-center gap-2">
                <p className="whitespace-pre-wrap text-xs">{buttonText}</p>
                <span className="ml-2.5 inline-block opacity-50">
                  {renderHotkey("D")}
                </span>
              </div>
            }
          >
            {triggerButton}
          </BasicTooltip>
        ) : (
          triggerButton
        )}

        {hasFormatOptions && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size="xs"
                className="truncate rounded-l-none"
                Icon={Ellipsis}
              />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {formatOptions.map((option) => {
                if (option.shouldShow === false) {
                  return null;
                }

                const menuItem = (
                  <DropdownMenuItem
                    key={option.id}
                    onClick={() => openDropdownWithFormat(option.id)}
                    onBlur={(e) => {
                      if (option.tooltip) {
                        e.stopPropagation();
                        e.preventDefault();
                      }
                    }}
                  >
                    <option.icon className="size-3 text-primary-700" />
                    <span>{option.label}</span>
                    {enableHotkeys && option.hotkey && (
                      <span className="ml-auto inline-block pl-2.5 opacity-50">
                        {renderHotkey(option.hotkey)}
                      </span>
                    )}
                  </DropdownMenuItem>
                );

                if (option.tooltip) {
                  return (
                    <BasicTooltip
                      key={option.id}
                      side="left"
                      sideOffset={10}
                      tooltipContent={option.tooltip}
                      disableHoverableContent={false}
                    >
                      {menuItem}
                    </BasicTooltip>
                  );
                }

                return menuItem;
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </DatasetDropdown>
  );
};
