{"python.formatting.provider": "black", "eslint.workingDirectories": [{"mode": "auto"}], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "notebook.formatOnSave.enabled": true, "notebook.formatOnCellExecution": true, "python.analysis.extraPaths": ["./api"], "python.languageServer": "<PERSON><PERSON><PERSON>", "python.analysis.typeCheckingMode": "standard", "typescript.tsdk": "node_modules/typescript/lib"}