import time
import unittest
from pprint import pprint

import braintrust
import redis
import requests
from braintrust_local.constants import LOCAL_REDIS_HOST, LOCAL_REDIS_PORT
from jsonschema import validate as json_schema_validate

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase, make_v1_url

ROWS = 101
MODELS = ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo"]


class SchemaInferenceTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

    def run_btql(self, query, realtime=False, **kwargs):
        result = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={"query": query, **self.brainstore_query_args(realtime=realtime), **kwargs},
        ).json()
        data = result.get("data")
        schema = result.get("schema")

        json_schema_validate(data, schema)
        return data

    def wait_till_compacted(self, from_clause, target_rows):
        self.catchupBrainstore()
        start = time.time()
        while True:
            ret = self.run_btql(f"measures: count(1) AS c | from: {from_clause}")
            compacted = ret[0]["c"]
            print("Rows compacted after", time.time() - start, compacted)
            if compacted == target_rows:
                break

            if time.time() - start > 300:
                raise unittest.SkipTest("Timeout waiting for rows to be compacted. Skipping test.")
            time.sleep(0.5)

    def test_schema_inference(self):
        logger = braintrust.init_logger("p")

        user_ids = [1, 2, 2, 3, 3, 3, 4, 4, 4, 4]

        for i in range(ROWS):
            with logger.start_span("task") as span:
                span.log(
                    input=f"input {i}",
                    output={f"output": i},
                    metadata={"user_id": user_ids[i % len(user_ids)]},
                    scores={"score0": 0 if i % 3 == 0 else 0.5},
                )

                with span.start_span("subtask") as subtask:
                    subtask.log(
                        input=f"llm bla bla",
                        output={f"output {i}": i},
                        metadata={"model": MODELS[i % len(MODELS)]},
                        metrics={"prompt_tokens": i, "completion_tokens": i},
                    )

        logger.flush()

        # This test needs to be run on compacted data, so query until all 202 spans are compacted
        self.wait_till_compacted(f"project_logs('{logger.project.id}')", ROWS * 2)

        # Verify that metrics and scores over the whole set of spans works
        ret = self.run_btql(f"infer: scores, metrics | from: project_logs('{logger.project.id}')")

        score0 = None
        prompt_tokens = None
        completion_tokens = None
        for row in ret:
            if row["name"] == ["scores", "score0"]:
                score0 = row
            elif row["name"] == ["metrics", "prompt_tokens"]:
                prompt_tokens = row
            elif row["name"] == ["metrics", "completion_tokens"]:
                completion_tokens = row

        self.assertIsNotNone(score0)
        self.assertIsNotNone(prompt_tokens)
        self.assertIsNotNone(completion_tokens)

        self.assertEqual(score0["type"]["type"], "number")
        self.assertEqual(prompt_tokens["type"]["type"], "integer")
        self.assertEqual(completion_tokens["type"]["type"], "integer")

        self.assertEqual(score0["top_values"][0]["value"], 0.5)
        self.assertEqual(score0["top_values"][0]["count"], 67)
        self.assertEqual(score0["top_values"][1]["value"], 0)
        self.assertEqual(score0["top_values"][1]["count"], 34)

        # The non-root outputs have a lot of different field names, so let's make sure it taps out after 100
        ret = self.run_btql(
            f"infer: output | from: project_logs('{logger.project.id}') | limit: 100000",
        )

        output_paths = set()
        for row in ret:
            output_paths.add(".".join(row["name"][1:]))

        self.assertEqual(len(output_paths), 100)

        # Now, run inference on just the root spans, but get input, output, and metadata
        ret = self.run_btql(
            f"infer: input, output, metadata | from: project_logs('{logger.project.id}') | filter: is_root | limit: 100000",
        )

        output_paths = set()
        input = None
        for row in ret:
            self.assertNotEqual(row["name"], ["metadata", "model"])

            if row["name"][0] == "output":
                output_paths.add(".".join(row["name"][1:]))
            elif row["name"] == ["input"]:
                input = row

        output_paths = list(output_paths)
        self.assertEqual(len(output_paths), 1)
        self.assertEqual(output_paths[0], "output")

        self.assertIsNotNone(input)
        input_values = set([x["value"] for x in input["top_values"]])
        self.assertFalse("llm bla bla" in input_values)

    def test_many_metadata_paths(self):
        logger = braintrust.init_logger("p")

        for i in range(ROWS):
            with logger.start_span("task") as span:
                huge_metadata = {}
                for j in range(ROWS):
                    huge_metadata[f"user_id_{i}__{j}"] = (i + 1) * (j + 1)
                span.log(
                    input=f"input {i}",
                    output={f"output": i},
                    metadata=huge_metadata,
                )

                with span.start_span("subtask") as subtask:
                    subtask.log(
                        input=f"llm bla bla",
                        output={f"output {i}": i},
                        metadata={"model": MODELS[i % len(MODELS)]},
                        metrics={"prompt_tokens": i, "completion_tokens": i},
                    )

        logger.flush()

        # This test needs to be run on compacted data, so query until all 202 spans are compacted
        self.wait_till_compacted(f"project_logs('{logger.project.id}')", ROWS * 2)

        # First, verify that only 100 paths show up for metadata in the root
        ret = self.run_btql(
            f"infer: metadata | from: project_logs('{logger.project.id}') | filter: is_root AND input='input 100' | limit: 100000",
        )

        metadata_paths = set()
        for row in ret:
            if row["name"][0] == "metadata":
                metadata_paths.add(".".join(row["name"][1:]))
                self.assertTrue(len(row["top_values"]) > 0)

        self.assertEqual(len(metadata_paths), 100)

        # Now, verify that if we filter to non-roots, we only get one path ("model")
        ret = self.run_btql(
            f"infer: metadata | from: project_logs('{logger.project.id}') | filter: is_root=false | limit: 100000",
        )
        metadata_paths = set()
        for row in ret:
            if row["name"][0] == "metadata":
                metadata_paths.add(".".join(row["name"][1:]))

        self.assertEqual(len(metadata_paths), 1)
        self.assertEqual(list(metadata_paths)[0], "model")

    def test_multi_inference_strategy(self):
        rows = 5

        # Create two experiments, one with 200 different metadata fields, and one with just 1.
        # Run inference across them. One will use columnar inference, and the other will use
        # the inverted index.
        slim_experiment = braintrust.init_experiment("p")
        for i in range(rows):
            slim_experiment.log(input=i, output=i, scores={}, metadata={"field": i})
        slim_experiment.flush()

        wide_experiment = braintrust.init_experiment("p")
        for i in range(rows):
            wide_experiment.log(input=i, output=i, scores={}, metadata={f"field_{j}": j for j in range(200)})
        wide_experiment.flush()

        self.wait_till_compacted(f"experiment('{slim_experiment.id}')", rows)
        self.wait_till_compacted(f"experiment('{wide_experiment.id}')", rows)

        # Run inference across them. One will use columnar inference, and the other will use
        # the inverted index. Try both directions.
        experiment_ids = [slim_experiment.id, wide_experiment.id]
        for i in range(2):
            ret = self.run_btql(
                f"infer: metadata | from: experiment('{experiment_ids[i]}', '{experiment_ids[1 - i]}')",
            )
            self.assertEqual(len(ret), 100)

    def test_multi_type(self):
        # Sanity test that a nested field with multiple types returns just one row with the types unioned together
        experiment = braintrust.init_experiment("p")
        experiment.log(input={"field": 1}, output=1, scores={}, metadata={"field": 1})
        experiment.log(input={"field": "foo"}, output=1, scores={}, metadata={"field": "foo"})
        experiment.flush()
        self.wait_till_compacted(f"experiment('{experiment.id}')", 2)

        for field in ["input", "metadata"]:
            ret = self.run_btql(f"infer: {field} | from: experiment('{experiment.id}')")
            print(ret)
            self.assertEqual(len(ret), 1)
