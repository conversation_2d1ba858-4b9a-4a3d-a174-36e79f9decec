import { Request, Response } from "express";
import { z } from "zod";
import { assertBrainstoreEnabled, runBrainstore } from "./brainstore";
import { authCheckObjectIds } from "./auth";
import { getRequestContext } from "../request_context";
import { AccessDeniedError, wrapZodError } from "../util";
import { globalTimeBasedRetentionStatusSchema } from "@braintrust/local/app-schema";
import { checkTokenAuthorized } from "../token_auth";

const brainstoreTimeBasedRetentionStatusRequest = z.object({
  recency_window_seconds: z.number().int().default(86400), // Defaults to 1 day
});

export async function getTimeBasedRetentionStatusRequest(
  req: Request,
  res: Response,
) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const { me } = await checkTokenAuthorized({
    ctxToken: ctx.token,
    appOrigin: ctx.appOrigin,
    checkSysadmin: true,
  });
  if (!(await me).is_sysadmin) {
    throw new AccessDeniedError({
      permission: "sysadmin",
      objectType: "brainstore",
      objectId: "status",
    });
  }

  const params = wrapZodError(() =>
    brainstoreTimeBasedRetentionStatusRequest.parse(ctx.data),
  );

  res.json(
    await runBrainstore({
      path: "/time_based_retention/status",
      args: params,
      schema: globalTimeBasedRetentionStatusSchema,
    }),
  );
}

const brainstoreTimeBasedRetentionResetStateRequest = z.object({});

export async function resetTimeBasedRetentionStateRequest(
  req: Request,
  res: Response,
) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const { me } = await checkTokenAuthorized({
    ctxToken: ctx.token,
    appOrigin: ctx.appOrigin,
    checkSysadmin: true,
  });
  if (!(await me).is_sysadmin) {
    throw new AccessDeniedError({
      permission: "sysadmin",
      objectType: "brainstore",
      objectId: "reset_state",
    });
  }

  const params = wrapZodError(() =>
    brainstoreTimeBasedRetentionResetStateRequest.parse(ctx.data),
  );

  const resp = await runBrainstore({
    path: "/time_based_retention/reset_state",
    args: params,
    schema: z.object({ success: z.boolean() }),
  });

  res.json(resp);
}

const brainstoreTimeBasedRetentionObjectRequest = z.object({
  dry_run: z.boolean().nullish(),
});

const brainstoreTimeBasedRetentionRequest = z.object({
  object_ids: z.array(z.string()).min(1, "At least one object_id is required"),
  dry_run: z.boolean().nullish(),
});

const brainstoreTimeBasedRetentionResponse = z.object({
  success: z.boolean(),
  num_processed_objects: z.number(),
  num_processed_segments: z.number(),
  planned_num_deleted_wal_entries: z.number(),
  num_deleted_wal_entries: z.number(),
  planned_num_deleted_index_docs: z.number(),
  num_deleted_index_docs: z.number(),
  num_write_locks: z.number(),
  error: z.string().nullish(),
});

export async function timeBasedRetentionObjectRequest(
  req: Request,
  res: Response,
) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreTimeBasedRetentionObjectRequest.parse(ctx.data),
  );
  const objectIds = [req.params.object_id];

  await authCheckObjectIds({
    objectIds,
    ctx,
  });

  res.json(
    await runBrainstore({
      path: "/time_based_retention",
      args: {
        object_ids: objectIds,
        dry_run: params.dry_run,
      },
      schema: brainstoreTimeBasedRetentionResponse,
      isWrite: true,
    }),
  );
}

export async function timeBasedRetentionRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreTimeBasedRetentionRequest.parse(ctx.data),
  );

  await authCheckObjectIds({
    objectIds: params.object_ids,
    ctx,
  });

  res.json(
    await runBrainstore({
      path: "/time_based_retention",
      args: {
        object_ids: params.object_ids,
        dry_run: params.dry_run,
      },
      schema: brainstoreTimeBasedRetentionResponse,
      isWrite: true,
    }),
  );
}
