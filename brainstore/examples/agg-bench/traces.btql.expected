[{"error": null, "query": "-- No match\n/*!result -- No match should be null\n  .[0].s == -1\n*/\nmeasures: COALESCE(sum(is_root), -1) AS s | filter: input='foo' | from: experiment('singleton') traces", "result_rows": [{"s": -1}], "skip": false}, {"error": null, "query": "-- This should return 4 rows\n/*!result -- This should return 4 spans across 2 traces\n  length == 4\n */\nfrom: experiment('singleton') traces | filter: _xact_id=100 OR _xact_id=109 | select: id", "result_rows": [{"id": "220b5a36-22a1-476f-b38d-d1d2471500e7"}, {"id": "280a0291-cb1d-490e-9686-ce62d5ccb7dc"}, {"id": "83d8199a-998d-4824-8c8b-32e4e1ace731"}, {"id": "dde4088d-916c-478d-ae3a-5a8b772875d6"}], "skip": false}, {"error": null, "query": "/*!result -- The count should corroborate that\n  .[0].c == 2\n*/\nfrom: experiment('singleton') spans | filter: _xact_id=100 OR _xact_id=109 | measures: count(1) as c", "result_rows": [{"c": 2}], "skip": false}, {"error": null, "query": "/*!result -- In traces mode, it should be 4 though\n  .[0].c == 4\n*/\nfrom: experiment('singleton') traces | filter: _xact_id=100 OR _xact_id=109 | measures: count(1) as c", "result_rows": [{"c": 4}], "skip": false}, {"error": null, "query": "/*!result -- Empty match\n  .[0].c == 0\n*/\nmeasures: count(1) AS c | from: experiment('singleton') traces | filter: created > NOW()", "result_rows": [{"c": 0}], "skip": false}, {"error": null, "query": "/*!result -- Only match the one span\n  .[0].c == 1\n*/\nmeasures: count(1) AS c | from: experiment('singleton') spans | filter: metadata.model='gpt-4'", "result_rows": [{"c": 1}], "skip": false}, {"error": null, "query": "/*!result -- Match both spans\n  .[0].c == 2\n*/\nmeasures: count(1) AS c | from: experiment('singleton') traces | filter: metadata.model='gpt-4'", "result_rows": [{"c": 2}], "skip": false}, {"error": null, "query": "/*!result -- Only match the one span\n  .[0].c == 1\n*/\nmeasures: count(1) AS c | from: experiment('singleton') spans | filter: metadata.model MATCH 'gpt-4'", "result_rows": [{"c": 1}], "skip": false}, {"error": null, "query": "/*!result -- Match both spans\n  .[0].c == 2\n*/\n/*!execution -- Pushes down columnar fields. If Retrieve full documents is in the tree, something very bad has happened\n\n[.. | objects | select(.name == \"Index search\") | .. | objects | select(.name == \"Retrieve full documents\")] | length == 0\n\n*/\nmeasures: count(1) AS c | from: experiment('singleton') traces | filter: metadata.model MATCH 'gpt-4'", "result_rows": [{"c": 2}], "skip": false}]