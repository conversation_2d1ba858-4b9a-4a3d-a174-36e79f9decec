/*!execution -- Pushes down columnar fields. If Retrieve full documents is in the tree, something very bad has happened

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: * | from: experiment('singleton') summary | comparison_key: input;

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: Levenshtein = 1;

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: scores.Factuality > 0.5;

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: scores.Factuality IS NULL;

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: scores.Factuality IS NOT NULL;

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: scores.Avg > 0.5;


/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: metrics.duration > 1.5;

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | custom_columns: metadata.category as Category
    | filter: Category = "cat";

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | custom_columns: metadata.model as Model
    | filter: Model = "gpt-4o";

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: * | from: experiment('singleton') summary | preview_length: 0;

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: * | from: experiment('singleton') summary | preview_length: 2000;

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
select: * | from: experiment('singleton') summary |  preview_length: -1;

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
from: experiment('singleton') summary | select: id | filter: tags includes ['greeting'];

/*!execution -- Pushes down columnar fields

[.. | objects | select(.name == "Index search") | .. | objects | select(.name == "Retrieve full documents")] | length == 0

*/
from: experiment('singleton') summary | select: id | filter: tags not includes ['greeting'];
