use std::sync::Arc;

use otel_common::{
    collector::Collector,
    opentelemetry::{
        global,
        metrics::{<PERSON><PERSON><PERSON>, <PERSON><PERSON>},
        KeyValue,
    },
};
use storage::{
    compaction_loop::CompactionLoop,
    optimize_tantivy_index::{
        LONG_TERM_OPTIMIZATION_LOOP_CONFIG, SHORT_TERM_OPTIMIZATION_LOOP_CONFIG,
    },
    postgres_global_store::record_pool_metrics,
    postgres_pool::PostgresPool,
    process_wal::SLOW_COMPACTION_THRESHOLD_SECS,
    static_sync_runtime::get_static_runtime_metrics,
};

use crate::base::AppState;
use async_trait::async_trait;
use util::anyhow::Result;

pub async fn run_metrics_loop(
    app_state: Arc<AppState>,
    compaction_loop: CompactionLoop,
    interval_seconds: u64,
) {
    let mut collectors = Vec::new();
    let meter = global::meter("brainstore");

    let global_store_url = &app_state.storage_config.metadata_uri;
    let pool_for_metrics = if !(global_store_url.scheme() == "postgres"
        || global_store_url.scheme() == "postgresql")
    {
        log::warn!(
            "Metrics are not supported for this storage type: {}",
            global_store_url
        );
        None
    } else {
        let pool = Arc::new(
            PostgresPool::new(global_store_url, 1).expect("Failed to connect to global store"),
        );
        collectors.push(Box::new(BackfillMetrics::new(
            &meter,
            pool.clone(),
            compaction_loop.clone(),
        )) as Box<dyn Collector>);
        Some(pool)
    };

    collectors.push(PoolMetricsCollector::new(pool_for_metrics));
    collectors.push(Box::new(StaticRuntimeMetrics::new(&meter)));

    agent::metrics_loop::run_metrics_loop(meter, interval_seconds, collectors).await;
}

// If you change the set of metrics, consider updating the corresponding
// monitoring/alerting dashboards for prod.
struct BackfillMetrics {
    pg_conn: Arc<PostgresPool>,
    compaction_loop: CompactionLoop,
    frontier_sequence_id_lag: Gauge<u64>,
    encountered_sequence_id_lag: Gauge<u64>,
    comments_backfill_sequence_id_lag: Gauge<u64>,
    realtime_encountered_processed_sequence_id_lag: Gauge<u64>,
    realtime_encountered_processed_sequence_id_lag_seconds: Gauge<u64>,

    processing_furthest_behind_delta_seconds: Gauge<u64>,
    processing_num_objects_behind: Gauge<u64>,

    // Of the objects that are at most 1 hour behind, how far behind are the worst?
    // TODO: I'd like to remove the 1h thing, but we have so many far behind objects
    // that it will be difficult at this time.
    compaction_furthest_behind_delta_seconds: Gauge<u64>,
    compaction_num_objects_behind: Gauge<u64>,
    compaction_short_term_loop_objects: Gauge<u64>,
    compaction_long_term_loop_objects: Gauge<u64>,
    compaction_queue_size: Gauge<u64>,
}

impl BackfillMetrics {
    fn new(meter: &Meter, pg_conn: Arc<PostgresPool>, compaction_loop: CompactionLoop) -> Self {
        let frontier_sequence_id_lag = meter
            .u64_gauge("brainstore.backfill.frontier_sequence_id_lag")
            .build();
        let encountered_sequence_id_lag = meter
            .u64_gauge("brainstore.backfill.encountered_sequence_id_lag")
            .build();
        let comments_backfill_sequence_id_lag = meter
            .u64_gauge("brainstore.backfill.comments_backfill_sequence_id_lag")
            .build();
        let realtime_encountered_processed_sequence_id_lag = meter
            .u64_gauge("brainstore.backfill.realtime_encountered_processed_sequence_id_lag")
            .build();
        let realtime_encountered_processed_sequence_id_lag_seconds = meter
            .u64_gauge("brainstore.backfill.realtime_encountered_processed_sequence_id_lag_seconds")
            .build();
        let processing_furthest_behind_delta_seconds = meter
            .u64_gauge("brainstore.backfill.processing_furthest_behind_delta_seconds")
            .build();
        let processing_num_objects_behind = meter
            .u64_gauge("brainstore.backfill.processing_num_objects_behind")
            .build();
        let compaction_furthest_behind_delta_seconds = meter
            .u64_gauge("brainstore.backfill.compaction_furthest_behind_delta_seconds")
            .build();
        let compaction_num_objects_behind = meter
            .u64_gauge("brainstore.backfill.compaction_num_objects_behind")
            .build();
        let compaction_short_term_loop_objects = meter
            .u64_gauge("brainstore.backfill.compaction_short_term_loop_objects")
            .build();
        let compaction_long_term_loop_objects = meter
            .u64_gauge("brainstore.backfill.compaction_long_term_loop_objects")
            .build();
        let compaction_queue_size = meter
            .u64_gauge("brainstore.backfill.compaction_queue_size")
            .build();

        Self {
            pg_conn,
            compaction_loop,
            frontier_sequence_id_lag,
            encountered_sequence_id_lag,
            comments_backfill_sequence_id_lag,
            realtime_encountered_processed_sequence_id_lag,
            realtime_encountered_processed_sequence_id_lag_seconds,
            processing_furthest_behind_delta_seconds,
            processing_num_objects_behind,
            compaction_furthest_behind_delta_seconds,
            compaction_num_objects_behind,
            compaction_short_term_loop_objects,
            compaction_long_term_loop_objects,
            compaction_queue_size,
        }
    }
}

#[async_trait]
impl Collector for BackfillMetrics {
    async fn collect(&mut self) -> Result<(), util::anyhow::Error> {
        let global_store_conn = self.pg_conn.get_client().await?;

        // Keep this query in sync with the "Monitor Backfill Lag" query in
        // https://www.notion.so/braintrustdata/Playbook-for-Monitoring-Hosted-Data-Backend-144f7858028980ea95d0db76ddc56441.
        let results = global_store_conn
            .query(
                r#"
with
logs_stats as (
    select max(sequence_id) logs_max_sequence_id
    from logs
),
logs2_stats as (
    select max(sequence_id) logs2_max_sequence_id
    from logs2
),
comments_stats as (
    select max(sequence_id) comments_max_sequence_id
    from comments
),
frontier_stats as (
    select
        historical_full_backfill_sequence_id as frontier_sequence_id,
        historical_full_backfill_sequence_id_2 as frontier_sequence_id_2,
        comments_backfill_sequence_id
    from brainstore_backfill_global_state
),
max_tracked_object_counters as (
    select
            max(last_encountered_sequence_id) max_last_encountered_sequence_id,
            max(last_encountered_sequence_id_2) max_last_encountered_sequence_id_2
    from brainstore_backfill_tracked_objects
    where is_backfilling
),
max_realtime_lag AS (
SELECT
    /* integer gap (largest of the two branches) */
    GREATEST(last_encountered_sequence_id - last_processed_sequence_id,
                last_encountered_sequence_id_2 - last_processed_sequence_id_2)
        AS realtime_ids_lag,

    /* wall-clock gap in seconds (largest branch) */
    GREATEST(
        /* logs branch */
        COALESCE(
            ( SELECT EXTRACT(EPOCH FROM (le.row_created - lp.row_created))
            FROM   logs  AS le
            JOIN   logs  AS lp ON lp.sequence_id = b.last_processed_sequence_id
            WHERE  le.sequence_id = b.last_encountered_sequence_id
            ), 0),
        /* logs2 branch */
        COALESCE(
            ( SELECT EXTRACT(EPOCH FROM (le2.row_created - lp2.row_created))
            FROM   logs2 AS le2
            JOIN   logs2 AS lp2 ON lp2.sequence_id = b.last_processed_sequence_id_2
            WHERE  le2.sequence_id = b.last_encountered_sequence_id_2
            ), 0)
    )::bigint AS realtime_secs_lag,

    /* pass-through diagnostics */
    org_id,
    project_id,
    object_type,
    last_backfilled_ts
FROM   brainstore_backfill_tracked_objects b
WHERE  is_backfilling
    AND  completed_initial_backfill_ts IS NOT NULL
ORDER  BY realtime_ids_lag DESC
LIMIT  1
)

/* -------------------------------------------------------------------- *
*  Grab the row_created timestamp for every "anchor" sequence_id
* -------------------------------------------------------------------- */
, ts_lookup AS (

/* ---------- logs ---------- */
SELECT 'logs' AS src, l.sequence_id, l.row_created
FROM   logs l
WHERE  l.sequence_id IN (
            (SELECT logs_max_sequence_id      FROM logs_stats),
            (SELECT frontier_sequence_id      FROM frontier_stats),
            (SELECT max_last_encountered_sequence_id FROM max_tracked_object_counters)
        )

UNION ALL

/* ---------- logs2 ---------- */
SELECT 'logs2', l2.sequence_id, l2.row_created
FROM   logs2 l2
WHERE  l2.sequence_id IN (
            (SELECT logs2_max_sequence_id     FROM logs2_stats),
            (SELECT frontier_sequence_id_2    FROM frontier_stats),
            (SELECT max_last_encountered_sequence_id_2 FROM max_tracked_object_counters)
        )

UNION ALL

/* ---------- comments ---------- */
SELECT 'comments', c.sequence_id, c.row_created
FROM   comments c
WHERE  c.sequence_id IN (
            (SELECT comments_max_sequence_id  FROM comments_stats),
            (SELECT comments_backfill_sequence_id FROM frontier_stats)
        )
)
, ts AS (
SELECT
    MAX(row_created) FILTER (WHERE src = 'logs'    AND sequence_id = (SELECT logs_max_sequence_id  FROM logs_stats))  AS logs_max_ts,
    MAX(row_created) FILTER (WHERE src = 'logs'    AND sequence_id = (SELECT frontier_sequence_id  FROM frontier_stats)) AS logs_frontier_ts,
    MAX(row_created) FILTER (WHERE src = 'logs'    AND sequence_id = (SELECT max_last_encountered_sequence_id FROM max_tracked_object_counters)) AS logs_enc_ts,

    MAX(row_created) FILTER (WHERE src = 'logs2'   AND sequence_id = (SELECT logs2_max_sequence_id FROM logs2_stats)) AS logs2_max_ts,
    MAX(row_created) FILTER (WHERE src = 'logs2'   AND sequence_id = (SELECT frontier_sequence_id_2 FROM frontier_stats)) AS logs2_frontier_ts,
    MAX(row_created) FILTER (WHERE src = 'logs2'   AND sequence_id = (SELECT max_last_encountered_sequence_id_2 FROM max_tracked_object_counters)) AS logs2_enc_ts,

    MAX(row_created) FILTER (WHERE src = 'comments' AND sequence_id = (SELECT comments_max_sequence_id FROM comments_stats)) AS comments_max_ts,
    MAX(row_created) FILTER (WHERE src = 'comments' AND sequence_id = (SELECT comments_backfill_sequence_id FROM frontier_stats)) AS comments_backfill_ts
FROM ts_lookup
)
SELECT
/* integer lags already present */
(logs_max_sequence_id   - frontier_sequence_id)               AS frontier_sequence_id_lag,
(frontier_sequence_id   - max_last_encountered_sequence_id)   AS encountered_sequence_id_lag,

(logs2_max_sequence_id  - frontier_sequence_id_2)             AS frontier_sequence_id_lag_2,
(frontier_sequence_id_2 - max_last_encountered_sequence_id_2) AS encountered_sequence_id_lag_2,

(comments_max_sequence_id - comments_backfill_sequence_id)    AS comments_backfill_sequence_id_lag,

/* real-time gap: ids and seconds */
GREATEST(0, rtl.realtime_ids_lag)  AS realtime_encountered_processed_sequence_id_lag,
rtl.realtime_secs_lag              AS realtime_encountered_processed_sequence_id_lag_seconds,

/* diagnostics */
rtl.org_id            AS max_realtime_lag_org_id,
rtl.project_id        AS max_realtime_lag_project_id,
rtl.object_type       AS max_realtime_lag_object_type,
rtl.last_backfilled_ts AS max_realtime_lag_last_backfilled_ts
FROM
logs_stats
CROSS JOIN logs2_stats
CROSS JOIN comments_stats
CROSS JOIN frontier_stats
CROSS JOIN max_tracked_object_counters
CROSS JOIN max_realtime_lag AS rtl
                "#,
                &[],
            )
        .await?;

        let row = results
            .get(0)
            .ok_or_else(|| util::anyhow::anyhow!("No backfill results"))?;

        let frontier_sequence_id_lag: Option<i64> = row.get("frontier_sequence_id_lag");
        let encountered_sequence_id_lag: Option<i64> = row.get("encountered_sequence_id_lag");
        let frontier_sequence_id_lag_2: Option<i64> = row.get("frontier_sequence_id_lag_2");
        let encountered_sequence_id_lag_2: Option<i64> = row.get("encountered_sequence_id_lag_2");
        let comments_backfill_sequence_id_lag: Option<i64> =
            row.get("comments_backfill_sequence_id_lag");
        let realtime_encountered_processed_sequence_id_lag: Option<i64> =
            row.get("realtime_encountered_processed_sequence_id_lag");
        let realtime_encountered_processed_sequence_id_lag_seconds: Option<i64> =
            row.get("realtime_encountered_processed_sequence_id_lag_seconds");

        self.frontier_sequence_id_lag.record(
            std::cmp::max(frontier_sequence_id_lag, frontier_sequence_id_lag_2).unwrap_or(0) as u64,
            &[],
        );
        self.encountered_sequence_id_lag.record(
            std::cmp::max(encountered_sequence_id_lag, encountered_sequence_id_lag_2).unwrap_or(0)
                as u64,
            &[],
        );
        self.comments_backfill_sequence_id_lag
            .record(comments_backfill_sequence_id_lag.unwrap_or(0) as u64, &[]);
        self.realtime_encountered_processed_sequence_id_lag.record(
            realtime_encountered_processed_sequence_id_lag.unwrap_or(0) as u64,
            &[],
        );
        self.realtime_encountered_processed_sequence_id_lag_seconds
            .record(
                realtime_encountered_processed_sequence_id_lag_seconds.unwrap_or(0) as u64,
                &[],
            );

        let worst_10_processing_laggards = global_store_conn
            .query(
                r#"
WITH
    /* ---------------------------------------------------------------- *
     *  High-water marks and misc stats                                 *
     * ---------------------------------------------------------------- */
    logs_stats AS (
        SELECT MAX(sequence_id) AS logs_max_sequence_id
        FROM   logs
    ),
    logs2_stats AS (
        SELECT MAX(sequence_id) AS logs2_max_sequence_id
        FROM   logs2
    ),
    comments_stats AS (
        SELECT MAX(sequence_id) AS comments_max_sequence_id
        FROM   comments
    ),
    frontier_stats AS (
        SELECT
            historical_full_backfill_sequence_id as frontier_sequence_id,
            historical_full_backfill_sequence_id_2 as frontier_sequence_id_2,
            comments_backfill_sequence_id
        FROM brainstore_backfill_global_state
    ),
    max_tracked_object_counters AS (
        SELECT
            MAX(last_encountered_sequence_id)     AS max_last_encountered_sequence_id,
            MAX(last_encountered_sequence_id_2)   AS max_last_encountered_sequence_id_2
        FROM   brainstore_backfill_tracked_objects
        WHERE  is_backfilling
    ),

    /* ---------------------------------------------------------------- *
     *  Per-object real-time lag (largest of the two branches)          *
     * ---------------------------------------------------------------- */
    max_realtime_lag AS (
        SELECT
            /* raw sequence-ID gap */
            GREATEST(
                last_encountered_sequence_id   - last_processed_sequence_id,
                last_encountered_sequence_id_2 - last_processed_sequence_id_2
            )                                                   AS realtime_ids_lag,

            /* wall-clock gap in seconds (choose larger branch) */
            GREATEST(
                /* logs branch */
                COALESCE(
                    (
                        SELECT EXTRACT(EPOCH FROM (le.row_created - lp.row_created))
                        FROM   logs  AS le
                        JOIN   logs  AS lp
                               ON lp.sequence_id = b.last_processed_sequence_id
                        WHERE  le.sequence_id = b.last_encountered_sequence_id
                    ),
                    0
                ),
                /* logs2 branch */
                COALESCE(
                    (
                        SELECT EXTRACT(EPOCH FROM (le2.row_created - lp2.row_created))
                        FROM   logs2 AS le2
                        JOIN   logs2 AS lp2
                               ON lp2.sequence_id = b.last_processed_sequence_id_2
                        WHERE  le2.sequence_id = b.last_encountered_sequence_id_2
                    ),
                    0
                )
            )::bigint                                            AS realtime_secs_lag,

            /* diagnostics passthrough */
            org_id,
            project_id,
            object_type,
            last_backfilled_ts
        FROM   brainstore_backfill_tracked_objects b
        WHERE  is_backfilling
          AND  completed_initial_backfill_ts IS NOT NULL
        ORDER  BY realtime_ids_lag DESC
        LIMIT  10
    ),

    /* ---------------------------------------------------------------- *
     *  Collect timestamps for key anchor IDs across all streams        *
     * ---------------------------------------------------------------- */
    ts_lookup AS (

        /* ---- logs ---- */
        SELECT 'logs' AS src, l.sequence_id, l.row_created
        FROM   logs l
        WHERE  l.sequence_id IN (
                  (SELECT logs_max_sequence_id            FROM logs_stats),
                  (SELECT frontier_sequence_id            FROM frontier_stats),
                  (SELECT max_last_encountered_sequence_id FROM max_tracked_object_counters)
              )

        UNION ALL

        /* ---- logs2 ---- */
        SELECT 'logs2', l2.sequence_id, l2.row_created
        FROM   logs2 l2
        WHERE  l2.sequence_id IN (
                  (SELECT logs2_max_sequence_id           FROM logs2_stats),
                  (SELECT frontier_sequence_id_2          FROM frontier_stats),
                  (SELECT max_last_encountered_sequence_id_2 FROM max_tracked_object_counters)
              )

        UNION ALL

        /* ---- comments ---- */
        SELECT 'comments', c.sequence_id, c.row_created
        FROM   comments c
        WHERE  c.sequence_id IN (
                  (SELECT comments_max_sequence_id        FROM comments_stats),
                  (SELECT comments_backfill_sequence_id   FROM frontier_stats)
              )
    ),

    /* ---------------------------------------------------------------- *
     *  Pivot the timestamps into a single row for easy diffing          *
     * ---------------------------------------------------------------- */
    ts AS (
        SELECT
            MAX(row_created) FILTER (WHERE src = 'logs'    AND sequence_id = (SELECT logs_max_sequence_id            FROM logs_stats))  AS logs_max_ts,
            MAX(row_created) FILTER (WHERE src = 'logs'    AND sequence_id = (SELECT frontier_sequence_id            FROM frontier_stats)) AS logs_frontier_ts,
            MAX(row_created) FILTER (WHERE src = 'logs'    AND sequence_id = (SELECT max_last_encountered_sequence_id FROM max_tracked_object_counters)) AS logs_enc_ts,

            MAX(row_created) FILTER (WHERE src = 'logs2'   AND sequence_id = (SELECT logs2_max_sequence_id           FROM logs2_stats)) AS logs2_max_ts,
            MAX(row_created) FILTER (WHERE src = 'logs2'   AND sequence_id = (SELECT frontier_sequence_id_2          FROM frontier_stats)) AS logs2_frontier_ts,
            MAX(row_created) FILTER (WHERE src = 'logs2'   AND sequence_id = (SELECT max_last_encountered_sequence_id_2 FROM max_tracked_object_counters)) AS logs2_enc_ts,

            MAX(row_created) FILTER (WHERE src = 'comments' AND sequence_id = (SELECT comments_max_sequence_id       FROM comments_stats)) AS comments_max_ts,
            MAX(row_created) FILTER (WHERE src = 'comments' AND sequence_id = (SELECT comments_backfill_sequence_id  FROM frontier_stats)) AS comments_backfill_ts
        FROM   ts_lookup
    )

/* ---------------------------------------------------------------------- *
 *  Final report                                                          *
 * ---------------------------------------------------------------------- */
SELECT
    /* sequence-ID lags (logs branch) */
    logs_max_sequence_id   - frontier_sequence_id                      AS frontier_sequence_id_lag,
    frontier_sequence_id   - max_last_encountered_sequence_id          AS encountered_sequence_id_lag,

    /* sequence-ID lags (logs2 branch) */
    logs2_max_sequence_id  - frontier_sequence_id_2                    AS frontier_sequence_id_lag_2,
    frontier_sequence_id_2 - max_last_encountered_sequence_id_2        AS encountered_sequence_id_lag_2,

    /* comments stream lag */
    comments_max_sequence_id - comments_backfill_sequence_id           AS comments_backfill_sequence_id_lag,

    /* real-time processing lag (largest branch) */
    GREATEST(0, rtl.realtime_ids_lag)                                  AS realtime_encountered_processed_sequence_id_lag,
    rtl.realtime_secs_lag                                              AS realtime_encountered_processed_sequence_id_lag_seconds,

    /* diagnostics */
    rtl.org_id,
    rtl.project_id,
    rtl.object_type,
    rtl.last_backfilled_ts
FROM   logs_stats
CROSS  JOIN logs2_stats
CROSS  JOIN comments_stats
CROSS  JOIN frontier_stats
CROSS  JOIN max_tracked_object_counters
CROSS  JOIN max_realtime_lag AS rtl
                "#,
                &[],
            )
            .await?;

        for row in worst_10_processing_laggards {
            let project_id: String = row.get(8);
            let object_type: String = row.get(9);
            let lag_seconds: i64 = row.get(6);
            self.processing_furthest_behind_delta_seconds.record(
                lag_seconds as u64,
                &[
                    KeyValue::new("object_type".to_string(), object_type),
                    KeyValue::new("project_id".to_string(), project_id),
                ],
            );
        }

        let num_behind_objects = global_store_conn
            .query(
                r#"
WITH object_lags AS (
    SELECT
        project_id,
        object_type,
        GREATEST(
            last_encountered_sequence_id  - last_processed_sequence_id,
            last_encountered_sequence_id_2 - last_processed_sequence_id_2
        ) AS backfill_lag
    FROM brainstore_backfill_tracked_objects
    WHERE is_backfilling
      AND completed_initial_backfill_ts IS NOT NULL
)

SELECT COUNT(*)
FROM   object_lags
WHERE  backfill_lag > 250000
                "#,
                &[],
            )
            .await?;

        let behind_objects: i64 = num_behind_objects.get(0).unwrap().get(0);
        self.processing_num_objects_behind
            .record(behind_objects as u64, &[]);

        let worst_10_compaction_laggards = global_store_conn
            .query(
                r#"
WITH last_compacted AS (
    SELECT
        l.object_id,
        MAX(m.last_compacted_index_meta_xact_id) AS last_compacted_xact_id
    FROM   brainstore_global_store_segment_id_to_liveness  l
    JOIN   brainstore_global_store_segment_id_to_metadata  m USING (segment_id)
    WHERE  l.is_live
    GROUP  BY l.object_id
),
compaction_delta AS (
    SELECT
        g.object_id,
        (((g.last_processed_xact_id >> 16) & X'FFFFFFFF'::bigint)
        - ((c.last_compacted_xact_id >> 16) & X'FFFFFFFF'::bigint)) AS lag_s
    FROM   brainstore_global_store_object_id_to_metadata g
    JOIN   last_compacted c USING (object_id)
)
SELECT  object_id,
        lag_s            AS lag_seconds
FROM    compaction_delta
WHERE   lag_s <= 3600
ORDER BY lag_s DESC NULLS LAST
            LIMIT   10
                "#,
                &[],
            )
            .await?;
        for row in worst_10_compaction_laggards {
            let object_id: String = row.get(0);
            let lag_seconds: i64 = row.get(1);
            self.compaction_furthest_behind_delta_seconds.record(
                lag_seconds as u64,
                &[KeyValue::new("object_id".to_string(), object_id)],
            );
        }

        let short_term_min_lag = SHORT_TERM_OPTIMIZATION_LOOP_CONFIG
            .min_compaction_lag_seconds
            .unwrap_or(0) as i64;
        let short_term_min_seconds_ago =
            SHORT_TERM_OPTIMIZATION_LOOP_CONFIG.min_processed_seconds_ago as i64;
        let short_term_max_seconds_ago =
            SHORT_TERM_OPTIMIZATION_LOOP_CONFIG.max_processed_seconds_ago as i64;

        let long_term_min_lag = SLOW_COMPACTION_THRESHOLD_SECS as i64;
        let long_term_min_seconds_ago =
            LONG_TERM_OPTIMIZATION_LOOP_CONFIG.min_processed_seconds_ago as i64;
        let long_term_max_seconds_ago =
            LONG_TERM_OPTIMIZATION_LOOP_CONFIG.max_processed_seconds_ago as i64;

        let num_behind_objects = global_store_conn
            .query(
                r#"
WITH last_compacted AS (
    SELECT
        l.object_id,
        MAX(m.last_compacted_index_meta_xact_id) AS last_compacted_xact_id
    FROM   brainstore_global_store_segment_id_to_liveness  l
    JOIN   brainstore_global_store_segment_id_to_metadata  m USING (segment_id)
    WHERE  l.is_live
    GROUP  BY l.object_id
),
compaction_delta AS (
    SELECT
        g.object_id,
        g.last_processed_xact_id,
        c.last_compacted_xact_id,
        (((g.last_processed_xact_id >> 16) & X'FFFFFFFF'::bigint)
        - ((c.last_compacted_xact_id >> 16) & X'FFFFFFFF'::bigint)) AS lag_s,
        ((g.last_processed_xact_id >> 16) & X'FFFFFFFF'::bigint) AS processed_timestamp,
        (EXTRACT(EPOCH FROM NOW())::bigint
        - ((g.last_processed_xact_id >> 16) & X'FFFFFFFF'::bigint)) AS processed_seconds_ago
    FROM   brainstore_global_store_object_id_to_metadata g
    JOIN   last_compacted c USING (object_id)
)
SELECT
    COUNT(*) FILTER (WHERE lag_s > $7 OR (lag_s IS NULL AND processed_seconds_ago > $7)) AS objects_over_8m,
    COUNT(*) FILTER (
        WHERE lag_s > $1::bigint OR (lag_s IS NULL AND processed_seconds_ago > $1::bigint)
        AND processed_seconds_ago >= $2::bigint
        AND processed_seconds_ago <= $3::bigint
    ) AS short_term_loop_objects,
    COUNT(*) FILTER (
        WHERE lag_s > $4::bigint OR (lag_s IS NULL AND processed_seconds_ago > $4::bigint)
        AND processed_seconds_ago >= $5::bigint
        AND processed_seconds_ago <= $6::bigint
    ) AS long_term_loop_objects
FROM   compaction_delta
                "#,
                &[
                    &short_term_min_lag,
                    &short_term_min_seconds_ago,
                    &short_term_max_seconds_ago,
                    &long_term_min_lag,
                    &long_term_min_seconds_ago,
                    &long_term_max_seconds_ago,
                    &long_term_min_lag, // $7 - reuse for objects_over_8m filter
                ],
            )
            .await?;

        let objects_over_8m: i64 = num_behind_objects.get(0).unwrap().get(0);
        let short_term_loop_objects: i64 = num_behind_objects.get(0).unwrap().get(1);
        let long_term_loop_objects: i64 = num_behind_objects.get(0).unwrap().get(2);

        self.compaction_num_objects_behind
            .record(objects_over_8m as u64, &[]);
        self.compaction_short_term_loop_objects
            .record(short_term_loop_objects as u64, &[]);
        self.compaction_long_term_loop_objects
            .record(long_term_loop_objects as u64, &[]);

        let queue_size = self.compaction_loop.queue_size();
        self.compaction_queue_size.record(queue_size as u64, &[]);

        Ok(())
    }
}

/* These queries are stashed here for reference purposes:


A histogram of compaction lag:

/* ---------- 1.  Per-object lag in seconds ---------- */
WITH last_compacted AS (
    SELECT  l.object_id,
            MAX(m.last_compacted_index_meta_xact_id) AS last_compacted_xact_id
    FROM    brainstore_global_store_segment_id_to_liveness  l
    JOIN    brainstore_global_store_segment_id_to_metadata  m USING (segment_id)
    WHERE   l.is_live
    GROUP   BY l.object_id
),
compaction_delta AS (
    SELECT  g.object_id,
            (((g.last_processed_xact_id >> 16) & X'FFFFFFFF'::bigint)
           - ((c.last_compacted_xact_id >> 16) & X'FFFFFFFF'::bigint)) AS lag_s
    FROM    brainstore_global_store_object_id_to_metadata g
    JOIN    last_compacted c USING (object_id)
)

/* ---------- 2.  Assign each lag to one of 10 buckets ---------- */
SELECT
       edges.le_seconds          AS le,        -- upper-bound label
       COALESCE(b.count_in_bucket, 0) AS objects
FROM (
        /* width_bucket() gives us a 1-based bucket index           */
        SELECT width_bucket(
                   lag_s,
                   ARRAY[0,30,60,120,300,600,1800,3600,86400,604800,
                         9223372036854775807]          -- 0 .. 7-d .. +∞
               ) AS bucket_no,
               COUNT(*) AS count_in_bucket
        FROM   compaction_delta
        GROUP  BY bucket_no
) AS b
RIGHT JOIN (  -- map bucket_no → human-readable upper bound
        VALUES
            (1,     30   ),   --  <30 s
            (2,     60   ),   --  30-60 s
            (3,    120   ),   --   1-2 m
            (4,    300   ),   --   2-5 m
            (5,    600   ),   --   5-10 m
            (6,   1800   ),   --  10-30 m
            (7,   3600   ),   --  30-60 m
            (8,  86400   ),   --   1 h-1 d
            (9, 604800   ),   --   1-7 d
            (10, 9223372036854775807)   --   7 d+
) AS edges(bucket_no, le_seconds)
USING (bucket_no)
ORDER BY edges.le_seconds
;

*/

struct PoolMetricsCollector {
    pg_conn: Option<Arc<PostgresPool>>,
}

impl PoolMetricsCollector {
    fn new(pg_conn: Option<Arc<PostgresPool>>) -> Box<dyn Collector> {
        Box::new(PoolMetricsCollector { pg_conn })
    }
}

#[async_trait]
impl Collector for PoolMetricsCollector {
    async fn collect(&mut self) -> Result<()> {
        record_pool_metrics();

        // If we have a connection, collect PostgreSQL connection states
        if let Some(pg_conn) = &self.pg_conn {
            if let Err(e) = collect_pg_connection_states(pg_conn).await {
                tracing::warn!("Failed to collect PostgreSQL connection states: {:?}", e);
            }
        }

        Ok(())
    }
}

async fn collect_pg_connection_states(pg_conn: &PostgresPool) -> Result<()> {
    use storage::postgres_global_store::POOL_METRICS;

    let client = pg_conn.get_client().await?;

    // Query pg_stat_activity for connection states
    let rows = client
        .query(
            r#"
SELECT state, COUNT(*) as count
FROM pg_stat_activity
WHERE datname = current_database()
GROUP BY state
            "#,
            &[],
        )
        .await?;

    // Reset all counters to 0
    POOL_METRICS.pg_connections_active.record(0, &[]);
    POOL_METRICS.pg_connections_idle.record(0, &[]);
    POOL_METRICS
        .pg_connections_idle_in_transaction
        .record(0, &[]);
    POOL_METRICS
        .pg_connections_idle_in_transaction_aborted
        .record(0, &[]);

    // Update counters based on query results
    for row in rows {
        let state: Option<String> = row.get(0);
        let count: i64 = row.get(1);

        match state.as_deref() {
            Some("active") => POOL_METRICS.pg_connections_active.record(count as u64, &[]),
            Some("idle") => POOL_METRICS.pg_connections_idle.record(count as u64, &[]),
            Some("idle in transaction") => POOL_METRICS
                .pg_connections_idle_in_transaction
                .record(count as u64, &[]),
            Some("idle in transaction (aborted)") => POOL_METRICS
                .pg_connections_idle_in_transaction_aborted
                .record(count as u64, &[]),
            _ => {} // Ignore other states or NULL
        }
    }

    // Query long-running queries with breakdown by duration buckets
    let long_running_queries = client
        .query_one(
            r#"
SELECT
    COUNT(*) FILTER (WHERE (NOW() - query_start) > interval '1 minutes') as over_1min,
    COUNT(*) FILTER (WHERE (NOW() - query_start) > interval '5 minutes') as over_5min,
    COUNT(*) FILTER (WHERE (NOW() - query_start) > interval '10 minutes') as over_10min,
    COUNT(*) FILTER (WHERE (NOW() - query_start) > interval '30 minutes') as over_30min,
    MAX(EXTRACT(EPOCH FROM (NOW() - query_start))::bigint) as max_duration_seconds
FROM pg_stat_activity
WHERE state <> 'idle'
  AND query_start IS NOT NULL
  AND datname = current_database()
            "#,
            &[],
        )
        .await?;

    let over_1min: i64 = long_running_queries.get(0);
    let over_5min: i64 = long_running_queries.get(1);
    let over_10min: i64 = long_running_queries.get(2);
    let over_30min: i64 = long_running_queries.get(3);
    let max_duration: Option<i64> = long_running_queries.get(4);

    POOL_METRICS
        .long_running_queries_count
        .record(over_1min as u64, &[]);
    if let Some(duration) = max_duration {
        POOL_METRICS
            .longest_query_duration_seconds
            .record(duration as u64, &[]);
    }

    // Log breakdown if there are long-running queries
    if over_1min > 0 {
        tracing::warn!(
            "Long-running queries: >1min: {}, >5min: {}, >10min: {}, >30min: {}, longest: {}s",
            over_1min,
            over_5min,
            over_10min,
            over_30min,
            max_duration.unwrap_or(0)
        );
    }

    // Query all active query durations for histogram
    let active_queries = client
        .query(
            r#"
SELECT EXTRACT(EPOCH FROM (NOW() - query_start))::bigint as duration_seconds
FROM pg_stat_activity
WHERE state <> 'idle'
  AND query_start IS NOT NULL
  AND datname = current_database()
            "#,
            &[],
        )
        .await?;

    for row in active_queries {
        let duration: i64 = row.get(0);
        if duration >= 0 {
            POOL_METRICS
                .query_duration_histogram
                .record(duration as u64, &[]);
        }
    }

    // Query connection age statistics
    let age_stats = client
        .query_one(
            r#"
SELECT
    COUNT(*) as total_connections,
    COUNT(*) FILTER (WHERE EXTRACT(EPOCH FROM (NOW() - backend_start)) < 60) as connections_under_1min,
    COUNT(*) FILTER (WHERE EXTRACT(EPOCH FROM (NOW() - backend_start)) >= 60 AND EXTRACT(EPOCH FROM (NOW() - backend_start)) < 300) as connections_1_5min,
    COUNT(*) FILTER (WHERE EXTRACT(EPOCH FROM (NOW() - backend_start)) >= 300 AND EXTRACT(EPOCH FROM (NOW() - backend_start)) < 3600) as connections_5min_1hr,
    COUNT(*) FILTER (WHERE EXTRACT(EPOCH FROM (NOW() - backend_start)) >= 3600) as connections_over_1hr,
    MAX(EXTRACT(EPOCH FROM (NOW() - backend_start))::bigint) as oldest_connection_seconds
FROM pg_stat_activity
WHERE datname = current_database()
            "#,
            &[],
        )
        .await?;

    // Record connection age metrics
    let total: i64 = age_stats.get(0);
    let under_1min: i64 = age_stats.get(1);
    let _1_5min: i64 = age_stats.get(2);
    let _5min_1hr: i64 = age_stats.get(3);
    let over_1hr: i64 = age_stats.get(4);
    let oldest: Option<i64> = age_stats.get(5);

    // Record oldest connection
    if let Some(oldest_secs) = oldest {
        POOL_METRICS
            .oldest_connection_seconds
            .record(oldest_secs as u64, &[]);
    }

    // Record connection ages in histogram buckets
    // Add each connection to the appropriate bucket
    for _ in 0..under_1min {
        POOL_METRICS.connection_age_histogram.record(30, &[]); // Use 30s as representative
    }
    for _ in 0.._1_5min {
        POOL_METRICS.connection_age_histogram.record(180, &[]); // Use 3min as representative
    }
    for _ in 0.._5min_1hr {
        POOL_METRICS.connection_age_histogram.record(1800, &[]); // Use 30min as representative
    }
    for _ in 0..over_1hr {
        POOL_METRICS.connection_age_histogram.record(7200, &[]); // Use 2hr as representative
    }

    if total > 0 {
        tracing::debug!(
            "Connection age distribution: <1min: {}, 1-5min: {}, 5min-1hr: {}, >1hr: {}, oldest: {}s",
            under_1min, _1_5min, _5min_1hr, over_1hr, oldest.unwrap_or(0)
        );
    }

    Ok(())
}

struct StaticRuntimeMetrics {
    num_workers: Gauge<u64>,
    num_alive_tasks: Gauge<u64>,
    num_blocking_threads: Gauge<u64>,
    num_idle_blocking_threads: Gauge<u64>,
    blocking_queue_depth: Gauge<u64>,
    blocking_thread_utilization_ratio: Gauge<f64>,
}

impl StaticRuntimeMetrics {
    fn new(meter: &Meter) -> Self {
        Self {
            num_workers: meter
                .u64_gauge("brainstore.static_runtime.num_workers")
                .with_description("Number of worker threads in STATIC_SYNC_RUNTIME")
                .build(),
            num_alive_tasks: meter
                .u64_gauge("brainstore.static_runtime.num_alive_tasks")
                .with_description("Number of alive tasks in STATIC_SYNC_RUNTIME")
                .build(),
            num_blocking_threads: meter
                .u64_gauge("brainstore.static_runtime.num_blocking_threads")
                .with_description("Number of blocking threads currently in use in STATIC_SYNC_RUNTIME")
                .build(),
            num_idle_blocking_threads: meter
                .u64_gauge("brainstore.static_runtime.num_idle_blocking_threads")
                .with_description("Number of idle blocking threads available in STATIC_SYNC_RUNTIME")
                .build(),
            blocking_queue_depth: meter
                .u64_gauge("brainstore.static_runtime.blocking_queue_depth")
                .with_description("Number of tasks waiting for a blocking thread in STATIC_SYNC_RUNTIME")
                .build(),
            blocking_thread_utilization_ratio: meter
                .f64_gauge("brainstore.static_runtime.blocking_thread_utilization_ratio")
                .with_description("Ratio of blocking threads in use to max blocking threads (0.0-1.0, where 1.0 means all threads exhausted)")
                .build(),
        }
    }
}

#[async_trait]
impl Collector for StaticRuntimeMetrics {
    async fn collect(&mut self) -> Result<()> {
        let metrics = get_static_runtime_metrics();

        self.num_workers.record(metrics.num_workers as u64, &[]);
        self.num_alive_tasks
            .record(metrics.num_alive_tasks as u64, &[]);
        self.num_blocking_threads
            .record(metrics.num_blocking_threads as u64, &[]);
        self.num_idle_blocking_threads
            .record(metrics.num_idle_blocking_threads as u64, &[]);
        self.blocking_queue_depth
            .record(metrics.blocking_queue_depth as u64, &[]);

        // Calculate and record the utilization ratio
        let threads_in_use = metrics.num_blocking_threads;
        let max_threads = storage::static_sync_runtime::MAX_STATIC_THREADS;
        let utilization_ratio = threads_in_use as f64 / max_threads as f64;
        self.blocking_thread_utilization_ratio
            .record(utilization_ratio, &[]);

        // Log warning if we're approaching thread exhaustion
        let utilization = utilization_ratio * 100.0;

        if utilization > 90.0 {
            tracing::warn!(
                "STATIC_SYNC_RUNTIME thread pool utilization high: {:.1}% ({}/{} threads, {} idle, queue: {})",
                utilization,
                threads_in_use,
                max_threads,
                metrics.num_idle_blocking_threads,
                metrics.blocking_queue_depth
            );
        } else if metrics.blocking_queue_depth > 100 {
            tracing::warn!(
                "STATIC_SYNC_RUNTIME blocking queue depth high: {} (threads: {}/{}, idle: {})",
                metrics.blocking_queue_depth,
                threads_in_use,
                max_threads,
                metrics.num_idle_blocking_threads
            );
        }

        Ok(())
    }
}
