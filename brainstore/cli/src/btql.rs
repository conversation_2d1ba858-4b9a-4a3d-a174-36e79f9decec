use std::{
    collections::{BTreeMap, HashSet},
    sync::Arc,
};

use agent::tracing_opentelemetry::OpenTelemetrySpanExt;
use btql::util::Cursor;
use clap::{Parser, Subcommand, ValueEnum};
use otel_common::opentelemetry;

use query::{
    interpreter::{
        context::{InterpreterOpts, ModelCostsMap},
        error::InterpreterError,
        InterpreterContext,
    },
    optimizer::optimizer::OptimizerOpts,
    Operator,
};

use futures::StreamExt;
use serde::{Deserialize, Serialize};
use serde_json::json;
use storage::{
    directory::AsyncDirectoryArc, index_document::make_full_schema,
    index_wal_reader::IndexWalReaderOpts,
};
use tracing::{debug, instrument, span, Instrument, Instrumented, Level};
use util::{
    anyhow::{anyhow, Result},
    global_opts::suppress_verbose_info,
    ptree::MakePTree,
    system_types::{make_object_schema, ObjectType},
    Value,
};

use crate::base::{AppState, BaseArgs, CLIArgs};

#[cfg(feature = "expect-tests")]
use async_util::spawn_blocking_util::spawn_blocking_with_async_timeout;
#[cfg(feature = "expect-tests")]
use query::expect_tests::StorageTestOpts;
#[cfg(feature = "expect-tests")]
use std::path::PathBuf;

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
#[command(author, version, about)]
pub struct BindArgs {
    #[arg(env = "BRAINSTORE_BTQL_QUERY")]
    pub query: serde_json::Value,

    #[arg(
        short,
        long,
        default_value_t = default_schema(),
        env = "BRAINSTORE_BTQL_SCHEMA"
    )]
    #[serde(default = "default_schema")]
    pub schema: String,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
#[command(author, version, about)]
pub struct CursorArgs {
    #[arg(env = "BRAINSTORE_BTQL_CURSOR_KEY")]
    pub cursor_or_pagination_key: String,
}

fn default_schema() -> String {
    "schema.json".to_string()
}

#[derive(Debug, Clone, Copy, ValueEnum, Serialize, Deserialize)]
pub enum ExecutionStage {
    #[value(name = "bind")]
    #[serde(rename = "bind")]
    Bind,
    #[value(name = "optimize")]
    #[serde(rename = "optimize")]
    Optimize,
    #[value(name = "plan")]
    #[serde(rename = "plan")]
    Plan,
    #[value(name = "execute")]
    #[serde(rename = "execute")]
    Execute,
    #[value(name = "results")]
    #[serde(rename = "results")]
    Results,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
#[command(author, version, about)]
pub struct QueryArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub bind: BindArgs,

    #[arg(long, default_value = "false", env = "BRAINSTORE_BTQL_BOUND")]
    #[serde(default)]
    pub bound: bool,

    #[command(flatten)]
    #[serde(flatten)]
    pub optimizer_opts: OptimizerOpts,

    #[command(flatten)]
    #[serde(flatten)]
    pub interpreter_opts: InterpreterOpts,

    #[arg(
        long,
        default_value = "results",
        value_enum,
        env = "BRAINSTORE_BTQL_STAGE"
    )]
    #[serde(default = "default_execution_stage")]
    pub stage: ExecutionStage,

    #[arg(
        long,
        default_value_t = default_num_query_threads(),
        help = "The number of threads to use (defaults to 64)",
        env = "BRAINSTORE_BTQL_NUM_THREADS"
    )]
    #[serde(default = "default_num_query_threads")]
    pub num_threads: usize,

    #[arg(long, default_value = "false", env = "BRAINSTORE_BTQL_CURSOR")]
    #[serde(default)]
    pub cursor: bool,

    #[arg(long, default_value = "false", env = "BRAINSTORE_BTQL_INCLUDE_PLAN")]
    #[serde(default)]
    pub include_plan: bool,

    #[arg(long, default_value = "false", env = "BRAINSTORE_BTQL_STREAM")]
    #[serde(default)]
    pub stream: bool,

    #[clap(skip)]
    #[serde(default)]
    pub model_costs: Option<ModelCostsMap>,

    // This argument is provided for compatibility with legacy callers, since we
    // changed the name of the field in the IndexWalReaderOpts to
    // skip_realtime_wal_entries.
    #[clap(skip)]
    #[serde(default)]
    pub skip_unprocessed_wal_entries: Option<bool>,

    #[clap(skip)]
    #[serde(default)]
    pub metric_scope: Option<BTreeMap<String, String>>,
}

impl QueryArgs {
    pub fn incorporate_index_wal_reader_opts(
        self,
        index_wal_reader_opts: &IndexWalReaderOpts,
    ) -> QueryArgs {
        QueryArgs {
            interpreter_opts: InterpreterOpts {
                index_wal_reader_opts: self
                    .interpreter_opts
                    .index_wal_reader_opts
                    .merge_unset(index_wal_reader_opts),
                ..self.interpreter_opts
            },
            ..self
        }
    }
}

fn default_execution_stage() -> ExecutionStage {
    ExecutionStage::Results
}

pub fn default_num_query_threads() -> usize {
    64
}

#[cfg(feature = "expect-tests")]
#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct TestArgs {
    pub paths: Vec<String>,

    #[arg(long, default_value = "false", env = "BRAINSTORE_BTQL_TEST_UPDATE")]
    pub update: bool,

    #[arg(
        long,
        default_value = "false",
        env = "BRAINSTORE_BTQL_TEST_USE_TMP_DIR"
    )]
    pub use_tmp_dir: bool,

    #[arg(long, default_value = "false", env = "BRAINSTORE_BTQL_TEST_SERIAL")]
    pub serial: bool,

    #[arg(
        long,
        default_missing_value = "true",
        num_args = 0..=1,
        env = "BRAINSTORE_BTQL_TEST_RANDOMIZE"
    )]
    pub randomize_events: Option<bool>,

    #[arg(
        long,
        default_missing_value = "true",
        num_args = 0..=1,
        env = "BRAINSTORE_BTQL_TEST_AUTO_EXPAND"
    )]
    pub auto_expand: Option<bool>,

    #[command(flatten)]
    pub storage_opts: StorageTestOpts,

    #[command(flatten)]
    pub optimizer_opts: OptimizerOpts,

    #[command(flatten)]
    pub interpreter_opts: InterpreterOpts,

    #[arg(
        long,
        default_value = "false",
        env = "BRAINSTORE_BTQL_TEST_DEFAULT_OPTS"
    )]
    pub default_opts: bool,
}

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum Commands {
    Bind(CLIArgs<BindArgs>),
    Cursor(CLIArgs<CursorArgs>),
    Query(CLIArgs<QueryArgs>),
    #[cfg(feature = "expect-tests")]
    Test(CLIArgs<TestArgs>),
}

pub fn base_args(args: &Commands) -> &BaseArgs {
    match args {
        Commands::Bind(a) => &a.base,
        Commands::Cursor(a) => &a.base,
        Commands::Query(a) => &a.base,
        #[cfg(feature = "expect-tests")]
        Commands::Test(a) => &a.base,
    }
}

pub fn verbose(args: &Commands) -> u8 {
    base_args(args).verbose
}

pub async fn main(args: Commands) -> Result<util::Value> {
    Ok(match args {
        Commands::Bind(args) => {
            let query_string = match args.args.query {
                serde_json::Value::String(s) => s,
                _ => return Err(util::anyhow::anyhow!("query must be a string")),
            };
            let output_ast = btql::binder::bind_query(&query_string, &args.args.schema)?;
            json!(format!("bound ast: {:#?}", output_ast))
        }
        Commands::Cursor(args) => match args.args.cursor_or_pagination_key.parse::<u64>() {
            Ok(cursor_value) => {
                let cursor = Cursor { cursor_value };
                json!(format!("Cursor: {}", cursor))
            }
            Err(_) => {
                let cursor: Cursor = Cursor::try_from(&args.args.cursor_or_pagination_key)?;
                json!(format!("Cursor: {}", cursor))
            }
        },
        Commands::Query(args) => {
            let span = span!(Level::INFO, "btql/query");
            async move {
                let app_state = AppState::new(&args.base)?;
                let start = std::time::Instant::now();
                let show_cursor = args.args.cursor;
                let stage = args.args.stage;

                let executor = Arc::new(query::interpreter::context::Executor::multi_thread(
                    args.args.num_threads,
                    "btql",
                )?);

                let result = run_query(app_state, executor, args.args)?;

                Ok::<_, util::anyhow::Error>(match result {
                    QueryResult::Plan(plan) => json!(plan),
                    QueryResult::Empty => {
                        log::info!("-- empty");
                        util::Value::Null
                    }
                    QueryResult::Stream {
                        mut stream,
                        ctx,
                        directory: dynamic_directory,
                        ..
                    } => {
                        while let Some(row) = stream.next().await {
                            if matches!(stage, ExecutionStage::Results) {
                                println!("{}", serde_json::to_string(&row?)?);
                            }
                        }

                        if show_cursor {
                            match ctx.get_cursor() {
                                Some(cursor) => println!("\nCursor: {}", cursor),
                                None => {}
                            }
                        }

                        if matches!(stage, ExecutionStage::Execute) {
                            return Ok(serde_json::to_value(&ctx.finish().into_tree())?);
                        } else if !suppress_verbose_info() {
                            tracing::info!(
                                "Query plan:\n{}",
                                ctx.finish().into_tree().format_tree().unwrap()
                            );
                        }
                        if log::log_enabled!(log::Level::Debug) {
                            log::debug!("\n----------DONE------------------\n");
                            log::debug!(
                                "Index timers (after running query in {:?})",
                                start.elapsed()
                            );
                            storage::instrumented::format_timers(
                                dynamic_directory.as_dyn_instrumented(),
                            );
                            if log::log_enabled!(log::Level::Debug) {
                                storage::instrumented::format_cache_metrics(
                                    dynamic_directory.as_dyn_instrumented(),
                                );
                            }
                        }

                        util::Value::Null
                    }
                })
            }
            .instrument(span)
            .await?
        }
        #[cfg(feature = "expect-tests")]
        Commands::Test(args) => {
            let mut config_paths = vec![];
            let mut query_paths = vec![];
            let mut dir_roots = HashSet::new();
            for path in args.args.paths {
                let path = PathBuf::from(path);
                if path.is_dir() {
                    dir_roots.insert(path);
                    continue;
                }
                let path_dir = path.parent().unwrap();
                if path_dir.to_str().unwrap().len() == 0 {
                    dir_roots.insert(PathBuf::from("./"));
                } else {
                    dir_roots.insert(path_dir.to_path_buf());
                }

                if path.extension().unwrap() == "btql" {
                    query_paths.push(path.canonicalize()?);
                } else if path.extension().unwrap() == "yaml" {
                    config_paths.push(path.canonicalize()?);
                }
            }
            for dir in dir_roots {
                tracing::info!("processing directory: {:?}", dir);
                let test_args = query::expect_tests::TestArgs {
                    config_paths: config_paths.clone(),
                    query_paths: query_paths.clone(),
                    update: args.args.update,
                    use_tmp_dir: args.args.use_tmp_dir,
                    parallel: !args.args.serial,
                    randomize_events: args.args.randomize_events,
                    auto_expand: args.args.auto_expand,
                    default_opts: args.args.default_opts,
                    handle: tokio::runtime::Handle::current(),
                    storage_opts: args.args.storage_opts.clone(),
                    optimizer_opts: args.args.optimizer_opts.clone(),
                    interpreter_opts: args.args.interpreter_opts.clone(),
                };
                spawn_blocking_with_async_timeout(
                    &tokio::runtime::Handle::current(),
                    move || query::expect_tests::process_entry(&dir, &test_args),
                    Default::default(),
                    || "test directory".into(),
                )
                .await???;
            }
            util::Value::Null
        }
    })
}

pub enum QueryResult {
    Plan(serde_json::Value),
    Empty,
    Stream {
        stream: Instrumented<query::interpreter::RowStream<'static>>,
        ctx: Arc<InterpreterContext>,
        directory: AsyncDirectoryArc,
        query_plan: String,
    },
}

#[instrument(err, level = "debug", skip(app_state, executor, args))]
pub fn run_query(
    app_state: Arc<AppState>,
    executor: Arc<query::interpreter::context::Executor>,
    args: QueryArgs,
) -> Result<QueryResult> {
    let output_ast = tracing::info_span!("bind").in_scope(|| {
        let output_ast = if args.bound {
            let output_json_ast: btql::binder::json_ast::BoundQuery =
                serde_json::from_value(args.bind.query)?;
            btql::binder::ast::Query::try_from(output_json_ast)?
        } else {
            let query_string = match args.bind.query {
                serde_json::Value::String(s) => s,
                _ => {
                    return Err(anyhow!(
                        "query must be a string (unless it's already bound)"
                    ))
                }
            };
            btql::binder::bind_query(&query_string, &args.bind.schema)?
        };

        let output_ast_plan = output_ast.format_tree().unwrap();
        debug!("output ast plan:\n{}", output_ast_plan);

        Ok::<_, util::anyhow::Error>(output_ast)
    })?;

    if matches!(args.stage, ExecutionStage::Bind) {
        return Ok(QueryResult::Plan(serde_json::to_value(&output_ast)?));
    }

    // Try to derive the object id from the query. If it's unspecified, use the default. Users can override
    // this object id with an explicit schema if they want to avoid the default behavior.

    let schema = make_full_schema(&match &app_state.config_schema {
        Some(schema) => schema.clone(),
        None => {
            let mut object_types = output_ast
                .from
                .objects
                .iter()
                .map(|o| o.object_type)
                .collect::<HashSet<_>>();
            if object_types.len() == 0 {
                return Ok(QueryResult::Empty);
            } else if object_types.len() == 2
                && object_types.contains(&ObjectType::Experiment)
                && object_types.contains(&ObjectType::ProjectLogs)
            {
                // This is the project('project id') case. It's a little messy, but hack around it
                // by just "coalescing" to the project_logs type.
                object_types.clear();
                object_types.insert(ObjectType::Project);
            } else if object_types.len() > 1 {
                return Err(anyhow!("multiple object types specified in FROM clause"));
            }
            make_object_schema(object_types.iter().next().unwrap().clone())?
        }
    })?;

    let expr_ctx = btql::interpreter::context::ExprContext::new_with_executor(
        executor.clone(),
        args.interpreter_opts.tz_offset,
    );

    let optimized_plan = tracing::info_span!("optimize").in_scope(|| {
        let optimized_plan =
            query::optimizer::optimize(&schema, &output_ast, args.optimizer_opts, &expr_ctx)?;
        let optimized_plan_str = optimized_plan.format_tree().unwrap();
        debug!("Optimized plan:\n{}", optimized_plan_str);
        tracing::Span::current().add_event(
            "optimized_plan",
            vec![opentelemetry::KeyValue::new("plan", optimized_plan_str)],
        );
        Ok::<_, util::anyhow::Error>(optimized_plan)
    })?;

    if matches!(args.stage, ExecutionStage::Optimize) {
        return Ok(QueryResult::Plan(serde_json::to_value(&optimized_plan)?));
    }

    let plan_ctx = query::planner::context::PlanContext::new(schema.clone())?;
    let plan = query::plan(&plan_ctx, &optimized_plan)?;
    let plan_str = plan.format_tree().unwrap();
    debug!("Query plan:\n{}", plan_str);

    if matches!(args.stage, ExecutionStage::Plan) {
        return Ok(QueryResult::Plan(Value::String(plan_str)));
    }

    let ctx = query::interpreter::InterpreterContext::new(
        app_state.config.clone(),
        schema,
        executor,
        tokio::runtime::Handle::current(),
        args.interpreter_opts,
        expr_ctx,
        args.model_costs,
        args.metric_scope,
    );

    if log::log_enabled!(log::Level::Debug) {
        log::info!("Index timers (before running query)");
        storage::instrumented::format_timers(
            app_state.config.index.directory.as_dyn_instrumented(),
        );
        storage::instrumented::format_cache_metrics(
            app_state.config.index.directory.as_dyn_instrumented(),
        );
        app_state.config.index.directory.reset_timing();
    }

    let result = tracing::info_span!("interpret")
        .in_scope(|| {
            let result = plan.interpret(ctx.clone())?;
            // We sanitize certain fields from the rows before returning them, to help ensure
            // deterministic output when querying across tantivy and the WAL.
            let sanitized_result = result
                .map(|row| {
                    row.and_then(|mut row| {
                        let sanitized = (move || {
                            use query::interpreter::sanitize::normalize_dt_field;
                            normalize_dt_field(&mut row, "created")?;
                            Ok::<_, util::anyhow::Error>(row)
                        })();
                        sanitized.map_err(InterpreterError::Anyhow)
                    })
                })
                .boxed();
            Ok::<_, InterpreterError>(sanitized_result)
        })?
        .instrument(tracing::info_span!("stream results"));

    Ok(QueryResult::Stream {
        stream: result,
        ctx,
        directory: app_state.config.index.directory.clone(),
        query_plan: plan_str,
    })
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct BtqlArgs {
    #[arg(help = "The query to execute")]
    pub query: String,

    #[arg(
        short,
        long,
        help = "Output format (json, csv, table)",
        default_value = "table",
        env = "BRAINSTORE_BTQL_FORMAT"
    )]
    pub format: String,

    #[arg(
        long,
        help = "Include realtime data from WAL",
        env = "BRAINSTORE_BTQL_REALTIME"
    )]
    pub realtime: bool,

    #[arg(
        long,
        help = "Maximum number of records to return",
        env = "BRAINSTORE_BTQL_LIMIT"
    )]
    pub limit: Option<u64>,

    #[command(flatten)]
    #[serde(flatten)]
    pub interpreter_opts: InterpreterOpts,
}
