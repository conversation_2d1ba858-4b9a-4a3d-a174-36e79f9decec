use std::{
    borrow::Cow,
    collections::{BTreeMap, HashMap},
    sync::Arc,
};

use btql::{
    binder::ast::<PERSON><PERSON>, interpreter::expr::interpret_expr, typesystem::error::TypesystemError,
};
use futures::StreamExt;
use tokio::sync::mpsc;
use util::{tracer::TracedNode, Value};

use crate::planner::ast::GroupByQuery;

use super::{
    aggregator::{aggregate::AggregatorBase, Aggregator, ExprAggregator, ValueAggregator},
    columnar::value::ColumnarExprContext,
    error::Result,
    limiter::SortValue,
    local::interpret_expr_value,
    op::{send_row, ShouldAbort},
    InterpreterContext, Operator, StreamValue,
};

#[async_trait::async_trait]
impl Operator for GroupByQuery {
    fn name(&self) -> &'static str {
        "GroupBy"
    }

    async fn execute(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        let GroupByQuery {
            from,
            dimensions,
            pivot,
            measures,
            aggregates,
        } = self;

        let from_stream = from.interpret_node(ctx.clone(), tracer.clone())?;
        let mut batched_stream =
            futures::StreamExt::ready_chunks(from_stream, ctx.opts.batch_size());

        let mut buckets = HashMap::new();
        let aggregator_base: Vec<ExprAggregator> =
            aggregates.iter().map(|(_, agg)| agg.clone()).collect();

        while let Some(rows) = batched_stream.next().await {
            let rows = rows
                .into_iter()
                .map(|row| Ok(Cow::Owned(row?)))
                .collect::<Result<Vec<_>>>()?;

            perform_groupby(
                &ctx,
                &mut buckets,
                &rows,
                &dimensions,
                &pivot,
                &aggregator_base,
            )?;
        }

        Self::finish(
            &ctx,
            &aggregator_base,
            expr_buckets_to_value_buckets(buckets),
            &dimensions,
            &pivot,
            &measures,
            &aggregates,
            tx,
        )
        .await?;

        Ok(())
    }
}

impl GroupByQuery {
    // Ideally this would be a separate operator, but because all operators must emit Values right now, this is
    // easier to implement.
    pub async fn finish(
        ctx: &InterpreterContext,
        aggregator_base: &Vec<ExprAggregator>,
        buckets: HashMap<Vec<Value>, Vec<ValueAggregator>>,
        dimensions: &[Alias],
        pivot: &[Alias],
        measures: &[Alias],
        aggregates: &[(String, impl Aggregator)],
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        if buckets.is_empty() && dimensions.is_empty() && pivot.is_empty() {
            let mut row = serde_json::Map::new();
            add_measures(ctx, &mut row, &aggregates, &aggregator_base, &measures)?;
            match send_row(&tx, Value::Object(row)).await {
                ShouldAbort::Continue => {}
                ShouldAbort::Abort => {
                    log::debug!("Aborting groupby since the parent stream was cancelled");
                }
            }
        }

        if pivot.is_empty() {
            for (bucket, aggs) in buckets.into_iter() {
                let mut row = serde_json::Map::new();
                for (dim, value) in dimensions.iter().zip(bucket.into_iter()) {
                    row.insert(dim.alias.clone(), value);
                }

                add_measures(&ctx, &mut row, &aggregates, &aggs, &measures)?;

                match send_row(&tx, Value::Object(row)).await {
                    ShouldAbort::Continue => {}
                    ShouldAbort::Abort => {
                        log::debug!("Aborting groupby since the parent stream was cancelled");
                        break;
                    }
                }
            }
        } else {
            let mut dim_buckets: HashMap<Vec<Value>, BTreeMap<Vec<_>, Vec<_>>> = HashMap::new();
            for (mut bucket, aggs) in buckets.into_iter() {
                // Split the bucket into dims and pivots at dimensions.len()
                let pivot_bucket = bucket
                    .split_off(dimensions.len())
                    .into_iter()
                    .map(|v| SortValue(v))
                    .collect::<Vec<_>>();
                dim_buckets
                    .entry(bucket)
                    .or_insert_with(BTreeMap::new)
                    .insert(pivot_bucket, aggs);
            }

            // Then, for each dimension, send the pivoted rows
            for (dim_values, pivot_buckets) in dim_buckets.into_iter() {
                let mut row = serde_json::Map::new();

                // Add dimension values to the row
                for (dim, value) in dimensions.iter().zip(dim_values.into_iter()) {
                    row.insert(dim.alias.clone(), value);
                }

                // Create nested pivot structure
                let mut pivot_values: PivotBucket = PivotBucket {
                    pivot_values: HashMap::new(),
                    aggregators: aggregator_base.iter().map(|a| a.agg.clone()).collect(),
                }; // We need to partially aggregate before we convert into Values
                for (pivot_bucket, aggs) in pivot_buckets.into_iter() {
                    let mut current = &mut pivot_values;
                    for pivot_value in pivot_bucket.into_iter() {
                        for (agg_idx, agg) in aggs.iter().enumerate() {
                            current.aggregators[agg_idx].combine(agg.clone())?;
                        }

                        current = current
                            .pivot_values
                            .entry(format_pivot_value(&pivot_value.0))
                            .or_insert_with(|| PivotBucket {
                                pivot_values: HashMap::new(),
                                aggregators: aggregator_base
                                    .iter()
                                    .map(|a| a.agg.clone())
                                    .collect(),
                            });
                    }
                    for (agg_idx, agg) in aggs.iter().enumerate() {
                        current.aggregators[agg_idx].combine(agg.clone())?;
                    }
                }

                // Add pivot structure to main row
                if !pivot.is_empty() {
                    row.extend(pivot_bucket_to_value(
                        &ctx,
                        pivot_values,
                        &aggregates,
                        &pivot,
                        &measures,
                    )?);
                }

                match send_row(&tx, Value::Object(row)).await {
                    ShouldAbort::Continue => {}
                    ShouldAbort::Abort => {
                        log::debug!("Aborting groupby since the parent stream was cancelled");
                        break;
                    }
                }
            }
        }

        Ok(())
    }
}

#[derive(Debug)]
pub struct PivotBucket {
    pub pivot_values: HashMap<String, PivotBucket>,
    pub aggregators: Vec<ValueAggregator>,
}

pub fn pivot_bucket_to_value(
    ctx: &InterpreterContext,
    bucket: PivotBucket,
    aggregates: &[(String, impl Aggregator)],
    pivot: &[Alias],
    measures: &[Alias],
) -> Result<serde_json::Map<String, Value>> {
    let mut row = serde_json::Map::new();
    add_measures(&ctx, &mut row, &aggregates, &bucket.aggregators, &measures)?;
    let mut children = serde_json::Map::new();
    for (k, v) in bucket.pivot_values.into_iter() {
        children.insert(
            k,
            Value::Object(pivot_bucket_to_value(
                ctx,
                v,
                aggregates,
                pivot[1..].as_ref(),
                measures,
            )?),
        );
    }
    debug_assert!(
        children.is_empty() || !pivot.is_empty(),
        "If there are children, we should have pivot fields"
    );
    // The leaf rows get the values directly
    if !pivot.is_empty() {
        row.insert(pivot[0].alias.to_string(), Value::Object(children));
    }
    Ok(row)
}

pub fn format_pivot_value(value: &Value) -> String {
    match value {
        Value::String(s) => s.clone(),
        _ => value.to_string(),
    }
}

#[inline]
fn add_measures(
    ctx: &InterpreterContext,
    row: &mut serde_json::Map<String, Value>,
    aggregates: &[(String, impl Aggregator)],
    aggs: &[impl Aggregator],
    measures: &[Alias],
) -> Result<()> {
    let mut agg_row = serde_json::Map::new();
    for ((agg_name, _), agg) in aggregates.iter().zip(aggs.iter()) {
        agg_row.insert(agg_name.clone(), agg.collect()?);
    }
    let agg_row = Value::Object(agg_row);
    for measure in measures.iter() {
        row.insert(
            measure.alias.clone(),
            interpret_expr_value(&ctx.expr_ctx, &measure.expr, &agg_row)?.into_owned(),
        );
    }
    Ok(())
}

#[inline]
pub fn perform_groupby(
    ctx: &InterpreterContext,
    buckets: &mut HashMap<Vec<Value>, Vec<ExprAggregator>>,
    rows: &[Cow<Value>],
    dimensions: &[Alias],
    pivot: &[Alias],
    aggregator_base: &[ExprAggregator],
) -> Result<()> {
    let dimension_values_per_dimension = dimensions
        .iter()
        .chain(pivot.iter())
        .map(|a| interpret_expr(&ctx.expr_ctx, &a.expr, rows))
        .collect::<Result<Vec<_>, TypesystemError>>()?;

    let mut dimension_rows = vec![vec![Value::Null; dimensions.len() + pivot.len()]; rows.len()];

    for (dim_idx, dim_values) in dimension_values_per_dimension.into_iter().enumerate() {
        for (row_idx, dim_value) in dim_values.into_iter().enumerate() {
            dimension_rows[row_idx][dim_idx] = dim_value.into_owned();
        }
    }

    let ctx = ColumnarExprContext::from_interpreter_ctx(ctx);

    for (bucket, row) in dimension_rows.into_iter().zip(rows) {
        let aggs = buckets
            .entry(bucket)
            .or_insert_with(|| aggregator_base.iter().map(|a| a.clone()).collect());

        for agg in aggs.iter_mut() {
            agg.aggregate_value(&ctx, Cow::Borrowed(row))?;
        }
    }

    Ok(())
}

pub fn expr_buckets_to_value_buckets(
    buckets: HashMap<Vec<Value>, Vec<ExprAggregator>>,
) -> HashMap<Vec<Value>, Vec<ValueAggregator>> {
    buckets
        .into_iter()
        .map(|(dimensions, aggregators)| {
            (
                dimensions,
                aggregators
                    .into_iter()
                    .map(|agg| agg.into_value_aggregator())
                    .collect(),
            )
        })
        .collect()
}
