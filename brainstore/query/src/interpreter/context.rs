use agent::util::derive_trace_id;
use arc_swap::ArcSwapOption;
use btql::{
    interpreter::context::ExprContext,
    util::{Cursor, CursorDirection, CursorValue},
};
use clap::Parser;
use serde::{Deserialize, Serialize};
use std::{
    collections::{BTreeMap, HashMap},
    sync::{
        atomic::{AtomicBool, AtomicU64, AtomicU8},
        Arc,
    },
};
use storage::{config_with_store::ConfigWithStore, index_wal_reader::IndexWalReaderOpts};

use util::tracer::TracedNode;

use super::error::{InterpreterError, Result};

pub use tantivy::Executor;

pub const DEFAULT_BATCH_SIZE: usize = 10000;
pub const EMPTY_CURSOR_VALUE: CursorValue = u64::MAX;
// If set, explicitly sets the truncation cursor to null which overrides the normal cursor
pub const NULL_TRUNCATION_CURSOR_VALUE: CursorValue = u64::MAX - 1;

#[derive(Debug, <PERSON><PERSON>, Parse<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct InterpreterOpts {
    #[arg(
        short,
        long,
        env = "BRAINSTORE_BATCH_SIZE",
        help = format!("The number of rows to fetch in a single batch (defaults to {})", DEFAULT_BATCH_SIZE)
    )]
    pub batch_size: Option<usize>,

    #[command(flatten)]
    #[serde(flatten)]
    pub index_wal_reader_opts: IndexWalReaderOpts,

    #[arg(long, allow_hyphen_values = true)]
    #[serde(default)]
    pub tz_offset: Option<i16>,

    #[arg(
        long,
        default_value_t = default_query_timeout_seconds(),
        help = format!("The timeout for a query (defaults to {})", default_query_timeout_seconds()),
    )]
    #[serde(default = "default_query_timeout_seconds")]
    pub query_timeout_seconds: usize,
}

pub fn default_query_timeout_seconds() -> usize {
    32 // 32 seconds, just higher than the API gateway 30s timeout
}

impl Default for InterpreterOpts {
    fn default() -> Self {
        Self {
            batch_size: None,
            index_wal_reader_opts: IndexWalReaderOpts::default(),
            tz_offset: None,
            query_timeout_seconds: default_query_timeout_seconds(),
        }
    }
}

impl InterpreterOpts {
    pub fn batch_size(&self) -> usize {
        self.batch_size.unwrap_or(DEFAULT_BATCH_SIZE)
    }
}

pub struct InterpreterContextState {
    pub index_wal_reader: storage::index_wal_reader::IndexWalReader,
    pub tantivy_schema: tantivy::schema::Schema,
    pub tantivy_index: tantivy::Index,
    pub tantivy_readers: Vec<tantivy::IndexReader>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ModelCosts {
    pub input_cost_per_mil_tokens: Option<f64>,
    pub output_cost_per_mil_tokens: Option<f64>,
    pub input_cache_read_cost_per_mil_tokens: Option<f64>,
    pub input_cache_write_cost_per_mil_tokens: Option<f64>,
}

pub type ModelCostsMap = HashMap<String, ModelCosts>;

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[repr(u8)] // Ensures enum values fit in a u8
pub enum CancellationStatus {
    Running = 0,
    Timeout = 1,
    Cancelled = 2,
}

impl From<u8> for CancellationStatus {
    fn from(value: u8) -> Self {
        match value {
            _ if value == CancellationStatus::Running as u8 => CancellationStatus::Running,
            _ if value == CancellationStatus::Timeout as u8 => CancellationStatus::Timeout,
            _ if value == CancellationStatus::Cancelled as u8 => CancellationStatus::Cancelled,
            _ => panic!("Unknown cancellation reason: {}", value),
        }
    }
}

pub struct InterpreterContext {
    pub handle: tokio::runtime::Handle,

    pub config: ConfigWithStore,
    pub schema: util::schema::Schema,

    pub executor: Arc<Executor>,
    pub opts: InterpreterOpts,
    pub root_tracer: Arc<TracedNode>,
    pub expr_ctx: ExprContext,

    pub state: ArcSwapOption<InterpreterContextState>,
    pub cursor: AtomicU64,
    // Overrides cursor if set
    pub truncation_cursor: AtomicU64,

    pub cancelled: AtomicU8,
    // This lets us do async things that we want to when we start running the query,
    // without forcing the constructor to be async
    pub started: AtomicBool,

    pub model_costs: Arc<Option<ModelCostsMap>>,

    pub metric_scope: Arc<BTreeMap<String, String>>,

    // This is for testing and collapses numbers/integers
    pub inference_collapse_numbers: bool,
}

impl InterpreterContext {
    pub fn new(
        config: ConfigWithStore,
        schema: util::schema::Schema,
        executor: Arc<Executor>,
        handle: tokio::runtime::Handle,
        opts: InterpreterOpts,
        expr_ctx: ExprContext,
        model_costs: Option<ModelCostsMap>,
        metric_scope: Option<BTreeMap<String, String>>,
    ) -> Arc<Self> {
        Arc::new(Self {
            config,
            schema,
            executor,
            handle,
            opts,
            root_tracer: TracedNode::new(format!(
                "Executed query{}",
                derive_trace_id(&tracing::Span::current())
                    .map_or_else(|| "".to_string(), |id| format!(" (trace id: {})", id))
            )),
            expr_ctx,
            state: ArcSwapOption::new(None),
            cursor: AtomicU64::new(EMPTY_CURSOR_VALUE),
            truncation_cursor: AtomicU64::new(EMPTY_CURSOR_VALUE),
            model_costs: Arc::new(model_costs),
            inference_collapse_numbers: false,
            metric_scope: Arc::new(metric_scope.unwrap_or_default()),
            cancelled: AtomicU8::new(0u8),
            started: AtomicBool::new(false),
        })
    }

    pub fn start(self: &Arc<Self>) {
        let prev = self.started.compare_exchange(
            false,
            true,
            std::sync::atomic::Ordering::Relaxed,
            std::sync::atomic::Ordering::Relaxed,
        );
        if prev.is_ok() {
            let ctx = self.clone();
            tokio::spawn(async move {
                tokio::time::sleep(std::time::Duration::from_secs(
                    ctx.opts.query_timeout_seconds as u64,
                ))
                .await;
                ctx.cancel(CancellationStatus::Timeout);
            });
        }
    }

    pub fn cancel(&self, reason: CancellationStatus) -> bool {
        self.cancelled
            .compare_exchange(
                CancellationStatus::Running as u8,
                reason as u8,
                std::sync::atomic::Ordering::Relaxed,
                std::sync::atomic::Ordering::Relaxed,
            )
            .is_ok()
    }

    pub fn is_cancelled(&self) -> CancellationStatus {
        self.cancelled
            .load(std::sync::atomic::Ordering::Relaxed)
            .into()
    }

    pub fn check_cancelled(&self) -> Result<()> {
        let status = self.is_cancelled();
        if status != CancellationStatus::Running {
            Err(InterpreterError::Cancelled { reason: status })
        } else {
            Ok(())
        }
    }

    pub fn get_tokenizer(&self, field: &str) -> Option<tantivy::tokenizer::TextAnalyzer> {
        self.expr_ctx.get_tokenizer(field)
    }

    pub fn finish(&self) -> Arc<TracedNode> {
        self.root_tracer.end();
        self.root_tracer.clone()
    }

    pub(crate) fn must_get_index_state(&self) -> Result<Arc<InterpreterContextState>> {
        let state = self.state.load();
        match &*state {
            Some(state) => Ok(state.clone()),
            None => Err(InterpreterError::InternalError(
                "should have initialized index state".to_string(),
            )),
        }
    }

    pub(crate) fn set_index_state(&self, state: InterpreterContextState) {
        self.state.store(Some(Arc::new(state)));
    }

    pub(crate) fn update_cursor(&self, cursor: &Cursor, direction: CursorDirection) {
        let _ = self.cursor.fetch_update(
            std::sync::atomic::Ordering::Relaxed,
            std::sync::atomic::Ordering::Relaxed,
            |current| match (current, direction) {
                (EMPTY_CURSOR_VALUE, _) => Some(cursor.cursor_value),
                (current, CursorDirection::Min) => Some(cursor.cursor_value.min(current)),
                (current, CursorDirection::Max) => Some(cursor.cursor_value.max(current)),
            },
        );
    }

    pub(crate) fn set_truncation_cursor(&self, cursor: &Cursor) {
        self.truncation_cursor
            .store(cursor.cursor_value, std::sync::atomic::Ordering::Release);
    }

    pub fn get_cursor(&self) -> Option<Cursor> {
        let cursor_val = self.cursor.load(std::sync::atomic::Ordering::Relaxed);
        let truncation_cursor_val = self
            .truncation_cursor
            .load(std::sync::atomic::Ordering::Acquire);

        match (truncation_cursor_val, cursor_val) {
            (EMPTY_CURSOR_VALUE, EMPTY_CURSOR_VALUE) => None,
            (NULL_TRUNCATION_CURSOR_VALUE, _) => None,
            (EMPTY_CURSOR_VALUE, cursor_value) => Some(Cursor { cursor_value }),
            (cursor_value, _) => Some(Cursor { cursor_value }),
        }
    }

    pub fn testing_set_inference_collapse_numbers(&mut self) {
        self.inference_collapse_numbers = true;
    }
}
