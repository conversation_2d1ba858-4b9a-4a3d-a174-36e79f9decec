use std::{borrow::Cow, cmp::Ordering, pin::Pin, sync::Arc};

use btql::{
    binder::ast::<PERSON><PERSON>,
    interpreter::{
        context::ExprContext,
        expr::{interpret_expr, order_values, perform_equals},
    },
    parser::json_ast::SortDirection,
    util::{<PERSON>ursor, CursorValue},
};
use futures::stream::ReadyChunks;
use tokio::sync::mpsc;
use tokio_stream::{Stream, StreamExt};
use util::{tracer::TracedNode, Value};

use crate::{
    interpreter::context::NULL_TRUNCATION_CURSOR_VALUE,
    optimizer::ast::{CursorField, OptimizedSortItem},
    planner::ast::ProjectQuery,
};

use super::{
    error::{InterpreterError, Result},
    local::interpret_expr_value,
    op::{send_row, Operator, ShouldAbort, StreamValue},
    tantivy::{
        expand::{is_slippery_cursor_field, truncate_with_cursor, Limiter, LimiterState},
        search::extract_cursor_value_from_row,
    },
    InterpreterContext,
};

#[async_trait::async_trait]
impl Operator for ProjectQuery {
    fn name(&self) -> &'static str {
        "Project"
    }

    async fn execute(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        let ProjectQuery {
            from,
            limit,
            sort,
            projection,
            cursor_field,
            is_top_level_limiter,
        } = self;

        let from_stream = from.interpret_node(ctx.clone(), tracer.clone())?;

        let batched_stream = futures::StreamExt::ready_chunks(from_stream, ctx.opts.batch_size());

        let effective_limit = match limit {
            Some(limit) => limit as i64,
            None => i64::MAX,
        };

        let project_batch = |rows: Vec<Value>| -> Result<Vec<Value>> {
            Ok(match &projection {
                Some(projection) => {
                    let cow_rows = rows
                        .into_iter()
                        .map(|row| Cow::Owned(row))
                        .collect::<Vec<_>>();
                    project_batch(&ctx.expr_ctx, projection, cow_rows)?
                }
                None => rows,
            })
        };

        match (limit, sort.len()) {
            (_, 0) => {
                limit_stream(
                    batched_stream,
                    effective_limit,
                    cursor_field,
                    ctx.clone(),
                    project_batch,
                    &tx,
                    is_top_level_limiter,
                )
                .await
            }
            (None, _) => {
                sort_full_result(
                    ctx.clone(),
                    batched_stream,
                    sort,
                    cursor_field,
                    project_batch,
                    &tx,
                    is_top_level_limiter,
                )
                .await
            }
            (Some(limit), _) => {
                sort_limited_result(
                    ctx.clone(),
                    batched_stream,
                    sort,
                    limit as i64,
                    cursor_field,
                    project_batch,
                    &tx,
                    is_top_level_limiter,
                )
                .await
            }
        }
    }
}

type RowBatchStream<'a> =
    ReadyChunks<Pin<Box<dyn Stream<Item = Result<Value, InterpreterError>> + Send>>>;

async fn limit_stream_slippery<'a>(
    mut batched_stream: RowBatchStream<'a>,
    limit: i64,
    cursor_field: CursorField,
    ctx: Arc<InterpreterContext>,
    project_batch: impl Fn(Vec<Value>) -> Result<Vec<Value>>,
    tx: &mpsc::Sender<StreamValue>,
    is_top_level_limiter: bool,
) -> Result<()> {
    let mut limiter = Limiter {
        cursor: None,
        state: LimiterState::NumRemaining(limit as usize),
    };

    while let Some(batch) = batched_stream.next().await {
        let mut rows_with_cursor = batch
            .into_iter()
            .map(|row| {
                let row = row?;
                let cursor_value = extract_cursor_value_from_row(
                    &Cow::Borrowed(&row),
                    cursor_field,
                    true, /*flatten*/
                )
                .ok();
                Ok((row, cursor_value))
            })
            .collect::<Result<Vec<_>>>()?;

        truncate_with_cursor(&mut rows_with_cursor, &mut limiter, true);

        if is_top_level_limiter {
            // If there's at least one row, set the truncation cursor to the last row's cursor value.
            if let Some((_, last_row_cursor_value)) = rows_with_cursor.last() {
                limiter.cursor = *last_row_cursor_value;
            }
        }

        let rows = rows_with_cursor
            .into_iter()
            .map(|(row, _)| row)
            .collect::<Vec<_>>();
        let projected_rows = project_batch(rows)?;

        for row in projected_rows {
            match send_row(tx, row).await {
                ShouldAbort::Continue => {}
                ShouldAbort::Abort => {
                    log::debug!("Aborting limiter since the parent stream was cancelled");
                    return Ok(());
                }
            }
        }

        if limiter.state == LimiterState::NumRemaining(0) {
            break;
        }
    }

    if is_top_level_limiter {
        ctx.set_truncation_cursor(&Cursor::new(
            limiter.cursor.unwrap_or(NULL_TRUNCATION_CURSOR_VALUE),
        ));
    }

    Ok(())
}

async fn limit_stream_simple<'a>(
    mut batched_stream: RowBatchStream<'a>,
    limit: i64,
    cursor_field: Option<CursorField>,
    ctx: Arc<InterpreterContext>,
    project_batch: impl Fn(Vec<Value>) -> Result<Vec<Value>>,
    tx: &mpsc::Sender<StreamValue>,
    is_top_level_limiter: bool,
) -> Result<()> {
    let mut truncation_cursor = NULL_TRUNCATION_CURSOR_VALUE;

    let mut remaining_rows = limit;
    while let Some(rows) = batched_stream.next().await {
        let mut rows = rows.into_iter().collect::<Result<Vec<_>>>()?;

        if remaining_rows < rows.len() as i64 {
            let truncation_idx = remaining_rows as usize;
            rows.truncate(truncation_idx);
        }

        if is_top_level_limiter {
            // If there's at least one row, set the truncation cursor to the last row's cursor value.
            match (cursor_field, rows.last()) {
                (Some(cursor_field), Some(last_row)) => {
                    let last_cursor_value = extract_cursor_value_from_row(
                        &Cow::Borrowed(&last_row),
                        cursor_field,
                        true, /*flatten*/
                    )
                    .ok();
                    if let Some(last_cursor_value) = last_cursor_value {
                        truncation_cursor = last_cursor_value;
                    }
                }
                _ => {}
            }
        }

        let projected_rows = project_batch(rows)?;
        let num_rows = projected_rows.len();

        for row in projected_rows {
            match send_row(tx, row).await {
                ShouldAbort::Continue => {}
                ShouldAbort::Abort => {
                    log::debug!("Aborting limiter since the parent stream was cancelled");
                    return Ok(());
                }
            }
        }

        remaining_rows -= num_rows as i64;
        if remaining_rows <= 0 {
            break;
        }
    }

    if is_top_level_limiter {
        ctx.set_truncation_cursor(&Cursor::new(truncation_cursor));
    }

    Ok(())
}

async fn limit_stream<'a>(
    batched_stream: RowBatchStream<'a>,
    limit: i64,
    cursor_field: Option<CursorField>,
    ctx: Arc<InterpreterContext>,
    project_batch: impl Fn(Vec<Value>) -> Result<Vec<Value>>,
    tx: &mpsc::Sender<StreamValue>,
    is_top_level_limiter: bool,
) -> Result<()> {
    // Explicitly set the truncation cursor to empty. This will be updated later
    // with the last row's cursor value if we get a non-empty result set.
    match cursor_field {
        Some(cursor_field) if is_slippery_cursor_field(cursor_field) => {
            limit_stream_slippery(
                batched_stream,
                limit,
                cursor_field,
                ctx,
                project_batch,
                tx,
                is_top_level_limiter,
            )
            .await?
        }
        _ => {
            // If we don't have a slippery cursor field, we can do simple truncation.
            limit_stream_simple(
                batched_stream,
                limit,
                cursor_field,
                ctx,
                project_batch,
                tx,
                is_top_level_limiter,
            )
            .await?
        }
    }

    Ok(())
}

pub fn make_key_fn<'a>(
    ctx: Arc<InterpreterContext>,
    sort: &'a [OptimizedSortItem],
) -> impl Fn(&Value) -> Vec<SortValueDir> + 'a {
    move |row| {
        sort.iter()
            .map(|sort_item| {
                let value = match interpret_expr_value(&ctx.expr_ctx, &sort_item.expr, &row) {
                    Ok(value) => value.into_owned(),
                    Err(e) => {
                        log::warn!("Error interpreting sort value: {:?}", e);
                        Value::Null
                    }
                };
                SortValueDir {
                    key: SortValue(value),
                    dir: sort_item.dir.clone(),
                }
            })
            .collect::<Vec<SortValueDir>>()
    }
}

async fn sort_full_result<'a>(
    ctx: Arc<InterpreterContext>,
    mut batched_stream: RowBatchStream<'a>,
    sort: Vec<OptimizedSortItem>,
    cursor_field: Option<CursorField>,
    project_batch: impl Fn(Vec<Value>) -> Result<Vec<Value>>,
    tx: &mpsc::Sender<StreamValue>,
    is_top_level_limiter: bool,
) -> Result<()> {
    let mut rows = Vec::new();

    while let Some(batch) = batched_stream.next().await {
        for row in batch {
            rows.push(row?);
        }
    }

    rows.sort_by_cached_key(make_key_fn(ctx.clone(), &sort));

    if is_top_level_limiter {
        if let Some(cursor_field) = cursor_field {
            let cursor = rows
                .last()
                .and_then(|row| {
                    extract_cursor_value_from_row(&Cow::Borrowed(row), cursor_field, true).ok()
                })
                .unwrap_or(NULL_TRUNCATION_CURSOR_VALUE);
            ctx.set_truncation_cursor(&Cursor::new(cursor));
        }
    }

    for row in project_batch(rows)? {
        match send_row(tx, row).await {
            ShouldAbort::Continue => {}
            ShouldAbort::Abort => {
                // TODO: The better thing to do here is to check a cancelled signal to make sure
                // the query actually ended, rather than some error case.
                log::debug!("Aborting sort since the parent stream was cancelled");
                return Ok(());
            }
        }
    }

    Ok(())
}

fn truncate_with_unique_cursor<T>(rows: &mut Vec<(T, Option<CursorValue>)>, limit: usize) {
    if limit == 0 {
        rows.truncate(0);
        return;
    } else if limit > rows.len() {
        return;
    }

    let mut truncation_idx = limit;
    let truncation_cursor = rows[truncation_idx - 1].1;

    while truncation_idx < rows.len() && rows[truncation_idx].1 == truncation_cursor {
        truncation_idx += 1;
    }

    rows.truncate(truncation_idx);
}

async fn sort_limited_result<'a>(
    ctx: Arc<InterpreterContext>,
    mut batched_stream: RowBatchStream<'a>,
    sort: Vec<OptimizedSortItem>,
    limit: i64,
    cursor_field: Option<CursorField>,
    project_batch: impl Fn(Vec<Value>) -> Result<Vec<Value>>,
    tx: &mpsc::Sender<StreamValue>,
    is_top_level_limiter: bool,
) -> Result<()> {
    let mut rows = Vec::new();

    while let Some(batch) = batched_stream.next().await {
        for row in batch {
            rows.push(row?);
        }
    }

    rows.sort_by_cached_key(make_key_fn(ctx.clone(), &sort));

    match cursor_field {
        Some(cursor_field) => {
            let mut rows_with_cursor = rows
                .into_iter()
                .map(|row| {
                    let cursor_value = extract_cursor_value_from_row(
                        &Cow::Borrowed(&row),
                        cursor_field,
                        true, /*flatten*/
                    )
                    .ok();
                    (row, cursor_value)
                })
                .collect::<Vec<_>>();

            // TODO: The better implementation is to use a heap here.
            truncate_with_unique_cursor(&mut rows_with_cursor, limit as usize);

            if is_top_level_limiter {
                // If there's at least one row, set the truncation cursor to the last row's cursor value.
                let truncation_cursor = match rows_with_cursor.last() {
                    Some((_, Some(last_row_cursor_value))) => *last_row_cursor_value,
                    _ => NULL_TRUNCATION_CURSOR_VALUE,
                };
                ctx.set_truncation_cursor(&Cursor::new(truncation_cursor));
            }

            rows = rows_with_cursor
                .into_iter()
                .map(|(row, _)| row)
                .collect::<Vec<_>>();
        }
        None => {
            rows.truncate(limit as usize);
        }
    }

    for row in project_batch(rows)? {
        send_row(&tx, row).await;
    }

    Ok(())
}

#[derive(Debug, Clone)]
pub struct SortValueDir {
    pub key: SortValue,
    pub dir: SortDirection,
}

impl PartialEq for SortValueDir {
    fn eq(&self, other: &Self) -> bool {
        self.key == other.key
    }
}

impl PartialOrd for SortValueDir {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        match self.dir {
            SortDirection::Asc => self.key.partial_cmp(&other.key),
            SortDirection::Desc => other.key.partial_cmp(&self.key),
        }
    }
}

impl Eq for SortValueDir {}

impl Ord for SortValueDir {
    fn cmp(&self, other: &Self) -> Ordering {
        self.partial_cmp(other).unwrap_or(Ordering::Equal)
    }
}

#[derive(Debug, Clone, Hash)]
pub struct SortValue(pub Value);

impl PartialEq for SortValue {
    fn eq(&self, other: &Self) -> bool {
        perform_equals(&self.0, &other.0).unwrap_or(false)
    }
}

impl Eq for SortValue {}

impl PartialOrd for SortValue {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        order_values(&self.0, &other.0)
    }
}

impl Ord for SortValue {
    fn cmp(&self, other: &Self) -> Ordering {
        let partial = self.partial_cmp(other);
        partial.unwrap_or(Ordering::Equal)
    }
}

#[inline]
pub fn project_batch(
    ctx: &ExprContext,
    projection: &Vec<Alias>,
    rows: Vec<Cow<Value>>,
) -> Result<Vec<Value>> {
    let mut final_rows = rows
        .iter()
        .map(|_| util::Value::Object(serde_json::Map::new()))
        .collect::<Vec<_>>();

    for alias in projection.iter() {
        let values = interpret_expr(&ctx, &alias.expr, &rows)?;

        for (value, row) in values.into_iter().zip(final_rows.iter_mut()) {
            row.as_object_mut()
                .unwrap()
                .insert(alias.alias.clone(), value.into_owned());
        }
    }

    Ok(final_rows)
}
