use std::{borrow::Cow, sync::Arc};

use futures::StreamExt;
use tokio::sync::mpsc;
use util::{tracer::TracedNode, Value};

use crate::{
    optimizer::ast::{UnpivotProjectedField, UnpivotProjectionExpr},
    planner::ast::UnpivotQuery,
};
use btql::typesystem::cast::value_is_null;

use super::{
    error::{InterpreterError, Result},
    op::{send_row, Operator, ShouldAbort, StreamValue},
    InterpreterContext,
};

#[async_trait::async_trait]
impl Operator for UnpivotQuery {
    fn name(&self) -> &'static str {
        "Unpivot"
    }

    async fn execute(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        let UnpivotQuery { from, unpivot } = self;

        let from_stream = from.interpret_node(ctx.clone(), tracer.clone())?;

        let mut batched_stream =
            futures::StreamExt::ready_chunks(from_stream, ctx.opts.batch_size());

        while let Some(rows) = batched_stream.next().await {
            let rows = rows
                .into_iter()
                .map(|r| Ok(Cow::Owned(r?)))
                .collect::<Result<Vec<_>>>()?;
            let projected_rows = unpivot_batch(rows, &unpivot)?;

            for row in projected_rows {
                match send_row(&tx, row.into_owned()).await {
                    ShouldAbort::Continue => {}
                    ShouldAbort::Abort => {
                        return Ok(());
                    }
                }
            }
        }

        Ok(())
    }
}

pub fn unpivot_batch<'a>(
    rows: Vec<Cow<'a, Value>>,
    unpivot: &[UnpivotProjectionExpr],
) -> Result<Vec<Cow<'a, Value>>> {
    if unpivot.len() == 0 {
        return Ok(rows);
    }

    let first_unpivot = &unpivot[0];

    let mut result = vec![];
    for mut row in rows.into_iter() {
        let object = row
            .to_mut()
            .as_object_mut()
            .ok_or_else(|| InterpreterError::InternalError("Expected an object".to_string()))?;

        let mut expanded = vec![];
        let base_value = object.remove(&first_unpivot.base_field);
        match &first_unpivot.projected_field {
            UnpivotProjectedField::Array { item } => {
                let array = match base_value {
                    None | Some(Value::Null) => {
                        expanded.push(row);
                        continue;
                    }
                    Some(Value::Array(array)) => array,
                    _ => {
                        return Err(InterpreterError::InternalError(format!(
                            "Expected {} to be an array while unpivoting ({:?})",
                            first_unpivot.base_field, base_value
                        )));
                    }
                };

                for value in array.iter() {
                    let mut new_row = row.clone().into_owned();
                    new_row[item] = value.clone();
                    expanded.push(Cow::Owned(new_row));
                }
            }
            UnpivotProjectedField::Object { key, value } => {
                let object = match base_value {
                    None | Some(Value::Null) => {
                        expanded.push(row);
                        continue;
                    }
                    Some(Value::Object(object)) => object,
                    _ => {
                        return Err(InterpreterError::InternalError(format!(
                            "Expected {} to be an object while unpivoting ({:?})",
                            first_unpivot.base_field, base_value
                        )));
                    }
                };

                for (k, v) in object.iter() {
                    // Since we don't distinguish this semantically, and the tantivy columnstore won't even
                    // store an empty object value, we can skip it.
                    if value_is_null(v) {
                        continue;
                    }

                    let mut new_row = row.clone().into_owned();
                    new_row[key] = Value::String(k.to_string());
                    new_row[value] = v.clone();
                    expanded.push(Cow::Owned(new_row));
                }
            }
        };
        result.extend(unpivot_batch(expanded, &unpivot[1..])?);
    }

    Ok(result)
}
