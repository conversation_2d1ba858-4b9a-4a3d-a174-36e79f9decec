use std::sync::Arc;

use btql::{
    binder::{ast::<PERSON><PERSON>, Expr},
    util::Cursor,
};
use storage::segment_batches::{SegmentBatchingSortSpec, SegmentEliminationFilterSpec};
use util::{
    ptree::{MakePTree, TreeBuilder},
    schema::{TantivyFastValueType, TantivyType},
    system_types::FullObjectIdOwned,
};

use crate::{
    interpreter::aggregator::{columnar_aggregator::ColumnarAggregator, ExprAggregator},
    optimizer::ast::{
        CursorField, CursorFilter, OptimizedSortItem, RealtimeWALSearchQuery,
        TantivyProjectedField, UnpivotProjectionExpr,
    },
};

use crate::interpreter::columnar::expr::ColumnarExpr;

#[derive(Debug, MakePTree)]
pub enum PlannedQuery {
    Noop(NoopQuery),
    TantivySearch(TantivySearchQuery),
    TantivyAggregate(TantivyAggregateQuery),
    TantivyExpandTraces(TantivyExpandTracesQuery),
    TantivySchemaInference(TantivySchemaInferenceQuery),
    Filter(FilterQuery),
    GroupBy(GroupByQuery),
    Project(ProjectQuery),
    Unpivot(UnpivotQuery),
}

#[derive(Debug, MakePTree)]
pub struct NoopQuery {}

#[derive(Debug, MakePTree)]
pub struct FilterQuery {
    pub from: Box<PlannedQuery>,
    pub filter: Option<Box<Expr>>,
}

#[derive(Debug, MakePTree)]
pub struct GroupByQuery {
    pub from: Box<PlannedQuery>,
    pub pivot: Vec<Alias>,
    pub measures: Vec<Alias>,
    pub dimensions: Vec<Alias>,
    pub aggregates: Vec<(String, ExprAggregator)>,
}

#[derive(Debug, MakePTree)]
pub struct ProjectQuery {
    pub from: Box<PlannedQuery>,
    pub sort: Vec<OptimizedSortItem>,
    pub projection: Option<Vec<Alias>>,
    pub limit: Option<usize>,
    pub cursor_field: Option<CursorField>,
    pub is_top_level_limiter: bool,
}

#[derive(Debug, MakePTree)]
pub struct UnpivotQuery {
    pub from: Box<PlannedQuery>,
    pub unpivot: Vec<UnpivotProjectionExpr>,
}

#[derive(Debug, MakePTree)]
#[ptree("Index search plan")]
pub struct TantivySearchQuery {
    pub object_ids: Vec<FullObjectIdOwned>,
    #[ptree(fn = make_ptree_for_query)]
    pub search: Box<dyn tantivy::query::Query>,
    pub realtime_search: RealtimeWALSearchQuery,
    pub projected_fields: Vec<TantivyProjectedField>,
    pub sort: Option<(tantivy::Order, String, TantivyFastValueType)>,
    pub limit: Option<usize>,
    pub batch_size: Option<usize>,
    pub cursor: Option<CursorFilter>,
    pub columnar_projection: bool,
    pub include_reader_batch: bool,
    pub segment_filters: Vec<SegmentEliminationFilterSpec>,
    pub segment_sort: Option<SegmentBatchingSortSpec>,
}

#[derive(Debug, MakePTree)]
#[ptree("Inference field")]
pub struct TantivyInferenceField {
    pub field: String,
    pub alias: String,
    pub field_type: TantivyType,
    pub is_columnar: bool,
}

#[derive(Debug, MakePTree)]
#[ptree("Index schema inference")]
pub struct TantivySchemaInferenceQuery {
    pub object_ids: Vec<FullObjectIdOwned>,
    #[ptree(fn = make_ptree_for_query)]
    pub search: Box<dyn tantivy::query::Query>,
    pub realtime_search: RealtimeWALSearchQuery,
    pub infer: Arc<Vec<TantivyInferenceField>>,
    pub budget: Option<u64>,
    pub sort: Option<(tantivy::Order, String, TantivyFastValueType)>,
    pub limit: Option<usize>,
    pub batch_size: Option<usize>,
    pub cursor: Option<Cursor>,
    pub segment_filters: Vec<SegmentEliminationFilterSpec>,
    pub segment_sort: Option<SegmentBatchingSortSpec>,
}

#[derive(Debug, MakePTree)]
#[ptree("Index aggregate")]
pub struct TantivyAggregateQuery {
    pub object_ids: Vec<FullObjectIdOwned>,
    #[ptree(fn = make_ptree_for_query)]
    pub search: Box<dyn tantivy::query::Query>,
    pub realtime_search: RealtimeWALSearchQuery,
    pub projection: Vec<TantivyProjectedField>,

    pub group_by: GroupByStrategy,

    // These always get generated, because we need them for real-time
    pub unpivot: Vec<UnpivotProjectionExpr>,
    pub dimensions: Arc<Vec<Alias>>,
    pub expr_aggs: Vec<(String, ExprAggregator)>,

    pub measures: Vec<Alias>,
    pub pivot: Arc<Vec<Alias>>,

    pub segment_filters: Vec<SegmentEliminationFilterSpec>,
    pub expand_traces: bool,
}

#[derive(Debug, Clone, MakePTree)]
#[ptree("Group by")]
pub enum GroupByStrategy {
    Dynamic,
    NoDims {
        aggs: Vec<ColumnarAggregator>,
    },
    Dims {
        dims: Vec<ColumnarExpr>,
        aggs: Vec<ColumnarAggregator>,
    },
}

#[derive(Debug, Clone, MakePTree)]
#[ptree("Projection")]
pub enum PlannedExpansionProjection {
    Spans {
        is_root_projection: Vec<TantivyProjectedField>,
        root_projection: Vec<TantivyProjectedField>,
        root_is_columnar: bool,
        span_projection: Vec<TantivyProjectedField>,
        span_is_columnar: bool,
        realtime_projection: Vec<Alias>,
    },
    Summary {
        root_projection: Vec<TantivyProjectedField>,
        comparison_key: Box<Expr>,
        weighted_scores: Vec<Alias>,
        custom_columns: Vec<Alias>,
        post_aggregation_filter: Option<Box<Expr>>,
        preview_length: Option<usize>,
    },
}

#[derive(Debug, MakePTree)]
#[ptree("Expand traces")]
pub struct TantivyExpandTracesQuery {
    pub from: Box<PlannedQuery>,
    pub projection: PlannedExpansionProjection,
    pub limit: Option<usize>,
    pub cursor: Option<CursorFilter>,
    pub is_top_level_limiter: bool,
}

#[derive(Debug, MakePTree)]
#[ptree("Index score summary")]
pub struct TantivyScoreSummaryQuery {
    pub object_ids: Vec<FullObjectIdOwned>,
    pub root_projection: Vec<TantivyProjectedField>,
}

fn make_ptree_for_query<T: AsRef<dyn tantivy::query::Query>>(builder: &mut TreeBuilder, query: &T) {
    let query = query.as_ref();
    if let Some(_) = query.downcast_ref::<tantivy::query::AllQuery>() {
        builder.add_empty_child("AllQuery".to_string());
    } else if let Some(clauses) = query.downcast_ref::<tantivy::query::BooleanQuery>() {
        builder.begin_child("BooleanQuery".to_string());
        for (typ, clause) in clauses.clauses() {
            builder.begin_child(format!("{:?}", typ));
            make_ptree_for_query(builder, &clause);
            builder.end_child();
        }
        builder.end_child();
    } else if let Some(exists) = query.downcast_ref::<tantivy::query::ExistsQuery>() {
        builder.add_empty_child(format!("{:?}", exists));
    } else if let Some(range) = query.downcast_ref::<tantivy::query::TermQuery>() {
        builder.begin_child("TermQuery".to_string());
        builder.add_empty_child(format!("{:?}", range.term()));
        builder.end_child();
    } else if let Some(phrase) = query.downcast_ref::<tantivy::query::PhraseQuery>() {
        builder.begin_child("PhraseQuery".to_string());
        for term in phrase.phrase_terms() {
            builder.add_empty_child(format!("{:?}", term));
        }
        builder.end_child();
    } else if let Some(regex) =
        query.downcast_ref::<crate::interpreter::tantivy::query::RegexQuery>()
    {
        builder.add_empty_child(format!("{:?}", regex));
    } else if let Some(range) = query.downcast_ref::<tantivy::query::FastFieldRangeWeight>() {
        builder.add_empty_child(format!("{:?}", range));
    } else if let Some(range) = query.downcast_ref::<tantivy::query::RangeQuery>() {
        builder.add_empty_child(format!("{:?}", range));
    } else if let Some(_) = query.downcast_ref::<tantivy::query::EmptyQuery>() {
        builder.add_empty_child("EmptyQuery".to_string());
    } else {
        log::warn!("Unknown query: {:?}", query);
        builder.add_empty_child("Optimized search".to_string());
    }
}
