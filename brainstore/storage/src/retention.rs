use async_util::await_spawn_blocking;
use futures::future::try_join_all;
use std::{collections::HashMap, sync::Arc};
use tracing::{instrument, Instrument};
use util::{anyhow::Result, uuid::Uuid, xact::TransactionId};

use crate::{
    config_with_store::StoreInfo,
    global_locks_manager::GlobalLocksManager,
    global_store::{
        DeleteFromSegmentIndexStats, DeleteFromSegmentStats, DeleteFromSegmentWalStats, GlobalStore,
    },
    index_document::make_full_schema,
    limits::global_limits,
    process_wal::{update_metadata_and_field_statistics, IndexState, XACT_ID_FIELD},
    tantivy_index::{flash_tantivy_index, TantivyIndexScope, TantivyIndexWriterOpts},
    tantivy_index_wrapper::{ReadWriteTantivyIndexWrapper, ReadonlyTantivyIndexWrapper},
};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct DeleteFromSegmentsInput<'a> {
    pub segment_ids: &'a [Uuid],
    pub min_retained_xact_id: TransactionId,
    pub index_store: &'a StoreInfo,
    pub schema: &'a util::schema::Schema,
    pub global_store: Arc<dyn GlobalStore>,
    pub locks_manager: &'a dyn GlobalLocksManager,
    pub dry_run: bool,
}

#[derive(Default, Debug)]
pub struct DeleteFromSegmentsOptionalInput {
    pub testing_skip_index_deletion: bool,
    pub testing_skip_field_statistics: bool,
}

#[derive(Default)]
pub struct DeleteFromSegmentsOptions {
    pub index_options: DeleteFromSegmentIndexOptions,
}

/// Purges entries for multiple segments based on their minimum retained transaction IDs
///
/// This function handles two operations:
/// 1. Purging segment WAL entries up to the specified min xact_id (done in one batch).
/// 2. Purging segment index documents up to the min xact_id (done for each segment in parallel with write locks). This step requires taking segment write locks.
#[instrument(skip(input, optional_input, options), fields(segment_ids = ?input.segment_ids, min_retained_xact_id = ?input.min_retained_xact_id))]
pub async fn delete_from_segments_up_to_xact_id(
    input: DeleteFromSegmentsInput<'_>,
    optional_input: DeleteFromSegmentsOptionalInput,
    options: DeleteFromSegmentsOptions,
) -> Result<DeleteFromSegmentStats> {
    if input.segment_ids.is_empty() {
        return Ok(DeleteFromSegmentStats::default());
    }

    let wal_stats = delete_from_segment_wal(DeleteFromSegmentWalInput {
        segment_ids: input.segment_ids,
        global_store: input.global_store.clone(),
        min_retained_xact_id: input.min_retained_xact_id,
        dry_run: input.dry_run,
    })
    .await?;

    let index_stats = if optional_input.testing_skip_index_deletion {
        DeleteFromSegmentIndexStats::default()
    } else {
        try_join_all(input.segment_ids.iter().map(|&segment_id| {
            let index_store = &input.index_store;
            let schema = &input.schema;
            let global_store = input.global_store.clone();
            let locks_manager = input.locks_manager;
            let min_retained_xact_id = input.min_retained_xact_id;
            let index_options = options.index_options.clone();
            let dry_run = input.dry_run;

            async move {
                delete_from_segment_index(
                    DeleteFromSegmentIndexInput {
                        segment_id,
                        index_store,
                        schema,
                        global_store,
                        locks_manager,
                        min_retained_xact_id,
                        dry_run,
                    },
                    DeleteFromSegmentIndexOptionalInput {
                        testing_skip_field_statistics: optional_input.testing_skip_field_statistics,
                    },
                    index_options,
                )
                .await
            }
        }))
        .await?
        .into_iter()
        .sum()
    };

    Ok(DeleteFromSegmentStats {
        wal_stats,
        index_stats,
    })
}

struct DeleteFromSegmentWalInput<'a> {
    pub segment_ids: &'a [Uuid],
    pub global_store: Arc<dyn GlobalStore>,
    pub min_retained_xact_id: TransactionId,
    pub dry_run: bool,
}

async fn delete_from_segment_wal(
    input: DeleteFromSegmentWalInput<'_>,
) -> Result<DeleteFromSegmentWalStats> {
    if input.dry_run {
        Ok(DeleteFromSegmentWalStats {
            planned_num_deleted_wal_entries: input
                .global_store
                .count_segment_wal_entries_up_to_xact_id(
                    &input.segment_ids,
                    input.min_retained_xact_id,
                )
                .await?,
            num_deleted_wal_entries: 0,
        })
    } else {
        let num_deleted_files = input
            .global_store
            .delete_segment_wal_entries_up_to_xact_id(
                &input.segment_ids,
                input.min_retained_xact_id,
            )
            .await?;
        Ok(DeleteFromSegmentWalStats {
            planned_num_deleted_wal_entries: num_deleted_files,
            num_deleted_wal_entries: num_deleted_files,
        })
    }
}

pub struct DeleteFromSegmentIndexInput<'a> {
    pub segment_id: Uuid,
    pub index_store: &'a StoreInfo,
    pub schema: &'a util::schema::Schema,
    pub global_store: Arc<dyn GlobalStore>,
    pub locks_manager: &'a dyn GlobalLocksManager,
    pub min_retained_xact_id: TransactionId,
    pub dry_run: bool,
}

#[derive(Default)]
pub struct DeleteFromSegmentIndexOptionalInput {
    pub testing_skip_field_statistics: bool,
}

#[derive(Default, Clone)]
pub struct DeleteFromSegmentIndexOptions {
    pub writer_opts: TantivyIndexWriterOpts,
}

pub async fn delete_from_segment_index(
    input: DeleteFromSegmentIndexInput<'_>,
    optional_input: DeleteFromSegmentIndexOptionalInput,
    options: DeleteFromSegmentIndexOptions,
) -> Result<DeleteFromSegmentIndexStats> {
    let field_statistics = input
        .global_store
        .query_field_statistics(&[input.segment_id], &[XACT_ID_FIELD])
        .await?;
    let min_xact_id = field_statistics
        .get(&input.segment_id)
        .and_then(|stats| stats.get(XACT_ID_FIELD))
        .map(|stat| stat.min());
    if let Some(min_xact_id) = min_xact_id {
        if input.min_retained_xact_id <= TransactionId(min_xact_id) {
            return Ok(DeleteFromSegmentIndexStats::default());
        }
    }

    // Technically this is an estimate of the actual number of deletes because
    // we haven't taken a write lock yet.
    let estimated_num_deletes = count_segment_index_docs_lt_xact_id(
        input.segment_id,
        &input.index_store,
        &input.schema,
        input.global_store.clone(),
        input.min_retained_xact_id,
    )
    .await?;
    if estimated_num_deletes == 0 {
        return Ok(DeleteFromSegmentIndexStats::default());
    }

    if input.dry_run {
        log::info!(
            "Dry run: would purge {} documents for segment {} (xact_id < {})",
            estimated_num_deletes,
            input.segment_id,
            input.min_retained_xact_id
        );
        return Ok(DeleteFromSegmentIndexStats {
            planned_num_deleted_index_docs: estimated_num_deletes,
            ..Default::default()
        });
    }

    let _permit = global_limits()
        .index_operations
        .start_task()
        .instrument(tracing::info_span!(
            "acquiring permit",
            permits = options.writer_opts.num_writer_threads
        ))
        .await?;

    let index_store = input.index_store;
    let index_scope = TantivyIndexScope::Segment(input.segment_id);
    let _lock = input.locks_manager.write(&index_scope.lock_name()).await?;

    let segment_metadatas = input
        .global_store
        .query_segment_metadatas(&[input.segment_id])
        .await;
    let global_store_last_compacted_index_meta =
        segment_metadatas?.remove(0).last_compacted_index_meta;

    if global_store_last_compacted_index_meta.is_none() {
        log::warn!(
            "Skipping purge for segment {} (missing last compacted index meta)",
            input.segment_id
        );
        return Ok(DeleteFromSegmentIndexStats::default());
    };
    let index_meta = global_store_last_compacted_index_meta.unwrap();

    flash_tantivy_index(
        Some(&index_meta.tantivy_meta),
        &input.schema,
        &index_store,
        &index_scope,
        None,
    )
    .await?;

    let tantivy_index_wrapper = ReadWriteTantivyIndexWrapper::new(
        index_store.directory.clone(),
        make_full_schema(&input.schema)?,
        &index_store.prefix,
        &index_scope,
    )
    .await?;

    let mut index_state = {
        let tantivy_index_wrapper = &tantivy_index_wrapper;
        let writer_opts = &options.writer_opts;
        await_spawn_blocking!(
            move |tantivy_index_wrapper: &ReadWriteTantivyIndexWrapper,
                  writer_opts: &TantivyIndexWriterOpts| {
                Ok::<IndexState, util::anyhow::Error>(IndexState {
                    writer: tantivy_index_wrapper.make_writer_blocking(writer_opts)?,
                    reader: tantivy_index_wrapper.make_reader_blocking()?,
                })
            },
            tantivy_index_wrapper,
            writer_opts
        )???
    };

    let index_state = &mut index_state;
    let writer_opts = &options.writer_opts;
    await_spawn_blocking!(
        move |writer_opts: &TantivyIndexWriterOpts, index_state: &mut IndexState| -> Result<()> {
            delete_index_docs_lt_xact_id(
                index_state,
                input.min_retained_xact_id,
                writer_opts,
            )
        },
        writer_opts; index_state
    )???;

    let last_xact_id = index_meta.xact_id;
    update_metadata_and_field_statistics(
        input.global_store.as_ref(),
        &tantivy_index_wrapper,
        index_state,
        input.segment_id,
        &index_store,
        &index_scope,
        &input.schema,
        &None,
        Some(index_meta),
        last_xact_id,
        optional_input.testing_skip_field_statistics,
        &options.writer_opts,
    )
    .await?;

    Ok(DeleteFromSegmentIndexStats {
        planned_num_deleted_index_docs: estimated_num_deletes,
        num_deleted_index_docs: estimated_num_deletes,
        num_write_locks: 1,
    })
}

#[instrument(err, skip(index_state), name = "delete_index_docs_lt_xact_id")]
fn delete_index_docs_lt_xact_id(
    index_state: &mut IndexState,
    min_retained_xact_id: TransactionId,
    writer_opts: &TantivyIndexWriterOpts,
) -> Result<()> {
    let lt_xact_id_query = make_tantivy_lt_xact_id_query(min_retained_xact_id.0);

    let writer = &mut index_state.writer;
    writer.delete_query(Box::new(lt_xact_id_query))?;
    writer.commit()?;
    log::info!(
        "Deleted index documents with xact_id < {}",
        min_retained_xact_id
    );

    Ok(())
}

#[instrument(
    err,
    skip(index_store, schema, global_store),
    name = "count_segment_index_docs_lt_xact_id"
)]
async fn count_segment_index_docs_lt_xact_id(
    segment_id: Uuid,
    index_store: &StoreInfo,
    schema: &util::schema::Schema,
    global_store: Arc<dyn GlobalStore>,
    min_retained_xact_id: TransactionId,
) -> Result<u64> {
    let segment_metadatas = global_store.query_segment_metadatas(&[segment_id]).await;
    let global_store_last_compacted_index_meta =
        segment_metadatas?.remove(0).last_compacted_index_meta;

    let index_meta_json = match global_store_last_compacted_index_meta {
        None => {
            log::warn!(
                "Dry run: Skipping count for segment {} (missing last compacted index meta)",
                segment_id
            );
            return Ok(0);
        }
        Some(index_meta) => index_meta.tantivy_meta,
    };

    let tantivy_index_wrapper = ReadonlyTantivyIndexWrapper::new(
        index_store.directory.clone(),
        make_full_schema(schema)?,
        &index_store.prefix,
        HashMap::from([(segment_id, index_meta_json)]),
    )
    .await?;

    let tantivy_index_wrapper = &tantivy_index_wrapper;
    let count = await_spawn_blocking!(
        move |tantivy_index_wrapper: &ReadonlyTantivyIndexWrapper| -> Result<u64> {
            let reader = tantivy_index_wrapper.make_reader_blocking()?;
            let searcher = reader.searcher();
            let count_lt_xact_id_query = make_tantivy_lt_xact_id_query(min_retained_xact_id.0);
            let num_matching_docs =
                searcher.search(&count_lt_xact_id_query, &tantivy::collector::Count {})?;
            Ok(num_matching_docs as u64)
        },
        tantivy_index_wrapper
    )???;

    Ok(count)
}

fn make_tantivy_lt_xact_id_query(min_retained_xact_id: u64) -> tantivy::query::RangeQuery {
    tantivy::query::RangeQuery::new_u64("_xact_id".to_string(), 0..min_retained_xact_id)
}
