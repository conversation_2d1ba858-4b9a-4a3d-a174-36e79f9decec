use clap::Parser;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tantivy::{
    indexer::{LogMergePolicy, MergeCandidate, MergePolicy},
    SegmentMeta,
};
use tokio::sync::mpsc;
use tracing::instrument;

use async_util::await_spawn_blocking;
use util::{
    anyhow::anyhow, global_opts::suppress_verbose_info, itertools::Itertools, schema::Schema,
    uuid::Uuid, Result,
};

use crate::{
    config_with_store::ConfigWithStore,
    global_store::{
        LastCompactedIndexMeta, LastIndexOperation, LastIndexOperationDetails,
        SegmentMetadataUpdate,
    },
    instrumented::{format_cache_metrics, format_timers},
    limits::global_limits,
    process_wal::ProcessObjectWalOptions,
    static_sync_runtime::STATIC_SYNC_RUNTIME,
    status_updater::{StatusUpdate, StatusUpdater},
    tantivy_footer::{make_footer, MakeFooterInput},
    tantivy_index::{
        flash_tantivy_index, validate_tantivy_index, IndexMetaJson, TantivyIndexScope,
        TantivyIndexWriterOpts,
    },
    tantivy_index_wrapper::ReadWriteTantivyIndexWrapper,
};

pub fn make_runtime() -> Result<tokio::runtime::Runtime> {
    Ok(tokio::runtime::Builder::new_multi_thread()
        .enable_all()
        .build()?)
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct MergeOpts {
    #[arg(
        long,
        default_value_t = default_target_num_segments(),
        env = "BRAINSTORE_TARGET_NUM_SEGMENTS"
    )]
    #[serde(default = "default_target_num_segments")]
    pub target_num_segments: usize,

    #[arg(long, env = "BRAINSTORE_TOTAL_MERGES")]
    #[serde(default)]
    pub total_merges: Option<usize>,

    #[arg(short, long, env = "BRAINSTORE_GARBAGE_COLLECT")]
    #[serde(default)]
    pub garbage_collect: bool,

    #[arg(
        short,
        long,
        default_value_t = false,
        env = "BRAINSTORE_USE_EXACT_NUM_MERGE_POLICY"
    )]
    #[serde(default)]
    pub use_exact_num_merge_policy: bool,
}

pub fn default_target_num_segments() -> usize {
    2 /* one for reads and one for writes */
}

impl Default for MergeOpts {
    fn default() -> Self {
        Self {
            target_num_segments: default_target_num_segments(),
            total_merges: Default::default(),
            garbage_collect: Default::default(),
            use_exact_num_merge_policy: Default::default(),
        }
    }
}

pub struct MergeTantivySegmentsInput<'a> {
    pub segment_id: Uuid,
    pub config: ConfigWithStore,
    pub schema: &'a Schema,
    pub dry_run: bool,
    pub try_acquire: bool,
}

#[derive(Debug, Default)]
pub struct MergeTantivySegmentsOptionalInput {
    pub use_status_updater: bool,
    pub testing_sleep_ms: Option<u64>,
    pub testing_max_merges: Option<usize>,
}

#[derive(Debug, Default)]
pub struct MergeTantivySegmentsOptions {
    pub merge_opts: MergeOpts,
    pub writer_opts: TantivyIndexWriterOpts,
    pub process_wal_opts: ProcessObjectWalOptions,
}

#[derive(Debug, Serialize)]
pub struct MergeTantivySegmentsOutput {
    pub num_rows: usize,
    pub merge_candidates: Vec<Vec<String>>,
}

impl MergeTantivySegmentsOutput {
    pub fn new(num_rows: usize, merge_candidates: Vec<MergeCandidate>) -> Self {
        Self {
            num_rows,
            merge_candidates: merge_candidates
                .into_iter()
                .map(|c| c.0.into_iter().map(|s| s.uuid_string()).collect())
                .collect(),
        }
    }
}

#[instrument(
    err,
    name = "merge_tantivy_segments",
    skip(input, optional_input, options),
    fields(segment_id = %input.segment_id, dry_run = %input.dry_run, try_acquire = %input.try_acquire)
)]
pub async fn merge_tantivy_segments<'a>(
    input: MergeTantivySegmentsInput<'a>,
    optional_input: MergeTantivySegmentsOptionalInput,
    options: MergeTantivySegmentsOptions,
) -> Result<MergeTantivySegmentsOutput> {
    let mut status_updater = if optional_input.use_status_updater {
        Some(StatusUpdater::new(
            input.config.global_store.clone(),
            input.segment_id,
        ))
    } else {
        None
    };
    let output =
        merge_tantivy_segments_inner(input, optional_input, options, &mut status_updater).await;
    if let Err(e) = &output {
        status_updater.update(LastIndexOperation {
            error: Some(e.to_string()),
            ..Default::default()
        });
    }
    status_updater.finish().await?;
    output
}

async fn merge_tantivy_segments_inner<'a, 'b>(
    input: MergeTantivySegmentsInput<'a>,
    optional_input: MergeTantivySegmentsOptionalInput,
    options: MergeTantivySegmentsOptions,
    status_updater: &'b mut Option<StatusUpdater>,
) -> Result<MergeTantivySegmentsOutput> {
    let MergeTantivySegmentsInput {
        segment_id,
        config,
        schema,
        dry_run,
        try_acquire,
    } = input;
    let MergeTantivySegmentsOptions {
        merge_opts,
        writer_opts,
        process_wal_opts,
    } = options;

    if !dry_run {
        status_updater.update(LastIndexOperation {
            stage: Some("acquiring permit".to_string()),
            details: Some(LastIndexOperationDetails::Merge { merges: vec![] }),
            ..Default::default()
        });
    }
    let _permit = if dry_run {
        None
    } else {
        Some(global_limits().index_operations.start_task().await?)
    };

    let index_scope = TantivyIndexScope::Segment(segment_id);

    status_updater.update(LastIndexOperation {
        stage: Some("acquiring lock".to_string()),
        details: Some(LastIndexOperationDetails::Merge { merges: vec![] }),
        ..Default::default()
    });
    let _guard = if try_acquire {
        match config
            .locks_manager
            .try_write(&index_scope.lock_name())
            .await?
        {
            Some(guard) => guard,
            None => {
                tracing::info!("Could not acquire lock for segment {}", segment_id);
                return Ok(MergeTantivySegmentsOutput {
                    num_rows: 0,
                    merge_candidates: vec![],
                });
            }
        }
    } else {
        config.locks_manager.write(&index_scope.lock_name()).await?
    };
    let _status_updater_guard = status_updater.ensure_next_update();
    status_updater.update(LastIndexOperation {
        stage: Some("acquired lock".to_string()),
        details: Some(LastIndexOperationDetails::Merge { merges: vec![] }),
        ..Default::default()
    });
    if let Some(sleep_ms) = optional_input.testing_sleep_ms {
        log::info!("Sleeping for {} ms", sleep_ms);
        tokio::time::sleep(std::time::Duration::from_millis(sleep_ms)).await;
    }

    let last_compacted_index_meta = config
        .global_store
        .query_segment_metadatas(&[segment_id])
        .await?
        .remove(0)
        .last_compacted_index_meta;

    // If the index is empty, there is nothing to merge.
    let last_compacted_index_meta = match last_compacted_index_meta {
        Some(meta) => meta,
        _ => {
            log::info!(
                "No last_compacted_index_meta for segment {}. Skipping merge",
                segment_id
            );
            return Ok(MergeTantivySegmentsOutput {
                num_rows: 0,
                merge_candidates: vec![],
            });
        }
    };

    // Flash the tantivy index to the metadata we read.
    flash_tantivy_index(
        Some(&last_compacted_index_meta.tantivy_meta),
        schema,
        &config.index,
        &index_scope,
        None,
    )
    .await?;

    let tantivy_index_wrapper = Arc::new(
        ReadWriteTantivyIndexWrapper::new(
            config.index.directory.clone(),
            schema.clone(),
            &config.index.prefix,
            &index_scope,
        )
        .await?,
    );

    let span = tracing::Span::current();
    let start = std::time::Instant::now();

    let (commits_tx, commits_rx) = mpsc::channel(COMMIT_BUFFER);

    let commit_worker_task = tokio::spawn(commit_worker(
        config.clone(),
        writer_opts.clone(),
        schema.clone(),
        segment_id,
        last_compacted_index_meta.clone(),
        commits_rx,
    ));

    let writer_opts = &writer_opts;
    let result = {
        let tantivy_index_wrapper = tantivy_index_wrapper.clone();
        let index_directory = config.index.directory.clone();
        await_spawn_blocking!(
        |writer_opts: &TantivyIndexWriterOpts, status_updater: &mut Option<StatusUpdater>| -> Result<_> {
            let _guard = span.enter();

            let mut index_writer = tantivy_index_wrapper.make_writer_blocking(writer_opts)?;

            let merge_policy: Box<dyn MergePolicy> = if merge_opts.use_exact_num_merge_policy {
                Box::new(ExactNumberMergePolicy(merge_opts.target_num_segments))
            } else {
                // If we're actually performing a merge, then use the default policy (different than what
                // we use at compaction time), which does a log based merge.
                Box::new(HybridMergePolicy::new(
                    process_wal_opts.max_rows_per_segment,
                    last_compacted_index_meta.xact_id.duration_since_now(),
                ))
            };

            if !suppress_verbose_info() {
              log::info!("Using merge policy: {:?}", merge_policy);
            }

            let segments = tantivy_index_wrapper.index.searchable_segment_metas()?;
            let mut merge_candidates = merge_policy
                .compute_merge_candidates(&segments)
                .into_iter()
                .take(merge_opts.total_merges.unwrap_or(usize::MAX))
                .collect::<Vec<_>>();

            merge_candidates.retain(|c| c.0.len() > 1);

            let num_mergeable_segments = merge_candidates.iter().map(|c| c.0.len()).sum::<usize>();
            let num_merges = merge_candidates.len();

            if num_mergeable_segments == 0 {
                return Ok((0, vec![]));
            }

            log::info!(
                "[{}] Merging {} index segments into {}",
                segment_id,
                num_mergeable_segments,
                merge_candidates.len()
            );

            if dry_run {
                for candidate in &merge_candidates {
                    log::info!("Dry run merge candidate: {:?}", candidate);
                }
                return Ok((0, merge_candidates));
            } else {
                status_updater.update(LastIndexOperation {
                    stage: Some("merging".to_string()),
                    details: Some(LastIndexOperationDetails::Merge {
                        merges: merge_candidates
                            .iter()
                            .map(|c| c.0.iter().map(|s| s.short_uuid_string()).collect())
                            .collect(),
                    }),
                    ..Default::default()
                });
            }

            let merge_ops = merge_candidates
                .iter()
                .map(|c| index_writer.merge(&c.0))
                .collect::<Vec<_>>();

            let num_rows = segments.iter().map(|s| s.max_doc() as usize).sum::<usize>();

            status_updater.update(LastIndexOperation {
                stage: Some("running merges".to_string()),
                estimated_progress: Some(0.0),
                ..Default::default()
            });

            for (i, merge_op) in merge_ops.into_iter().enumerate() {
                if i >= optional_input.testing_max_merges.unwrap_or(usize::MAX) {
                    return Err(anyhow!("max merges reached"));
                }

                tracing::info_span!("merge_op", merge_op = i, num_merges = num_merges)
                    .in_scope(|| merge_op.wait())?;
                status_updater.update(LastIndexOperation {
                    estimated_progress: Some((i + 1) as f64 / num_merges as f64),
                    ..Default::default()
                });

                let tantivy_meta = tantivy_index_wrapper.index.load_metas()?;
                let tantivy_meta_json = IndexMetaJson::from_tantivy_meta(&tantivy_meta)?;
                STATIC_SYNC_RUNTIME.block_on(commits_tx.send(tantivy_meta_json))?;
            }
            drop(commits_tx);

            if merge_opts.garbage_collect {
                log::info!("garbage collecting");
                status_updater.update(LastIndexOperation {
                    stage: Some("garbage collecting".to_string()),
                    estimated_progress: Some(1.0),
                    ..Default::default()
                });
                tracing::info_span!("garbage_collect_files")
                    .in_scope(|| index_writer.garbage_collect_files().wait())?;

                status_updater.update(LastIndexOperation {
                    stage: Some("waiting for merging threads".to_string()),
                    estimated_progress: Some(1.0),
                    ..Default::default()
                });
                tracing::info_span!("wait_merging_threads")
                    .in_scope(|| index_writer.wait_merging_threads())?;
            }

            if start.elapsed().as_secs() > 1 {
                tracing::info!("Timing info (merge took {:?})", start.elapsed());
                format_timers(index_directory.as_ref().as_dyn_instrumented());

                log::info!("Cache info");
                format_cache_metrics(index_directory.as_dyn_instrumented());
            }

            Ok((num_rows, merge_candidates))
        },
        writer_opts; status_updater
        )
        // Don't ? this result, because we want to make sure we wait for the commit worker task
        // before bailing out.
    };

    log::info!("Waiting for commit worker to finish");
    commit_worker_task.await??;

    log::info!("Finished merge!");
    let result = result???;
    Ok(MergeTantivySegmentsOutput::new(result.0, result.1))
}

pub const COMMIT_BUFFER: usize = 10;
type CommitMeta = IndexMetaJson;

async fn commit_worker(
    config: ConfigWithStore,
    writer_opts: TantivyIndexWriterOpts,
    schema: Schema,
    segment_id: Uuid,
    mut last_compacted_index_meta: LastCompactedIndexMeta,
    mut commits: mpsc::Receiver<CommitMeta>,
) -> Result<()> {
    let index_scope = TantivyIndexScope::Segment(segment_id);
    let mut commit_buffer: Vec<CommitMeta> = Vec::with_capacity(COMMIT_BUFFER);

    loop {
        match commits.recv_many(&mut commit_buffer, COMMIT_BUFFER).await {
            0 => {
                break;
            }
            n => {
                let mut commit_buffer = commit_buffer.drain(..n).collect::<Vec<_>>();
                let tantivy_meta_json = commit_buffer.pop().expect("commit buffer is empty");
                last_compacted_index_meta = commit_merged_index(
                    &config,
                    &index_scope,
                    &writer_opts,
                    &last_compacted_index_meta,
                    segment_id,
                    tantivy_meta_json,
                    schema.clone(),
                )
                .await?;
            }
        }
    }
    Ok(())
}

async fn commit_merged_index(
    config: &ConfigWithStore,
    index_scope: &TantivyIndexScope,
    writer_opts: &TantivyIndexWriterOpts,
    last_compacted_index_meta: &LastCompactedIndexMeta,
    segment_id: Uuid,
    tantivy_meta_json: IndexMetaJson,
    schema: Schema,
) -> Result<LastCompactedIndexMeta> {
    let validation_result = validate_tantivy_index(
        tantivy_meta_json,
        &config.index.store,
        &config.index.prefix,
        &index_scope,
        &writer_opts.validate_opts,
    )
    .await?;

    let next_last_compacted_index_meta = LastCompactedIndexMeta {
        xact_id: last_compacted_index_meta.xact_id,
        tantivy_meta: validation_result.check_success()?,
    };

    if !config
        .global_store
        .upsert_segment_metadatas(
            [(
                segment_id,
                SegmentMetadataUpdate {
                    last_compacted_index_meta: Some((
                        Some(last_compacted_index_meta.clone()),
                        Some(next_last_compacted_index_meta.clone()),
                    )),
                    ..Default::default()
                },
            )]
            .into_iter()
            .collect(),
        )
        .await?
    {
        let latest_value = config
            .global_store
            .query_segment_metadatas(&[segment_id])
            .await?
            .remove(0)
            .last_compacted_index_meta;
        return Err(anyhow!(
                "last_compacted_index_meta was concurrently modified by another task. Expected value was {}. Latest value is {}",
                serde_json::to_string(&last_compacted_index_meta)?,
                serde_json::to_string(&latest_value)?,
            ));
    }

    // Write a fresh redirect footer to the index directory.
    make_footer(MakeFooterInput {
        index_store: &config.index,
        schema,
        segment_id,
        index_meta: &next_last_compacted_index_meta.tantivy_meta,
    })
    .await?;

    Ok(next_last_compacted_index_meta)
}

#[derive(Debug)]
pub struct ExactNumberMergePolicy(pub usize);

impl MergePolicy for ExactNumberMergePolicy {
    fn compute_merge_candidates(&self, segments: &[SegmentMeta]) -> Vec<MergeCandidate> {
        let size_sorted_segments = segments
            .iter()
            .sorted_by_key(|seg| std::cmp::Reverse(seg.max_doc()))
            .collect::<Vec<&SegmentMeta>>();

        // Calculate size of each chunk to get exactly self.0 chunks
        let chunk_size = size_sorted_segments.len() / self.0;
        let remainder = size_sorted_segments.len() % self.0;

        if segments.len() <= self.0 {
            return Vec::new();
        }

        let mut candidates = Vec::new();
        let mut start = 0;

        for i in 0..self.0 {
            let this_chunk_size = if i < remainder {
                chunk_size + 1
            } else {
                chunk_size
            };
            let end = start + this_chunk_size;
            if start < size_sorted_segments.len() {
                candidates.push(MergeCandidate(
                    size_sorted_segments[start..end]
                        .iter()
                        .map(|seg| seg.id())
                        .collect(),
                ));
            }
            start = end;
        }
        candidates
    }
}

pub const MERGE_SMALL_SEGMENTS_AFTER: std::time::Duration =
    std::time::Duration::from_secs(3600 * 4); // 4 hours

#[derive(Debug)]
pub struct HybridMergePolicy {
    log_merge_policy: LogMergePolicy,
    exact_number_merge_policy: ExactNumberMergePolicy,
    max_rows_per_brainstore_segment: usize,
    compacted_ago: std::time::Duration,
}

impl HybridMergePolicy {
    pub fn new(max_rows_per_brainstore_segment: usize, compacted_ago: std::time::Duration) -> Self {
        Self::new_with_log_merge_policy(
            Self::default_log_merge_policy(),
            max_rows_per_brainstore_segment,
            compacted_ago,
        )
    }

    pub fn new_with_log_merge_policy(
        log_merge_policy: LogMergePolicy,
        max_rows_per_brainstore_segment: usize,
        compacted_ago: std::time::Duration,
    ) -> Self {
        Self {
            log_merge_policy,
            exact_number_merge_policy: ExactNumberMergePolicy(1),
            max_rows_per_brainstore_segment,
            compacted_ago,
        }
    }

    pub fn default_log_merge_policy() -> LogMergePolicy {
        // The tests try to walk through this configuration. The long and short of it is that
        // by setting min_num_segments to 2, we allow the merger to target having exactly one tantivy
        // segment per index (i.e. per braintrust segment).
        let mut merge_policy = LogMergePolicy::default();
        merge_policy.set_min_num_segments(2);
        merge_policy.set_min_layer_size(100);
        merge_policy
    }
}

impl MergePolicy for HybridMergePolicy {
    fn compute_merge_candidates(&self, segments: &[SegmentMeta]) -> Vec<MergeCandidate> {
        // First, try to apply the log merge policy. This works well especially when there are a lot of
        // segments, and we want to (in parallel) merge them.
        let log_candidates = self.log_merge_policy.compute_merge_candidates(segments);

        // If we're within 95% of the max rows per braintrust segment or are at least MERGE_SMALL_SEGMENTS_AFTER
        // stale, then apply the exact number merge policy. This could be a lot more sophisticated, e.g after trying
        // to merge N times and after some inactivity with other segments, we could try to
        // forcibly apply the exact number merge policy.
        let num_rows = segments.iter().map(|s| s.max_doc() as usize).sum::<usize>();
        if log_candidates.is_empty()
            && (num_rows >= (self.max_rows_per_brainstore_segment as f64 * 0.95) as usize
                || self.compacted_ago >= MERGE_SMALL_SEGMENTS_AFTER)
        {
            return self
                .exact_number_merge_policy
                .compute_merge_candidates(segments);
        }

        log_candidates
    }
}

#[cfg(test)]
mod tests {
    use crate::{
        basic_test_fixture::{BasicTestFixture, ValidateVacuumArgs},
        merge::{
            merge_tantivy_segments, ExactNumberMergePolicy, HybridMergePolicy, MergeOpts,
            MergeTantivySegmentsInput, MergeTantivySegmentsOptionalInput,
            MergeTantivySegmentsOptions,
        },
        process_wal::{compact_segment_wal, CompactSegmentWalOptions},
        tantivy_index::{
            write_meta_json, IndexMetaJson, TantivyIndexScope, TantivyIndexWriterOpts,
            ValidateTantivyIndexOptions,
        },
        wal_entry::WalEntry,
    };
    use once_cell::sync::Lazy;
    use std::time::Instant;
    use tantivy::{
        doc,
        indexer::{LogMergePolicy, MergePolicy, NoMergePolicy, UserOperation},
    };
    use tantivy::{
        schema::{Schema, STORED, TEXT},
        Index,
    };
    use tempfile::tempdir;
    use util::xact::TransactionId;

    static DEFAULT_COMPACT_SEGMENT_WAL_OPTS: Lazy<CompactSegmentWalOptions> =
        Lazy::new(|| CompactSegmentWalOptions {
            writer_opts: TantivyIndexWriterOpts {
                index_writer_force_no_merges: true,
                validate_opts: ValidateTantivyIndexOptions {
                    index_writer_validate: true,
                    index_writer_validate_only_deletes: false,
                    ..Default::default()
                },
                ..Default::default()
            },
            ..Default::default()
        });

    #[test]
    fn test_exact_number_merge_policy() {
        let max_segments = 8;
        let mut merge_policy = Box::new(LogMergePolicy::default());
        merge_policy.set_min_num_segments(max_segments);

        let mut schema_builder = Schema::builder();
        let id_field = schema_builder.add_text_field("id", TEXT | STORED);
        let schema = schema_builder.build();

        // First, add max_segments-1 documents, one at a time, and make sure there are n-1 segments
        let start = Instant::now();
        {
            // Create a temporary directory for the index
            let index_dir = tempdir().unwrap();
            let index = Index::create_in_dir(&index_dir, schema.clone()).unwrap();

            // Create writer with default LogMergePolicy
            let mut writer = index.writer(50_000_000).unwrap();

            for i in 0..(max_segments - 1) {
                writer
                    .add_document(doc!(id_field => format!("doc {}", i)))
                    .unwrap();
                writer.commit().unwrap();
            }

            let segments = index.searchable_segments().unwrap();
            assert_eq!(
                segments.len(),
                max_segments - 1,
                "Expected exactly max_segments-1 segments after merge"
            );

            writer.wait_merging_threads().unwrap();

            // Even after the merge, we should still have max_segments-1 segments
            let segments = index.searchable_segments().unwrap();
            assert_eq!(
                segments.len(),
                max_segments - 1,
                "Expected exactly max_segments-1 segments after waiting for merges"
            );
        }
        eprintln!(
            "Time to add {} segments with LogMergePolicy: {:?}",
            max_segments - 1,
            start.elapsed()
        );

        // Now do the same thing, but with max_segments+1 documents
        let start = Instant::now();
        {
            // Create a temporary directory for the index
            let index_dir = tempdir().unwrap();
            let index = Index::create_in_dir(&index_dir, schema.clone()).unwrap();

            // Create writer with default LogMergePolicy
            let mut writer = index.writer(50_000_000).unwrap();

            for i in 0..(max_segments + 1) {
                writer
                    .add_document(doc!(id_field => format!("doc {}", i)))
                    .unwrap();
            }
            eprintln!("Merge policy: {:?}", writer.get_merge_policy());
            writer.commit().unwrap();
            writer.wait_merging_threads().unwrap();

            // After the merge, we should have less than max_segments segments
            let segments = index.searchable_segments().unwrap();
            assert!(segments.len() < max_segments);
            assert!(segments.len() > 1);
        }
        eprintln!(
            "Time to add {} segments with LogMergePolicy: {:?}",
            max_segments + 1,
            start.elapsed()
        );

        // Now do the same thing, but change the merge policy to allow merging 2 segments
        let start = Instant::now();
        {
            // Create a temporary directory for the index
            let index_dir = tempdir().unwrap();
            let index = Index::create_in_dir(&index_dir, schema.clone()).unwrap();

            // Create writer with default LogMergePolicy
            let mut writer = index.writer(50_000_000).unwrap();
            let mut merge_policy = Box::new(LogMergePolicy::default());
            merge_policy.set_min_num_segments(2);
            writer.set_merge_policy(merge_policy);

            let start = Instant::now();
            for i in 0..(max_segments + 1) {
                writer
                    .add_document(doc!(id_field => format!("doc {}", i)))
                    .unwrap();
            }
            eprintln!("Merge policy: {:?}", writer.get_merge_policy());
            writer.commit().unwrap();
            eprintln!("Waiting for merges after {:?}", start.elapsed());
            writer.wait_merging_threads().unwrap();

            // After the merge, we should have exactly 1 segment
            let segments = index.searchable_segments().unwrap();
            assert_eq!(segments.len(), 1);
        }
        eprintln!(
            "Time to add {} segments with LogMergePolicy(2): {:?}",
            max_segments + 1,
            start.elapsed()
        );

        // Now use the ExactNumberMergePolicy with 3
        let start = Instant::now();
        {
            // Create a temporary directory for the index
            let index_dir = tempdir().unwrap();
            let index = Index::create_in_dir(&index_dir, schema.clone()).unwrap();

            // Create writer with default LogMergePolicy
            let mut writer = index.writer(50_000_000).unwrap();
            let merge_policy = Box::new(ExactNumberMergePolicy(3));
            writer.set_merge_policy(merge_policy);

            for i in 0..(max_segments + 1) {
                writer
                    .add_document(doc!(id_field => format!("doc {}", i)))
                    .unwrap();
            }
            eprintln!("Merge policy: {:?}", writer.get_merge_policy());
            writer.commit().unwrap();
            writer.wait_merging_threads().unwrap();

            // After the merge, we should have exactly 3 segments
            let segments = index.searchable_segments().unwrap();
            assert_eq!(segments.len(), 3);
        }
        eprintln!(
            "Time to add {} segments with ExactNumberMergePolicy(3): {:?}",
            max_segments + 1,
            start.elapsed()
        );

        // Now add everything in one op, and make sure that we have exactly one segment, even if the merge
        // policy is disabled.
        let start = Instant::now();
        {
            // Create a temporary directory for the index
            let index_dir = tempdir().unwrap();
            let index = Index::create_in_dir(&index_dir, schema.clone()).unwrap();

            // Create writer with default LogMergePolicy
            let mut writer = index.writer(50_000_000).unwrap();
            writer.set_merge_policy(Box::new(NoMergePolicy::default()));

            let mut ops = Vec::new();
            for i in 0..(max_segments) {
                ops.push(UserOperation::Add(doc!(id_field => format!("doc {}", i))));
            }

            writer.run(ops).unwrap();
            writer.commit().unwrap();

            // Even before the merge, we should have exactly one segment
            let segments = index.searchable_segments().unwrap();
            assert_eq!(segments.len(), 1);

            eprintln!("Merge policy: {:?}", writer.get_merge_policy());
            writer.wait_merging_threads().unwrap();
        }
        eprintln!(
            "Time to add {} segments with NoMergePolicy in one op: {:?}",
            max_segments,
            start.elapsed()
        );

        // And now do this with the 2 segments merge policy
        let start = Instant::now();
        {
            // Create a temporary directory for the index
            let index_dir = tempdir().unwrap();
            let index = Index::create_in_dir(&index_dir, schema.clone()).unwrap();

            // Create writer with default LogMergePolicy
            let mut writer = index.writer(50_000_000).unwrap();
            let mut merge_policy = Box::new(LogMergePolicy::default());
            merge_policy.set_min_num_segments(2);
            writer.set_merge_policy(merge_policy);

            let mut ops = Vec::new();
            for i in 0..(max_segments) {
                ops.push(UserOperation::Add(doc!(id_field => format!("doc {}", i))));
            }

            writer.run(ops).unwrap();
            writer.commit().unwrap();

            // Even before the merge, we should have exactly one segment
            let segments = index.searchable_segments().unwrap();
            assert_eq!(segments.len(), 1);

            eprintln!("Merge policy: {:?}", writer.get_merge_policy());
            writer.wait_merging_threads().unwrap();
        }
        eprintln!(
            "Time to add {} segments with LogMergePolicy(2) in one op: {:?}",
            max_segments,
            start.elapsed()
        );
    }

    #[test]
    fn test_hybrid_merge_policy() {
        let max_rows_per_segment = 10;

        let mut schema_builder = Schema::builder();
        let id_field = schema_builder.add_text_field("id", TEXT | STORED);
        let schema = schema_builder.build();

        // Create a temporary directory for the index
        let index_dir = tempdir().unwrap();
        let index = Index::create_in_dir(&index_dir, schema.clone()).unwrap();

        // Create writer with no merge policy so we end up with max_rows_per_segment segments
        let mut writer = index.writer(50_000_000).unwrap();
        writer.set_merge_policy(Box::new(NoMergePolicy::default()));

        for i in 0..(max_rows_per_segment) {
            let mut ops = Vec::new();
            for _ in 0..i + 1 {
                ops.push(UserOperation::Add(doc!(id_field => format!("doc {}", i))));
            }
            writer.run(ops).unwrap();
            writer.commit().unwrap();
        }

        let segments = index.searchable_segments().unwrap();
        assert_eq!(segments.len(), max_rows_per_segment);

        let segment_meta = segments
            .into_iter()
            .map(|s| s.meta().clone())
            .collect::<Vec<_>>();

        // Neuter the logic policy so we don't actually merge anything.
        let mut log_merge_policy = LogMergePolicy::default();
        log_merge_policy.set_min_layer_size(1);
        log_merge_policy.set_min_num_segments(max_rows_per_segment);
        let candidates = log_merge_policy.compute_merge_candidates(&segment_meta);
        assert_eq!(candidates.len(), 0);

        let merge_policy = HybridMergePolicy::new_with_log_merge_policy(
            log_merge_policy.clone(),
            max_rows_per_segment,
            std::time::Duration::from_secs(0),
        );

        let candidates = merge_policy.compute_merge_candidates(&segment_meta);
        assert_eq!(candidates.len(), 1);
        assert_eq!(candidates[0].0.len(), max_rows_per_segment);

        // Now create a hybrid merge policy with a much larger max_rows_per_segment
        let merge_policy = HybridMergePolicy::new_with_log_merge_policy(
            log_merge_policy,
            max_rows_per_segment * 1000,
            std::time::Duration::from_secs(0),
        );
        let candidates = merge_policy.compute_merge_candidates(&segment_meta);
        assert_eq!(candidates.len(), 0);
    }

    #[tokio::test]
    async fn test_hybrid_merge_policy_stale_segments() {
        let mut schema_builder = tantivy::schema::SchemaBuilder::new();
        let id_field =
            schema_builder.add_text_field("id", tantivy::schema::STRING | tantivy::schema::STORED);
        let schema = schema_builder.build();

        let index = Index::create_in_ram(schema.clone());
        let max_rows_per_segment = 100;

        // Create writer with no merge policy so we end up with max_rows_per_segment segments
        let mut writer = index.writer(50_000_000).unwrap();
        writer.set_merge_policy(Box::new(NoMergePolicy::default()));

        // Create segments that are less than 95% of max_rows_per_segment
        let num_segments = 5;
        let rows_per_segment = 10; // Total 50 rows, which is 50% of max_rows_per_segment
        for i in 0..num_segments {
            let mut ops = Vec::new();
            for j in 0..rows_per_segment {
                ops.push(UserOperation::Add(
                    doc!(id_field => format!("doc {}_{}", i, j)),
                ));
            }
            writer.run(ops).unwrap();
            writer.commit().unwrap();
        }

        let segments = index.searchable_segments().unwrap();
        assert_eq!(segments.len(), num_segments);

        let segment_meta = segments
            .into_iter()
            .map(|s| s.meta().clone())
            .collect::<Vec<_>>();

        // Configure LogMergePolicy to not merge these segments
        let mut log_merge_policy = LogMergePolicy::default();
        log_merge_policy.set_min_layer_size(10000); // Very high to prevent merging
        log_merge_policy.set_min_num_segments(100); // Higher than our num_segments to prevent merging

        // Test 1: Fresh segments (compacted_ago = 0) should not be merged
        let merge_policy = HybridMergePolicy::new_with_log_merge_policy(
            log_merge_policy.clone(),
            max_rows_per_segment,
            std::time::Duration::from_secs(0),
        );
        let candidates = merge_policy.compute_merge_candidates(&segment_meta);
        assert_eq!(
            candidates.len(),
            0,
            "Fresh small segments should not be merged"
        );

        // Test 2: Stale segments (compacted_ago >= MERGE_SMALL_SEGMENTS_AFTER) should be merged
        let merge_policy = HybridMergePolicy::new_with_log_merge_policy(
            log_merge_policy.clone(),
            max_rows_per_segment,
            crate::merge::MERGE_SMALL_SEGMENTS_AFTER,
        );
        let candidates = merge_policy.compute_merge_candidates(&segment_meta);
        assert_eq!(candidates.len(), 1, "Stale small segments should be merged");
        assert_eq!(
            candidates[0].0.len(),
            num_segments,
            "All segments should be merged together"
        );

        // Test 3: Segments just before staleness threshold should not be merged
        let almost_stale =
            crate::merge::MERGE_SMALL_SEGMENTS_AFTER - std::time::Duration::from_secs(1);
        let merge_policy = HybridMergePolicy::new_with_log_merge_policy(
            log_merge_policy,
            max_rows_per_segment,
            almost_stale,
        );
        let candidates = merge_policy.compute_merge_candidates(&segment_meta);
        assert_eq!(
            candidates.len(),
            0,
            "Segments just before staleness threshold should not be merged"
        );
    }

    #[tokio::test]
    async fn test_basic_merge() {
        let fixture = BasicTestFixture::new();

        // Write a few WAL entries to a segment in different compaction steps.
        fixture
            .write_object_wal_entries(vec![WalEntry {
                id: "foo".to_string(),
                _xact_id: TransactionId(0),
                ..Default::default()
            }])
            .await;
        let segment_ids = fixture.run_process_wal().await.modified_segment_ids;
        assert_eq!(segment_ids.len(), 1);
        let segment_id = *segment_ids.iter().next().unwrap();
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            DEFAULT_COMPACT_SEGMENT_WAL_OPTS.clone(),
        )
        .await
        .unwrap();

        fixture
            .write_object_wal_entries(vec![WalEntry {
                id: "bar".to_string(),
                _xact_id: TransactionId(1),
                ..Default::default()
            }])
            .await;
        let segment_ids = fixture.run_process_wal().await.modified_segment_ids;
        assert_eq!(segment_ids.len(), 1);
        assert_eq!(segment_ids.iter().next().unwrap(), &segment_id);
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            DEFAULT_COMPACT_SEGMENT_WAL_OPTS.clone(),
        )
        .await
        .unwrap();

        fixture
            .write_object_wal_entries(vec![WalEntry {
                id: "baz".to_string(),
                _xact_id: TransactionId(2),
                ..Default::default()
            }])
            .await;
        let segment_ids = fixture.run_process_wal().await.modified_segment_ids;
        assert_eq!(segment_ids.len(), 1);
        assert_eq!(segment_ids.iter().next().unwrap(), &segment_id);
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            DEFAULT_COMPACT_SEGMENT_WAL_OPTS.clone(),
        )
        .await
        .unwrap();

        // There should be three segments now.
        let tantivy_meta = fixture.fetch_segment_tantivy_metadata(segment_id).await;
        assert_eq!(tantivy_meta.segments.len(), 3);

        // Wipe the meta.json file just to make sure we flash it when we merge.
        write_meta_json(
            fixture.tmp_dir_config.config.index.directory.as_ref(),
            &TantivyIndexScope::Segment(segment_id)
                .path(&fixture.tmp_dir_config.config.index.prefix),
            &IndexMetaJson::default(),
        )
        .await
        .unwrap();

        // After running a merge into exactly one segment, there should be one segment.
        let output = merge_tantivy_segments(
            MergeTantivySegmentsInput {
                segment_id,
                config: fixture.tmp_dir_config.config.clone(),
                schema: &fixture.make_default_full_schema(),
                dry_run: false,
                try_acquire: false,
            },
            Default::default(),
            MergeTantivySegmentsOptions {
                merge_opts: MergeOpts {
                    target_num_segments: 1,
                    use_exact_num_merge_policy: true,
                    ..Default::default()
                },
                ..Default::default()
            },
        )
        .await
        .unwrap();
        assert_eq!(output.num_rows, 3);
        let tantivy_meta = fixture.fetch_segment_tantivy_metadata(segment_id).await;
        assert_eq!(tantivy_meta.segments.len(), 1);

        fixture.validate_vacuum(ValidateVacuumArgs::default()).await;
    }

    #[tokio::test]
    async fn test_partial_merge() {
        let fixture = BasicTestFixture::new();

        // Write a few WAL entries to a segment in different compaction steps.
        fixture
            .write_object_wal_entries(vec![WalEntry {
                id: "foo".to_string(),
                _xact_id: TransactionId(0),
                ..Default::default()
            }])
            .await;
        let segment_ids = fixture.run_process_wal().await.modified_segment_ids;
        assert_eq!(segment_ids.len(), 1);
        let segment_id = *segment_ids.iter().next().unwrap();
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            DEFAULT_COMPACT_SEGMENT_WAL_OPTS.clone(),
        )
        .await
        .unwrap();

        let writes: usize = 10;
        for i in 0..writes - 1 {
            fixture
                .write_object_wal_entries(vec![WalEntry {
                    id: format!("row{}", i),
                    _xact_id: TransactionId(1 + i as u64),
                    ..Default::default()
                }])
                .await;
            let segment_ids = fixture.run_process_wal().await.modified_segment_ids;
            assert_eq!(segment_ids.len(), 1);
            assert_eq!(segment_ids.iter().next().unwrap(), &segment_id);
            compact_segment_wal(
                fixture.compact_wal_input(segment_id),
                Default::default(),
                DEFAULT_COMPACT_SEGMENT_WAL_OPTS.clone(),
            )
            .await
            .unwrap();
        }

        let tantivy_meta = fixture.fetch_segment_tantivy_metadata(segment_id).await;
        assert_eq!(tantivy_meta.segments.len(), writes);

        // Wipe the meta.json file just to make sure we flash it when we merge.
        write_meta_json(
            fixture.tmp_dir_config.config.index.directory.as_ref(),
            &TantivyIndexScope::Segment(segment_id)
                .path(&fixture.tmp_dir_config.config.index.prefix),
            &IndexMetaJson::default(),
        )
        .await
        .unwrap();

        // Do a dry run, and make sure it results in exactly 5 merges.
        let output = merge_tantivy_segments(
            MergeTantivySegmentsInput {
                segment_id,
                config: fixture.tmp_dir_config.config.clone(),
                schema: &fixture.make_default_full_schema(),
                dry_run: true,
                try_acquire: false,
            },
            Default::default(),
            MergeTantivySegmentsOptions {
                merge_opts: MergeOpts {
                    target_num_segments: 5,
                    use_exact_num_merge_policy: true,
                    ..Default::default()
                },
                ..Default::default()
            },
        )
        .await
        .unwrap();

        assert_eq!(output.num_rows, 0);
        assert_eq!(output.merge_candidates.len(), 5);

        // Run a 5-part merge but bail after 2. Make sure that the merge was partially committed.
        let output = merge_tantivy_segments(
            MergeTantivySegmentsInput {
                segment_id,
                config: fixture.tmp_dir_config.config.clone(),
                schema: &fixture.make_default_full_schema(),
                dry_run: false,
                try_acquire: false,
            },
            MergeTantivySegmentsOptionalInput {
                testing_max_merges: Some(2),
                ..Default::default()
            },
            MergeTantivySegmentsOptions {
                merge_opts: MergeOpts {
                    target_num_segments: 5,
                    use_exact_num_merge_policy: true,
                    ..Default::default()
                },
                ..Default::default()
            },
        )
        .await;
        assert!(output.is_err());

        // Make sure that the merge was partially committed.
        let tantivy_meta = fixture.fetch_segment_tantivy_metadata(segment_id).await;
        assert!(tantivy_meta.segments.len() <= writes - 2);

        fixture.validate_vacuum(ValidateVacuumArgs::default()).await;

        fixture
            .validate_vacuum(ValidateVacuumArgs {
                object_ids: Some(&[Default::default()]),
                ..Default::default()
            })
            .await;
    }
}
