use once_cell::sync::Lazy;
use std::sync::Arc;

use util::{
    json::deep_merge,
    serde_json::{json, Value},
    system_types::{FullObjectId, ObjectId, ObjectType},
    uuid::Uuid,
    xact::TransactionId,
};

use crate::{
    global_store::{GlobalStore, MemoryGlobalStore},
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    object_store_wal::ObjectStoreWal,
    test_util::{
        collect_wal_stream, collect_wal_stream_with_opts, CollectWalStreamOpts, TmpDirStore,
    },
    wal::{wal_insert_unnormalized, wal_stream, WALScope, Wal, WalMetadataStreamOptionalInput},
    wal_entry::{WalEntry, WalEntryComment, WalEntryComments},
    xact_manager::MemoryTransactionManager,
};

static WAL0_OBJECT_ID: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj1").unwrap(),
});

static WAL1_OBJECT_ID: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Dataset,
    object_id: ObjectId::new("obj2").unwrap(),
});

static WAL_SCOPE0: Lazy<WALScope<'static>> = Lazy::new(|| WALScope::Segment(Uuid::new_v4()));

fn flatten_wal_entries(entries: Vec<(TransactionId, Vec<WalEntry>)>) -> Vec<WalEntry> {
    entries
        .into_iter()
        .flat_map(|(_, entries)| entries)
        .collect()
}

async fn test_wal_helper_basic(wal: Arc<dyn Wal>) {
    // Insert several WAL entries.
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row0".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1.0".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1.1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row2".to_string(),
            ..Default::default()
        },
    ];
    wal.insert(*WAL_SCOPE0, wal_entries.clone()).await.unwrap();

    // Iterate through the whole wal.
    let collected_entries = flatten_wal_entries(
        collect_wal_stream(wal_stream(
            wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
                .await
                .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap(),
    );
    assert_eq!(wal_entries, collected_entries);

    // Iterate through the WAL starting from the second transaction.
    let collected_entries = flatten_wal_entries(
        collect_wal_stream(wal_stream(
            wal.wal_metadata_stream(
                *WAL_SCOPE0,
                WalMetadataStreamOptionalInput {
                    start_xact_id: Some(TransactionId(1)),
                    ..Default::default()
                },
            )
            .await
            .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap(),
    );
    assert_eq!(wal_entries[1..], collected_entries);

    // Iterate through the WAL starting from a transaction past the end.
    let collected_entries = flatten_wal_entries(
        collect_wal_stream(wal_stream(
            wal.wal_metadata_stream(
                *WAL_SCOPE0,
                WalMetadataStreamOptionalInput {
                    start_xact_id: Some(TransactionId(3)),
                    ..Default::default()
                },
            )
            .await
            .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap(),
    );
    assert!(collected_entries.is_empty());

    // Iterate through the WAL between a range of transactions.
    let collected_entries = flatten_wal_entries(
        collect_wal_stream(wal_stream(
            wal.wal_metadata_stream(
                *WAL_SCOPE0,
                WalMetadataStreamOptionalInput {
                    start_xact_id: Some(TransactionId(1)),
                    end_xact_id: Some(TransactionId(1)),
                    ..Default::default()
                },
            )
            .await
            .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap(),
    );
    assert_eq!(wal_entries[1..3], collected_entries);
}

async fn test_wal_helper_overlapping_transactions(wal: Arc<dyn Wal>) {
    let wal_entries0 = vec![
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row2".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row0".to_string(),
            ..Default::default()
        },
    ];

    let wal_entries1 = vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1_1".to_string(),
            ..Default::default()
        },
    ];

    wal.insert(*WAL_SCOPE0, wal_entries0).await.unwrap();
    wal.insert(*WAL_SCOPE0, wal_entries1).await.unwrap();

    // Iterating through the stream, we should receive transactions in order and de-duplicated.
    // Explicitly sort within each transaction for comparison purposes.
    let wal_entries = collect_wal_stream(wal_stream(
        wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
            .await
            .unwrap(),
        Default::default(),
    ))
    .await
    .unwrap();
    assert_eq!(
        wal_entries,
        vec![
            (
                TransactionId(0),
                vec![WalEntry {
                    _xact_id: TransactionId(0),
                    id: "row0".to_string(),
                    ..Default::default()
                }]
            ),
            (
                TransactionId(1),
                vec![
                    WalEntry {
                        _xact_id: TransactionId(1),
                        id: "row1".to_string(),
                        ..Default::default()
                    },
                    WalEntry {
                        _xact_id: TransactionId(1),
                        id: "row1_1".to_string(),
                        ..Default::default()
                    },
                ]
            ),
            (
                TransactionId(2),
                vec![WalEntry {
                    _xact_id: TransactionId(2),
                    id: "row2".to_string(),
                    ..Default::default()
                }]
            ),
        ]
    );

    // Test start_xact_id.
    let wal_entries = collect_wal_stream(wal_stream(
        wal.wal_metadata_stream(
            *WAL_SCOPE0,
            WalMetadataStreamOptionalInput {
                start_xact_id: Some(TransactionId(1)),
                ..Default::default()
            },
        )
        .await
        .unwrap(),
        Default::default(),
    ))
    .await
    .unwrap();
    assert_eq!(
        wal_entries,
        vec![
            (
                TransactionId(1),
                vec![
                    WalEntry {
                        _xact_id: TransactionId(1),
                        id: "row1".to_string(),
                        ..Default::default()
                    },
                    WalEntry {
                        _xact_id: TransactionId(1),
                        id: "row1_1".to_string(),
                        ..Default::default()
                    },
                ]
            ),
            (
                TransactionId(2),
                vec![WalEntry {
                    _xact_id: TransactionId(2),
                    id: "row2".to_string(),
                    ..Default::default()
                }]
            ),
        ]
    );
}

async fn test_wal_helper_retention(wal: Arc<dyn Wal>) {
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row0".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row2".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(3),
            id: "row3".to_string(),
            ..Default::default()
        },
    ];
    wal.insert(*WAL_SCOPE0, wal_entries.clone()).await.unwrap();

    // Verify all entries are present initially.
    let collected_entries = flatten_wal_entries(
        collect_wal_stream(wal_stream(
            wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
                .await
                .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap(),
    );
    assert_eq!(wal_entries, collected_entries);

    // Delete entries with transaction ID < 1 (exclusive).
    wal.delete_up_to_xact_id(*WAL_SCOPE0, TransactionId(1), false)
        .await
        .unwrap();

    // Verify entries with transaction ID >= 1 remain.
    let collected_entries = flatten_wal_entries(
        collect_wal_stream(wal_stream(
            wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
                .await
                .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap(),
    );
    assert_eq!(&wal_entries[1..], collected_entries);

    // Delete entries with transaction ID < 3 (exclusive).
    wal.delete_up_to_xact_id(*WAL_SCOPE0, TransactionId(3), false)
        .await
        .unwrap();

    // Verify only entries with transaction ID >= 3 remain.
    let collected_entries = flatten_wal_entries(
        collect_wal_stream(wal_stream(
            wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
                .await
                .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap(),
    );
    assert_eq!(&wal_entries[3..], collected_entries);

    // Delete entries with transaction ID < 4 (exclusive).
    wal.delete_up_to_xact_id(*WAL_SCOPE0, TransactionId(4), false)
        .await
        .unwrap();

    // Verify no WAL entries remain.
    let collected_entries = flatten_wal_entries(
        collect_wal_stream(wal_stream(
            wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
                .await
                .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap(),
    );
    assert!(collected_entries.is_empty());
}

async fn test_wal_helper_standalone_comments(wal: Arc<dyn Wal>) {
    // Create a sequence of WAL entries with the same transaction ID and row ID
    // but with different data and some marked as standalone comments
    let wal_entries = vec![
        // First entry: standalone comment
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            _is_standalone_comment: Some(true),
            comments: WalEntryComments(vec![WalEntryComment {
                id: "comment1".to_string(),
                data: json!({ "text": "This is comment 1" })
                    .as_object()
                    .unwrap()
                    .clone(),
                ..Default::default()
            }]),
            ..Default::default()
        },
        // Second entry: regular entry
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: json!({"regular_data": "This is the main data"})
                .as_object()
                .unwrap()
                .clone(),
            ..Default::default()
        },
        // Third entry: another standalone comment
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            _is_standalone_comment: Some(true),
            comments: WalEntryComments(vec![WalEntryComment {
                id: "comment2".to_string(),
                data: json!({ "text": "This is comment 2" })
                    .as_object()
                    .unwrap()
                    .clone(),
                ..Default::default()
            }]),
            ..Default::default()
        },
    ];

    wal.insert(*WAL_SCOPE0, wal_entries).await.unwrap();

    // Retrieve the WAL entries
    let wal_entries = collect_wal_stream(wal_stream(
        wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
            .await
            .unwrap(),
        Default::default(),
    ))
    .await
    .unwrap();

    // We should have one transaction with one entry that contains all the merged data
    assert_eq!(wal_entries.len(), 1);
    let (xact_id, entries) = &wal_entries[0];
    assert_eq!(*xact_id, TransactionId(1));
    assert_eq!(entries.len(), 1);

    // The merged entry should contain all the data from both comments and the regular entry
    let merged_entry = &entries[0];
    assert_eq!(merged_entry._xact_id, TransactionId(1));
    assert_eq!(merged_entry.id, "row1");

    // Check that all data was merged properly
    let comment1 = merged_entry
        .comments
        .0
        .iter()
        .find(|c| c.id == "comment1")
        .expect("Comment1 should exist");
    assert_eq!(comment1.data.get("text").unwrap(), "This is comment 1");

    assert_eq!(
        merged_entry.data.get("regular_data").unwrap(),
        "This is the main data"
    );

    let comment2 = merged_entry
        .comments
        .0
        .iter()
        .find(|c| c.id == "comment2")
        .expect("Comment2 should exist");
    assert_eq!(comment2.data.get("text").unwrap(), "This is comment 2");

    // The merged entry should not be marked as a standalone comment
    assert_eq!(merged_entry._is_standalone_comment, None);
}

async fn test_wal_helper_merge_streams(wal1: Arc<dyn Wal>, wal2: Arc<dyn Wal>) {
    // Insert entries into the first WAL
    let wal1_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "wal1_row0".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "wal1_row2".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(3),
            id: "wal1_row3".to_string(),
            ..Default::default()
        },
    ];
    wal1.insert(*WAL_SCOPE0, wal1_entries.clone())
        .await
        .unwrap();

    // Insert entries into the second WAL
    let wal2_entries = vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "wal2_row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "wal2_row2".to_string(),
            ..Default::default()
        },
    ];
    wal2.insert(*WAL_SCOPE0, wal2_entries.clone())
        .await
        .unwrap();

    // Create metadata streams for both WALs
    let wal1_metadata_stream = wal1
        .wal_metadata_stream(*WAL_SCOPE0, Default::default())
        .await
        .unwrap();
    let wal2_metadata_stream = wal2
        .wal_metadata_stream(*WAL_SCOPE0, Default::default())
        .await
        .unwrap();

    // Merge the metadata streams
    let merged_metadata_stream =
        crate::wal::merge_wal_metadata_streams(vec![wal1_metadata_stream, wal2_metadata_stream]);

    // Process the merged stream
    let merged_entries = collect_wal_stream(wal_stream(merged_metadata_stream, Default::default()))
        .await
        .unwrap();

    // Verify that entries are properly merged and ordered by transaction ID
    assert_eq!(merged_entries.len(), 4); // Should have 4 transactions: 0, 1, 2, and 3

    // Transaction 0 should have one entry from wal1
    assert_eq!(merged_entries[0].0, TransactionId(0));
    assert_eq!(merged_entries[0].1.len(), 1);
    assert_eq!(merged_entries[0].1[0].id, "wal1_row0");

    // Transaction 1 should have one entry from wal2
    assert_eq!(merged_entries[1].0, TransactionId(1));
    assert_eq!(merged_entries[1].1.len(), 1);
    assert_eq!(merged_entries[1].1[0].id, "wal2_row1");

    // Transaction 2 should have entries from both wal1 and wal2
    assert_eq!(merged_entries[2].0, TransactionId(2));
    assert_eq!(merged_entries[2].1.len(), 2);

    // The entries for transaction 2 should include both rows
    let ids: Vec<String> = merged_entries[2].1.iter().map(|e| e.id.clone()).collect();
    assert!(ids.contains(&"wal1_row2".to_string()));
    assert!(ids.contains(&"wal2_row2".to_string()));

    // Transaction 3 should have one entry from wal1 only
    assert_eq!(merged_entries[3].0, TransactionId(3));
    assert_eq!(merged_entries[3].1.len(), 1);
    assert_eq!(merged_entries[3].1[0].id, "wal1_row3");
}

async fn test_wal_helper_max_num_bytes(wal: Arc<dyn Wal>) {
    // Create two large WAL entries (10KB each)
    let large_data = "x".repeat(10 * 1024);
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: json!({"large_data": large_data.clone()})
                .as_object()
                .unwrap()
                .clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row2".to_string(),
            data: json!({"large_data": large_data})
                .as_object()
                .unwrap()
                .clone(),
            ..Default::default()
        },
    ];
    wal.insert(*WAL_SCOPE0, wal_entries.clone()).await.unwrap();

    // Test with max_num_bytes = 10KB (should get only first entry)
    let entries = flatten_wal_entries(
        collect_wal_stream_with_opts(
            wal_stream(
                wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
                    .await
                    .unwrap(),
                crate::wal::WalStreamOptions {
                    max_num_bytes: Some(10 * 1024),
                    ..Default::default()
                },
            ),
            CollectWalStreamOpts {
                expect_exhausted_max_num_bytes: Some(true),
                ..Default::default()
            },
        )
        .await
        .unwrap(),
    );
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].id, "row1");

    // Test with max_num_bytes = 1 (should get only first entry)
    let entries = flatten_wal_entries(
        collect_wal_stream_with_opts(
            wal_stream(
                wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
                    .await
                    .unwrap(),
                crate::wal::WalStreamOptions {
                    max_num_bytes: Some(1),
                    ..Default::default()
                },
            ),
            CollectWalStreamOpts {
                expect_exhausted_max_num_bytes: Some(true),
                ..Default::default()
            },
        )
        .await
        .unwrap(),
    );
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].id, "row1");

    // Test with max_num_bytes = 0 (should get no entries)
    let entries = flatten_wal_entries(
        collect_wal_stream_with_opts(
            wal_stream(
                wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
                    .await
                    .unwrap(),
                crate::wal::WalStreamOptions {
                    max_num_bytes: Some(0),
                    ..Default::default()
                },
            ),
            CollectWalStreamOpts {
                expect_exhausted_max_num_bytes: Some(true),
                ..Default::default()
            },
        )
        .await
        .unwrap(),
    );
    assert_eq!(entries.len(), 0);

    // Test with max_num_bytes = 20KB (should get both entries)
    let entries = flatten_wal_entries(
        collect_wal_stream_with_opts(
            wal_stream(
                wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
                    .await
                    .unwrap(),
                crate::wal::WalStreamOptions {
                    max_num_bytes: Some(20 * 1024),
                    ..Default::default()
                },
            ),
            CollectWalStreamOpts {
                expect_exhausted_max_num_bytes: Some(false),
                ..Default::default()
            },
        )
        .await
        .unwrap(),
    );
    assert_eq!(entries.len(), 2);
    assert_eq!(entries[0].id, "row1");
    assert_eq!(entries[1].id, "row2");

    // Test with max_num_bytes = None (should get both entries)
    let entries = flatten_wal_entries(
        collect_wal_stream_with_opts(
            wal_stream(
                wal.wal_metadata_stream(*WAL_SCOPE0, Default::default())
                    .await
                    .unwrap(),
                crate::wal::WalStreamOptions {
                    max_num_bytes: None,
                    ..Default::default()
                },
            ),
            CollectWalStreamOpts {
                expect_exhausted_max_num_bytes: Some(false),
                ..Default::default()
            },
        )
        .await
        .unwrap(),
    );
    assert_eq!(entries.len(), 2);
    assert_eq!(entries[0].id, "row1");
    assert_eq!(entries[1].id, "row2");
}

async fn test_wal_helper<F, Fixture>(make_wal: F)
where
    F: Fn() -> (Fixture, Arc<dyn Wal>),
{
    {
        let (_fixture, wal) = make_wal();
        wal.status().await.unwrap();
    }
    {
        let (_fixture, wal) = make_wal();
        test_wal_helper_basic(wal).await;
    }
    {
        let (_fixture, wal) = make_wal();
        test_wal_helper_overlapping_transactions(wal).await;
    }
    {
        let (_fixture, wal) = make_wal();
        test_wal_helper_standalone_comments(wal).await;
    }
    {
        let (_fixture0, wal0) = make_wal();
        let (_fixture1, wal1) = make_wal();
        test_wal_helper_merge_streams(wal0, wal1).await;
    }
    {
        let (_fixture, wal) = make_wal();
        test_wal_helper_retention(wal).await;
    }
    {
        let (_fixture, wal) = make_wal();
        test_wal_helper_max_num_bytes(wal).await;
    }
}

struct ObjectStoreWalInfo {
    _tmp_dir_store: TmpDirStore,
}

impl ObjectStoreWalInfo {
    pub fn new() -> (Self, Arc<dyn Wal>) {
        let tmp_dir_store = TmpDirStore::new();
        let wal = Arc::new(ObjectStoreWal {
            store: tmp_dir_store.store_info.store.clone(),
            store_type: tmp_dir_store.store_info.store_type,
            directory: tmp_dir_store.store_info.directory.clone(),
            store_prefix: tmp_dir_store.store_info.prefix.clone(),
        });
        let ret = Self {
            _tmp_dir_store: tmp_dir_store,
        };
        (ret, wal)
    }
}

struct ObjectAndGlobalStoreWalInfo {
    _tmp_dir_store: TmpDirStore,
}

impl ObjectAndGlobalStoreWalInfo {
    pub fn new() -> (Self, Arc<dyn Wal>) {
        let global_store = Arc::new(MemoryGlobalStore::default());
        let tmp_dir_store = TmpDirStore::new();
        let wal = Arc::new(ObjectAndGlobalStoreWal {
            object_store: tmp_dir_store.store_info.store.clone(),
            global_store,
            directory: tmp_dir_store.store_info.directory.clone(),
            store_prefix: tmp_dir_store.store_info.prefix.clone(),
            store_type: tmp_dir_store.store_info.store_type,
        });
        let ret = Self {
            _tmp_dir_store: tmp_dir_store,
        };
        (ret, wal)
    }
}

fn make_wal0(extra: Value) -> Value {
    let mut ret = json!({
        "_object_type": WAL0_OBJECT_ID.object_type,
        "_object_id": WAL0_OBJECT_ID.object_id,
    });
    deep_merge(&mut ret, &extra);
    ret
}

fn make_wal1(extra: Value) -> Value {
    let mut ret = json!({
        "_object_type": WAL1_OBJECT_ID.object_type,
        "_object_id": WAL1_OBJECT_ID.object_id,
    });
    deep_merge(&mut ret, &extra);
    ret
}

async fn get_wal_token(global_store: &dyn GlobalStore, object_id: FullObjectId<'_>) -> Uuid {
    global_store
        .query_object_metadatas(&[object_id])
        .await
        .unwrap()
        .remove(0)
        .wal_token
}

// This test is more-or-less WAL-agnostic and is more about the behavior of the
// `wal_insert_unnormalized` function.
#[tokio::test]
async fn test_wal_insert_unnormalized() {
    let global_store_arc = Arc::new(MemoryGlobalStore::default());
    let xact_manager_arc = Arc::new(MemoryTransactionManager::default());
    let (_fixture, wal) = ObjectStoreWalInfo::new();
    let global_store = &*global_store_arc;
    let xact_manager = &*xact_manager_arc;

    // Successful insert.
    let xact_id0 = wal_insert_unnormalized(
        &*wal,
        global_store,
        vec![make_wal0(json!({
            "input": "foo",
            "output": 99,
        }))],
        xact_manager,
    )
    .await
    .unwrap();
    // Failed insert, due to invalidly-typed string value.
    {
        let failed_result = wal_insert_unnormalized(
            &*wal,
            global_store,
            vec![make_wal0(json!({
                "id": 99,
            }))],
            xact_manager,
        )
        .await;
        assert!(failed_result.is_err());
    }
    // Failed insert, due to invalidly-typed boolean value.
    {
        let failed_result = wal_insert_unnormalized(
            &*wal,
            global_store,
            vec![make_wal0(json!({
                "_is_merge": 99,
            }))],
            xact_manager,
        )
        .await;
        assert!(failed_result.is_err());
    }
    // Another successful insert, where the two entries for the same row should get merged.
    let xact_id1 = wal_insert_unnormalized(
        &*wal,
        global_store,
        vec![
            make_wal0(json!({
                "id": "row0",
                "input": "bar",
            })),
            make_wal0(json!({
                "id": "row0",
                "input": "baz",
                "_is_merge": true,
            })),
        ],
        xact_manager,
    )
    .await
    .unwrap();
    // Another insert into two different objects.
    let xact_id2 = wal_insert_unnormalized(
        &*wal,
        global_store,
        vec![
            make_wal0(json!({
                "input": "blitzer",
            })),
            make_wal1(json!({
                "input": "blitzer",
            })),
        ],
        xact_manager,
    )
    .await
    .unwrap();

    // Iterate through the whole WAL0.
    let object0_wal_token = get_wal_token(global_store, *WAL0_OBJECT_ID).await;
    let wal_entries = collect_wal_stream(wal_stream(
        wal.wal_metadata_stream(
            WALScope::ObjectId(*WAL0_OBJECT_ID, object0_wal_token),
            Default::default(),
        )
        .await
        .unwrap(),
        Default::default(),
    ))
    .await
    .unwrap();
    assert_eq!(wal_entries.len(), 3);
    {
        let (xact_id, entries) = &wal_entries[0];
        assert_eq!(xact_id, &xact_id0);
        assert_eq!(entries.len(), 1);
        {
            let entry = &entries[0];
            assert_eq!(entry._xact_id, xact_id0);
            assert_eq!(entry.data["input"], json!("foo"));
            assert_eq!(entry.data["output"], json!(99));
        }
    }
    {
        let (xact_id, entries) = &wal_entries[1];
        assert_eq!(xact_id, &xact_id1);
        assert_eq!(entries.len(), 1);
        {
            let entry = &entries[0];
            assert_eq!(entry._xact_id, xact_id1);
            assert_eq!(entry.id, "row0");
            assert_eq!(entry._is_merge, None);
            assert_eq!(entry.data["input"], json!("baz"));
        }
    }
    {
        let (xact_id, entries) = &wal_entries[2];
        assert_eq!(xact_id, &xact_id2);
        assert_eq!(entries.len(), 1);
        {
            let entry = &entries[0];
            assert_eq!(entry._xact_id, xact_id2);
            assert_eq!(entry.data["input"], json!("blitzer"));
        }
    }

    // Similar for WAL1.
    let object1_wal_token = get_wal_token(global_store, *WAL1_OBJECT_ID).await;
    let wal_entries = collect_wal_stream(wal_stream(
        wal.wal_metadata_stream(
            WALScope::ObjectId(*WAL1_OBJECT_ID, object1_wal_token),
            Default::default(),
        )
        .await
        .unwrap(),
        Default::default(),
    ))
    .await
    .unwrap();
    assert_eq!(wal_entries.len(), 1);
    {
        let (xact_id, entries) = &wal_entries[0];
        assert_eq!(xact_id, &xact_id2);
        assert_eq!(entries.len(), 1);
        {
            let entry = &entries[0];
            assert_eq!(entry._xact_id, xact_id2);
            assert_eq!(entry.data["input"], json!("blitzer"));
        }
    }
}

#[tokio::test]
async fn test_object_store_wal() {
    test_wal_helper(ObjectStoreWalInfo::new).await;
}

#[tokio::test]
async fn test_object_and_global_store_wal() {
    test_wal_helper(ObjectAndGlobalStoreWalInfo::new).await;
}
