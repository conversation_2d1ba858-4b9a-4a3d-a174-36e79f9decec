use clap::Parse<PERSON>;
use serde::{Deserialize, Serialize};
use util::uuid::Uuid;

#[derive(<PERSON><PERSON>, Debug, Parser, Serialize, Deserialize)]
pub struct CommonVacuumOptions {
    // To avoid interfering with in-progress read/write operations, don't delete files
    // last modified within the last `vacuum_deletion_grace_period_seconds`.
    #[arg(
        long,
        default_value_t = default_vacuum_deletion_grace_period_seconds(),
        help = "Grace period for deleting files.",
        env = "BRAINSTORE_VACUUM_DELETION_GRACE_PERIOD_SECONDS",
    )]
    #[serde(default = "default_vacuum_deletion_grace_period_seconds")]
    pub vacuum_deletion_grace_period_seconds: i64,
    // This accounts for the fact that SegmentVacuumState::last_written_ts isn't a precise "commit
    // timestamp", since writes can continue briefly after a segment op commits its metadata update.
    #[arg(
        long,
        default_value_t = default_vacuum_last_written_slop_seconds(),
        help = "Buffer period to account for writes that continue after the segment's last_written timestamp is committed.",
        env = "BRAINSTORE_VACUUM_LAST_WRITTEN_SLOP_SECONDS",
    )]
    #[serde(default = "default_vacuum_last_written_slop_seconds")]
    pub vacuum_last_written_slop_seconds: i64,
    #[arg(
        long,
        default_value_t = default_delete_ops_batch_size(),
        help = "Batch size for delete ops files.",
        env = "BRAINSTORE_VACUUM_DELETE_OPS_BATCH_SIZE",
    )]
    #[serde(default = "default_delete_ops_batch_size")]
    pub vacuum_delete_ops_batch_size: usize,
}

impl Default for CommonVacuumOptions {
    fn default() -> Self {
        Self {
            vacuum_deletion_grace_period_seconds: default_vacuum_deletion_grace_period_seconds(),
            vacuum_last_written_slop_seconds: default_vacuum_last_written_slop_seconds(),
            vacuum_delete_ops_batch_size: default_delete_ops_batch_size(),
        }
    }
}

// This plus `default_vacuum_last_written_slop_seconds` sum to 1 day to make the resulting
// query filter (see `query_vacuum_segment_ids`) easier to reason about, but the exact
// values don't really matter so long as we don't interfere with ongoing write operations.
fn default_vacuum_deletion_grace_period_seconds() -> i64 {
    23 * 60 * 60 // 23 hours
}

fn default_vacuum_last_written_slop_seconds() -> i64 {
    60 * 60 // 1 hour
}

fn default_delete_ops_batch_size() -> usize {
    1000
}

#[derive(Copy, Clone, Debug, Eq, PartialEq, Serialize, Deserialize)]
pub enum VacuumType {
    #[serde(rename = "vacuum_index")]
    VacuumIndex,
    #[serde(rename = "vacuum_segment_wal")]
    VacuumSegmentWal,
}

impl VacuumType {
    pub fn lock_name(self) -> String {
        self.to_string()
    }
}

impl std::fmt::Display for VacuumType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let s = match self {
            VacuumType::VacuumIndex => "vacuum_index",
            VacuumType::VacuumSegmentWal => "vacuum_segment_wal",
        };
        write!(f, "{}", s)
    }
}

/// Directory names for delete operations logging
pub(crate) const DELETE_OPS_DIR: &str = "delete_ops";
pub(crate) const DRY_RUN_DELETE_OPS_DIR: &str = "dry_run_delete_ops";

/// Generate a unique ID for delete operations files with lexicographic ordering
pub(crate) fn generate_delete_ops_id(time: std::time::SystemTime) -> String {
    let timestamp = time
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let uuid = Uuid::new_v4();
    format!("{:012}.{}", timestamp, uuid)
}

#[cfg(test)]
pub mod tests {
    use super::*;
    use std::time::{SystemTime, UNIX_EPOCH};
    use util::anyhow::Result;

    /// Extract the timestamp from a delete log filename.
    pub fn extract_delete_ops_timestamp(id: &str) -> Result<u64> {
        let parts: Vec<&str> = id.split(".").collect();
        let timestamp = parts[0].parse::<u64>()?;
        Ok(timestamp)
    }

    #[test]
    fn test_delete_ops_id_lexicographic_ordering() -> Result<()> {
        let times = vec![
            // Timestamp = 0
            UNIX_EPOCH,
            // Timestamp = 100
            UNIX_EPOCH + std::time::Duration::from_secs(100),
            // Timestamp = 1000
            UNIX_EPOCH + std::time::Duration::from_secs(1000),
            SystemTime::now() - std::time::Duration::from_secs(1),
            SystemTime::now(),
            // Rolls over into 11 digits
            UNIX_EPOCH + std::time::Duration::from_secs(99_999_999_999),
        ];

        let mut ids = Vec::new();
        for time in &times {
            ids.push(generate_delete_ops_id(*time));
        }

        // Verify that the IDs are in ascending lexicographic order.
        for i in 0..ids.len() - 1 {
            assert!(ids[i] < ids[i + 1], "IDs should be in ascending order");
        }

        // Verify that the timestamp can be reconstructed.
        for (id, time) in ids.iter().zip(times.iter()) {
            let timestamp = extract_delete_ops_timestamp(id)?;
            assert_eq!(
                timestamp,
                time.duration_since(UNIX_EPOCH).unwrap().as_secs()
            );
        }

        Ok(())
    }
}
