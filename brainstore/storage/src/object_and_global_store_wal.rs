use async_trait::async_trait;
use bytes::Bytes;
use object_store::ObjectStore;
use std::collections::HashMap;
use std::io::{Cursor, Write};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tracing::{instrument, Instrument};
use util::sha2::{Digest, Sha256};
use util::url_util::ObjectStoreType;

use async_stream::stream;
use futures::{stream::BoxStream, StreamExt};
use util::{
    anyhow::{self, Context, Result},
    itertools::Itertools,
    serde_json::{self, Value},
    uuid::Uuid,
    xact::TransactionId,
};

use crate::directory::async_directory::AsyncDirectory;
use crate::directory::AsyncDirectoryArc;
use crate::global_store::{
    compute_next_wal_entry_cursor, GlobalStore, SegmentWalEntriesCursor, SegmentWalEntry,
    UpsertSegmentWalEntry,
};
use crate::healthcheck_util::validate_object_store_connection;
use crate::paths::make_segment_directory_path;
use crate::wal::{
    DeleteUpToXactIdOutput, WALScope, Wal, WalEntryVariant, WalEntryVariantPreference, WalMetadata,
    WalMetadataStreamOptionalInput,
};
use crate::wal_entry::WalEntry;

const LIST_BYTE_RANGES_BATCH_SIZE: i64 = 1000;

#[derive(Debug, Clone)]
pub struct ObjectAndGlobalStoreWal {
    // The store and directory should be pointing to the same filesystem location.
    pub object_store: Arc<dyn ObjectStore>,
    pub global_store: Arc<dyn GlobalStore>,
    pub directory: AsyncDirectoryArc,
    pub store_prefix: PathBuf,
    pub store_type: ObjectStoreType,
}

pub struct ObjectAndGlobalStoreWalMetadata {
    pub xact_id: TransactionId,
    pub wal_filename: Uuid,
    pub byte_range_start: usize,
    pub byte_range_end: usize,
    pub wal_directory: PathBuf,

    directory: AsyncDirectoryArc,
}

#[async_trait]
impl WalMetadata for ObjectAndGlobalStoreWalMetadata {
    fn xact_id(&self) -> TransactionId {
        self.xact_id
    }

    fn num_bytes(&self) -> usize {
        self.byte_range_end - self.byte_range_start
    }

    async fn read_wal_entries(&self, _: WalEntryVariantPreference) -> Result<Vec<WalEntryVariant>> {
        let mut entries = Vec::new();
        let bytes = ObjectAndGlobalStoreWal::read_byte_range(
            self.directory.as_ref(),
            self.wal_directory.as_path(),
            self.wal_filename,
            self.byte_range_start,
            self.byte_range_end,
        )
        .await?;
        let deserializer = {
            let mut de = serde_json::Deserializer::from_slice(&bytes);
            // WAL entries can overflow the default serde recursion limit.
            de.disable_recursion_limit();
            de.into_iter::<Value>()
        };
        for (position_number, value) in deserializer.enumerate() {
            let entry = value
                .map_err(|e| -> anyhow::Error { e.into() })
                .and_then(WalEntry::new)
                .with_context(|| {
                    format!(
                        "Failed to parse JSON value at position {}. xact_id: {} wal_filename: {} byte_range_start: {}. byte_range_end: {}",
                        position_number + 1,
                        self.xact_id,
                        self.wal_filename,
                        self.byte_range_start,
                        self.byte_range_end
                    )
                })?;
            entries.push(WalEntryVariant::Full(entry));
        }
        Ok(entries)
    }
}

impl ObjectAndGlobalStoreWal {
    pub fn wal_directory(&self, scope: WALScope) -> PathBuf {
        match scope {
            WALScope::ObjectId(_, _) => {
                panic!("Object WAL not supported for ObjectAndGlobalStoreWal");
            }
            WALScope::Segment(segment_id) => {
                make_segment_directory_path(&self.store_prefix, segment_id)
                    .join("object-global-store-wal")
            }
        }
    }

    /// Callers that specifically utilize ObjectAndGlobalStoreWal can extra metadata about the WAL
    /// entries by using this method.
    #[instrument(err, skip(self))]
    pub async fn wal_metadata_stream_concrete<'a>(
        &self,
        scope: WALScope<'a>,
        optional_input: WalMetadataStreamOptionalInput,
    ) -> Result<BoxStream<'static, Result<ObjectAndGlobalStoreWalMetadata>>> {
        let segment_id = get_wal_scope_segment_id(&scope);
        let wal_directory = self.wal_directory(scope);
        let global_store = self.global_store.clone();
        // Note: we don't use is_compacted_filter here because we generally want to stream ALL wal
        // entries starting from a particular xact_id in compaction. Skipping already-compacted wal
        // entries beyond the start_xact_id is incorrect for compaction.
        let is_compacted_filter = None;
        let directory = self.directory.clone();
        let ret = stream! {
            let mut total_num_byte_ranges = 0;
            let mut next_page_fut: Option<tokio::task::JoinHandle<Result<Vec<SegmentWalEntry>>>> = Some({
              let global_store = global_store.clone();
              tokio::spawn(async move {
                      global_store.query_segment_wal_entries_batch(
                          segment_id,
                          optional_input.start_xact_id.map(SegmentWalEntriesCursor::XactIdGe),
                          Some(LIST_BYTE_RANGES_BATCH_SIZE),
                          is_compacted_filter,
                      ).await
                  }.instrument(tracing::Span::current())
              )
            });
            'outer: loop {
                let page = match next_page_fut.take() {
                    Some(fut) => fut.await??,
                    None => break,
                };
                let next_page_cursor = compute_next_wal_entry_cursor(&page);
                next_page_fut = next_page_cursor.map(|cursor| {
                    let global_store = global_store.clone();
                    tokio::spawn(async move {
                          global_store.query_segment_wal_entries_batch(
                              segment_id,
                              Some(cursor),
                              Some(LIST_BYTE_RANGES_BATCH_SIZE),
                              is_compacted_filter).await
                    }.instrument(tracing::Span::current()))
                });
                for entry in page {
                    if let Some(end_xact_id) = optional_input.end_xact_id {
                        if entry.xact_id > end_xact_id {
                            break 'outer;
                        }
                    }
                    total_num_byte_ranges += 1;
                    yield Ok(ObjectAndGlobalStoreWalMetadata {
                        xact_id: entry.xact_id,
                        wal_filename: entry.wal_filename,
                        byte_range_start: entry.byte_range_start,
                        byte_range_end: entry.byte_range_end,
                        wal_directory: wal_directory.clone(),
                        directory: directory.clone(),
                    });
                }
            }
            tracing::info!(num_byte_ranges = total_num_byte_ranges, "Read WAL byte ranges");
        }.instrument(tracing::info_span!("list WAL byte ranges")).boxed();
        Ok(ret)
    }

    #[instrument(err, skip(self, wal_entries), fields(num_entries = wal_entries.len()))]
    pub async fn insert_no_commit_metadata<'a>(
        &self,
        scope: WALScope<'a>,
        wal_entries: Vec<WalEntryVariant>,
        is_compacted: bool,
    ) -> Result<HashMap<Uuid, HashMap<Uuid, Vec<UpsertSegmentWalEntry>>>> {
        if wal_entries.is_empty() {
            return Ok(HashMap::new());
        }

        let mut xact_id_to_entries: Vec<(TransactionId, Vec<WalEntryVariant>)> = wal_entries
            .into_iter()
            .into_group_map_by(|entry| entry.xact_id())
            .into_iter()
            .collect();
        xact_id_to_entries.sort_by_key(|(xact_id, _)| *xact_id);

        // Serialize each group of entries into a single byte buffer, and track the byte ranges
        // compromising each group.
        let mut buffer: Vec<u8> = Vec::new();
        let mut xact_byte_ranges: Vec<(TransactionId, usize, usize)> = Vec::new();
        {
            let _span = tracing::info_span!("serializing wal entries").entered();
            let mut cursor = Cursor::new(&mut buffer);
            for (xact_id, entries) in xact_id_to_entries {
                let start_pos = cursor.position() as usize;
                assert!(!entries.is_empty());
                for entry in entries {
                    match entry {
                        WalEntryVariant::Full(entry) => {
                            serde_json::to_writer(&mut cursor, &entry.to_value())?;
                        }
                        WalEntryVariant::SystemFields { full_bytes, .. } => {
                            cursor.write_all(full_bytes.as_slice())?;
                        }
                    }
                    cursor.write_all(b"\n")?;
                }
                let end_pos = cursor.position() as usize;
                xact_byte_ranges.push((xact_id, start_pos, end_pos));
            }
        }

        // Write to the directory.
        let segment_id = get_wal_scope_segment_id(&scope);
        let wal_filename = Uuid::new_v4();
        let fullpath = self.wal_directory(scope).join(wal_filename.to_string());
        {
            self.directory
                .async_atomic_write(&fullpath, &buffer)
                .instrument(tracing::info_span!(
                    "write WAL file",
                    bytes_written = buffer.len(),
                ))
                .await?;
        }

        // Collect the byte ranges to the global store.
        let upsert_segment_wal_entries = xact_byte_ranges
            .into_iter()
            .map(
                |(xact_id, byte_range_start, byte_range_end)| UpsertSegmentWalEntry {
                    is_compacted,
                    xact_id,
                    byte_range_start,
                    byte_range_end,
                    digest: Some(compute_wal_entry_digest(
                        &buffer[byte_range_start..byte_range_end],
                    )),
                },
            )
            .collect::<Vec<_>>();
        Ok([(
            segment_id,
            [(wal_filename, upsert_segment_wal_entries)]
                .into_iter()
                .collect(),
        )]
        .into_iter()
        .collect())
    }

    #[instrument(err, skip(self, wal_entries), fields(num_entries = wal_entries.len()))]
    pub async fn insert_full<'a>(
        &self,
        scope: WALScope<'a>,
        wal_entries: Vec<WalEntryVariant>,
        is_compacted: bool,
    ) -> Result<()> {
        let upsert_metadata = self
            .insert_no_commit_metadata(scope, wal_entries, is_compacted)
            .await?;
        if upsert_metadata.is_empty() {
            return Ok(());
        }
        // Commit the WAL entries to the global store.
        self.global_store
            .upsert_segment_wal_entries(upsert_metadata)
            .await?;
        Ok(())
    }

    #[instrument(err, level = "debug", skip(directory))]
    async fn read_byte_range(
        directory: &dyn AsyncDirectory,
        wal_directory: &Path,
        wal_filename: Uuid,
        byte_range_start: usize,
        byte_range_end: usize,
    ) -> Result<Bytes> {
        let path = wal_directory.join(wal_filename.to_string());
        let file_handle = directory.async_get_file_handle(&path, None).await?;
        let read_bytes = file_handle
            .async_read_bytes(byte_range_start..byte_range_end)
            .await?;
        Ok::<_, anyhow::Error>(read_bytes.to_vec().into())
    }
}

#[async_trait]
impl Wal for ObjectAndGlobalStoreWal {
    async fn insert<'a>(&self, scope: WALScope<'a>, wal_entries: Vec<WalEntry>) -> Result<()> {
        self.insert_full(
            scope,
            wal_entries.into_iter().map(WalEntryVariant::Full).collect(),
            false, /* is_compacted */
        )
        .await
    }

    async fn wal_metadata_stream<'a>(
        &self,
        scope: WALScope<'a>,
        optional_input: WalMetadataStreamOptionalInput,
    ) -> Result<BoxStream<'static, Result<Box<dyn WalMetadata>>>> {
        let concrete_stream = self
            .wal_metadata_stream_concrete(scope, optional_input)
            .await?;
        let ret = concrete_stream
            .map(|x| x.map(|x| Box::new(x) as Box<dyn WalMetadata>))
            .boxed();
        Ok(ret)
    }

    async fn status(&self) -> Result<String> {
        validate_object_store_connection(&self.object_store, &self.store_prefix).await?;
        let global_store_status = self.global_store.status().await?;
        Ok(format!(
            "{:?}-type ObjectStore is ok. {}",
            self.store_type, global_store_status
        ))
    }

    fn remove_local(&self) -> Result<()> {
        let wal_prefix_path = PathBuf::from("/").join(&self.store_prefix);
        if std::fs::metadata(&wal_prefix_path).is_ok() {
            std::fs::remove_dir_all(&wal_prefix_path)?;
        }
        Ok(())
    }

    #[instrument(err, skip(self))]
    async fn delete_up_to_xact_id<'a>(
        &self,
        scope: WALScope<'a>,
        xact_id: TransactionId,
        dry_run: bool,
    ) -> Result<DeleteUpToXactIdOutput> {
        // This function deletes segment metadata earlier than `xact_id` from
        // the global store. We don't need to explicitly delete the WAL files
        // here, because without metadata in the global store these entries will
        // never be accessed. Stale WAL files should be removed by vacuuming.

        let segment_ids = vec![get_wal_scope_segment_id(&scope)];

        if dry_run {
            Ok(DeleteUpToXactIdOutput {
                planned_num_deletes: self
                    .global_store
                    .count_segment_wal_entries_up_to_xact_id(&segment_ids, xact_id)
                    .await?,
                num_deleted_files: 0,
            })
        } else {
            let num_deleted_files = self
                .global_store
                .delete_segment_wal_entries_up_to_xact_id(&segment_ids, xact_id)
                .await?;
            Ok(DeleteUpToXactIdOutput {
                planned_num_deletes: num_deleted_files,
                num_deleted_files,
            })
        }
    }
}

fn get_wal_scope_segment_id(scope: &WALScope) -> Uuid {
    match scope {
        WALScope::ObjectId(_, _) => {
            panic!("Object WAL not supported for ObjectAndGlobalStoreWal");
        }
        WALScope::Segment(segment_id) => *segment_id,
    }
}

fn compute_wal_entry_digest(bytes: &[u8]) -> i64 {
    let mut hasher = Sha256::new();
    hasher.update(bytes);
    let result = hasher.finalize();
    i64::from_le_bytes(result[0..8].try_into().unwrap())
}
