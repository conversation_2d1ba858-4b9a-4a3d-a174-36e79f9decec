use std::{collections::HashMap, sync::Arc};

use clap::Parser;
use futures::future::try_join_all;
use serde::{Deserialize, Serialize};
use util::{
    anyhow::{anyhow, Result},
    chrono::{Duration, Utc},
    schema::Schema,
    system_types::{make_object_schema, FullObjectId, FullObjectIdOwned},
    uuid::Uuid,
    xact::TransactionId,
};

use crate::{
    config_with_store::StoreInfo,
    global_locks_manager::GlobalLocksManager,
    global_store::{
        DeleteFromSegmentStats, GlobalStore, TaskInfo, TimeBasedRetentionCursor,
        TimeBasedRetentionInfo, TimeBasedRetentionOperation, TimeBasedRetentionState,
        TimeBasedRetentionStats,
    },
    index_document::make_full_schema,
    retention::{
        delete_from_segments_up_to_xact_id, DeleteFromSegmentsInput,
        DeleteFromSegmentsOptionalInput, DeleteFromSegmentsOptions,
    },
    retention_policy_lookup::{
        resolve_retention_policies, ControlPlaneContext, ResolveRetentionPoliciesInput,
        ResolveRetentionPoliciesOptionalInput, RetentionObject,
    },
    retention_util::RetentionObjectType,
};

#[derive(Clone, Debug)]
pub struct TimeBasedRetentionInput<'a> {
    // If object_ids is None, retention will run over all objects with an active retention policy.
    pub object_ids: Option<&'a [FullObjectId<'a>]>,
    pub global_store: Arc<dyn GlobalStore>,
    pub index_store: &'a StoreInfo,
    pub locks_manager: &'a dyn GlobalLocksManager,
    pub config_file_schema: Option<&'a Schema>,
    pub control_plane_ctx: Option<&'a ControlPlaneContext>,
    pub dry_run: bool,
}

#[derive(Clone, Debug, Default)]
pub struct TimeBasedRetentionOptionalInput<'a> {
    pub testing_force_error_for_object_id: Option<FullObjectId<'a>>,
    pub testing_force_error_for_segment_id: Option<Uuid>,
    // If provided, use these time-based retention policies instead of calling the API to resolve them.
    pub testing_object_id_to_retention_days: Option<HashMap<FullObjectId<'a>, i64>>,
}

#[derive(Clone, Debug, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionOptions {
    #[arg(
        long,
        default_value_t = default_time_based_retention_object_batch_size(),
        help = "Batch size for resolving object retention policies.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_OBJECT_BATCH_SIZE",
    )]
    pub time_based_retention_object_batch_size: usize,
    #[arg(
        long,
        default_value_t = default_time_based_retention_segment_batch_size(),
        help = "Number of segments to run time-based retention on in each iteration.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_SEGMENT_BATCH_SIZE",
    )]
    pub time_based_retention_segment_batch_size: usize,
    #[arg(
        long,
        default_value_t = default_time_based_retention_interval_seconds(),
        help = "Minimum wait between iterations of the time-based retention worker.",
        env = "BRAINSTORE_TIME_BASED_RETENTION_INTERVAL_SECONDS",
    )]
    pub time_based_retention_interval_seconds: i64,
}

impl Default for TimeBasedRetentionOptions {
    fn default() -> Self {
        Self {
            time_based_retention_object_batch_size: default_time_based_retention_object_batch_size(
            ),
            time_based_retention_segment_batch_size:
                default_time_based_retention_segment_batch_size(),
            time_based_retention_interval_seconds: default_time_based_retention_interval_seconds(),
        }
    }
}

fn default_time_based_retention_object_batch_size() -> usize {
    // NOTE: This was determined on staging while testing with very few retention policies.
    // We may want to tune this default further at some point.
    5000
}

fn default_time_based_retention_segment_batch_size() -> usize {
    // NOTE: We may want to tune this default once more segments are being processed by
    // the worker.
    100
}

fn default_time_based_retention_interval_seconds() -> i64 {
    // NOTE: As of 8/8/25 the retention loop takes ~4 minutes to complete on our
    // data plane with ~1mm objects so a 1 hour interval is reasonable for now.
    60 * 60 // 1 hour
}

const LOCK_NAME: &str = "time_based_retention";

struct ObjectSegmentsBatch {
    object_id: FullObjectIdOwned,
    retention_days: i64,
    segment_ids: Vec<Uuid>,
}

/// Run time-based retention on objects with active retention policies.
#[tracing::instrument(err, skip(input, optional_input), fields(object_ids = ?input.object_ids, dry_run = input.dry_run))]
pub async fn time_based_retention<'a>(
    input: TimeBasedRetentionInput<'a>,
    optional_input: TimeBasedRetentionOptionalInput<'a>,
    options: &TimeBasedRetentionOptions,
) -> Result<TimeBasedRetentionStats> {
    // Procedure:
    //
    // 1. If `current_op_start_ts` is null, there is no partially-completed run to resume, so start
    //    a new run. Reset the retention state and set `current_op_start_ts` to the current timestamp.
    // 2. Begin loop, resuming from the (object_id, segment_id) cursor, which may be null.
    //     - If the segment_id cursor is not nil, continue work on the current object by retrieving
    //       a new batch of segments for this object, up to a maximum of `segment_batch_size` segments.
    //       If this fills up a full batch of segments, skip to step 3.
    //     - If we need more segments, fetch object_ids past the object_id cursor. Look up their
    //       retention policies, discard objects without active retention policies, then fetch
    //       (object_id, segment_id) pairs for those objects. Continue in a loop until we fill up
    //       this batch of segments.
    //    We now have a batch of (object_id, retention_days, segment_ids) entries containing a maximum
    //    of `segment_batch_size` total segments.
    // 3. Process each entry in parallel; retention can be handled with a single call to
    //    `purge_segments_up_to_xact_id` per object. If all complete successfully, update the cursor.
    //    If any fail, write the error to the `operation` field and exit.
    // 4. Once we run out of segments to process, exit the loop.
    //     - To mark this run as completed, set last_successful_start_ts to this run's current_op_start_ts,
    //       then reset current_op_start_ts to null. Record any useful information from the completed run
    //       to the `operation` field.

    if let Some(object_ids) = input.object_ids {
        if object_ids.is_empty() {
            tracing::warn!("No object IDs provided, skipping time-based retention");
            return Ok(TimeBasedRetentionStats::default());
        }
    }

    let _lock = input.locks_manager.write(LOCK_NAME).await?;

    let input = &input;
    let optional_input = &optional_input;

    let mut state = input
        .global_store
        .query_time_based_retention_state()
        .await?;

    if state.current_op_start_ts.is_none() {
        // If we're starting a new run, first check that it's been long enough since the last successful
        // run's start time (last_successful_start_ts). If it has, then kick off a new run by setting
        // `current_op_start_ts` to the current time.
        let new_start_ts = Utc::now();
        let last_successful_start_ts = state.last_successful_start_ts;
        let duration_since_last_run =
            last_successful_start_ts.map(|ts| new_start_ts.signed_duration_since(ts));
        let interval_seconds = Duration::seconds(options.time_based_retention_interval_seconds);

        if let Some(duration_since_last_run) = duration_since_last_run {
            if duration_since_last_run < interval_seconds {
                let remaining_sleep_seconds =
                    (interval_seconds - duration_since_last_run).num_seconds();
                tracing::debug!(
                    duration_since_last_run = duration_since_last_run.num_seconds(),
                    remaining_sleep_seconds = remaining_sleep_seconds,
                    "Skipping this iteration of time-based retention",
                );
                return Ok(TimeBasedRetentionStats::default());
            }
        }

        state.current_op_start_ts = Some(new_start_ts);
        state.cursor = None;
        state.operation = TimeBasedRetentionOperation::default();

        input
            .global_store
            .upsert_time_based_retention_state(&state)
            .await?;

        tracing::info!(
            start_ts = ?state.current_op_start_ts,
            "Starting new time-based retention run",
        );
    } else {
        tracing::info!(
            start_ts = ?state.current_op_start_ts,
            "Continuing time-based retention run",
        );
    }

    let mut stats = state.operation.stats;

    loop {
        let result = retention_loop_iter(
            &mut state,
            RetentionLoopIterArgs {
                input,
                optional_input,
                options,
            },
        )
        .await;
        match result {
            Err(ref e) => {
                state.operation = TimeBasedRetentionOperation {
                    error: Some(e.to_string()),
                    completed_ts: None,
                    stats,
                };
            }
            Ok(ref output) => {
                stats = stats + output.stats;
                state.operation = TimeBasedRetentionOperation {
                    error: None,
                    completed_ts: None,
                    stats,
                };
            }
        }

        input
            .global_store
            .upsert_time_based_retention_state(&state)
            .await?;

        if result?.finished {
            break;
        }
    }

    // We're done. Set last_successful_start_ts to current_op_start_ts, then reset
    // current_op_start_ts to null to signify completion.
    state.last_successful_start_ts = state.current_op_start_ts;
    state.current_op_start_ts = None;
    state.operation = TimeBasedRetentionOperation {
        error: None,
        completed_ts: Some(Utc::now()),
        stats,
    };
    input
        .global_store
        .upsert_time_based_retention_state(&state)
        .await?;

    tracing::info!(
        num_objects = stats.num_processed_objects,
        num_segments = stats.num_processed_segments,
        segment_stats = ?stats.segment_stats,
        "Retention loop complete",
    );

    Ok(stats)
}

struct RetentionLoopIterArgs<'a> {
    input: &'a TimeBasedRetentionInput<'a>,
    optional_input: &'a TimeBasedRetentionOptionalInput<'a>,
    options: &'a TimeBasedRetentionOptions,
}

struct RetentionLoopIterOutput {
    finished: bool,
    stats: TimeBasedRetentionStats,
}

#[tracing::instrument(
    err,
    skip(state, args),
    fields(
        dry_run = args.input.dry_run,
        start_ts = ?state.current_op_start_ts,
        cursor = ?state.cursor,
    )
)]
async fn retention_loop_iter<'a>(
    state: &'a mut TimeBasedRetentionState,
    args: RetentionLoopIterArgs<'a>,
) -> Result<RetentionLoopIterOutput> {
    let input = args.input;
    let optional_input = args.optional_input;
    let options = args.options;

    let object_batch_size = options.time_based_retention_object_batch_size;
    let segment_batch_size = options.time_based_retention_segment_batch_size;

    let mut per_object_batches: Vec<ObjectSegmentsBatch> = Vec::new();
    let mut num_processed_objects = 0;
    let mut num_processed_segments = 0;

    // If we have an non-null object cursor, first try to fill up the batch with additional
    // segments from that object.
    if let Some(cursor) = state.cursor.clone() {
        let current_object_segments = input
            .global_store
            .list_object_segment_ids(
                cursor.object_id.as_ref(),
                cursor.segment_id,
                segment_batch_size,
            )
            .await?;

        if current_object_segments.is_empty() {
            num_processed_objects += 1;
            tracing::debug!(
                object_id = %cursor.object_id,
                "No remaining segments for object",
            );
        } else {
            // We still have segments to process for this object, so get the retention policy and add a
            // batch of segments to per_object_batches.
            let object_ids = [cursor.object_id.as_ref()];
            let object_id_to_retention_days = get_retention_days_for_objects(
                input.control_plane_ctx,
                &object_ids,
                optional_input.testing_object_id_to_retention_days.as_ref(),
            )
            .await?;
            let retention_days = object_id_to_retention_days.get(&cursor.object_id);

            if let Some(retention_days) = retention_days {
                let num_current_object_segments = current_object_segments.len();
                num_processed_segments += num_current_object_segments;
                per_object_batches.push(ObjectSegmentsBatch {
                    object_id: cursor.object_id.clone(),
                    retention_days: *retention_days,
                    segment_ids: current_object_segments,
                });
                tracing::debug!(
                    object_id = %cursor.object_id,
                    num_segments = num_current_object_segments,
                    retention_days = %retention_days,
                    "Added segments to batch from object with retention policy",
                );
            }
        }
    }

    // If we need more segments, get them from additional objects in a loop until we've
    // reached the segments batch size.
    let mut last_object_id = state.cursor.as_ref().map(|cursor| cursor.object_id.clone());
    while num_processed_segments < segment_batch_size {
        let objects = input
            .global_store
            .list_object_ids(
                last_object_id.as_ref().map(|o| o.as_ref()),
                object_batch_size,
                input.object_ids,
            )
            .await?;
        last_object_id = objects.last().cloned();
        if last_object_id.is_none() {
            break;
        }

        tracing::debug!(
            num_objects = objects.len(),
            "Fetched additional objects to fill batch",
        );

        // Get retention policies for the batch of objects, then discard any objects that
        // don't have active retention policies.
        let object_refs: Vec<FullObjectId> = objects.iter().map(|o| o.as_ref()).collect();
        let object_id_to_retention_days = get_retention_days_for_objects(
            input.control_plane_ctx,
            &object_refs,
            optional_input.testing_object_id_to_retention_days.as_ref(),
        )
        .await?;

        let object_ids = object_id_to_retention_days
            .keys()
            .map(|o| o.as_ref())
            .collect::<Vec<_>>();
        if object_ids.is_empty() {
            continue;
        }

        let num_additional_segments_needed = segment_batch_size - num_processed_segments;
        let object_ids_segment_ids = input
            .global_store
            .list_object_ids_segment_ids(&object_ids, num_additional_segments_needed)
            .await?;

        let mut object_id_to_segment_ids: HashMap<FullObjectIdOwned, Vec<Uuid>> = HashMap::new();
        for (object_id, segment_id) in object_ids_segment_ids {
            object_id_to_segment_ids
                .entry(object_id)
                .or_default()
                .push(segment_id);
        }

        for (object_id, segment_ids) in object_id_to_segment_ids {
            let retention_days = match object_id_to_retention_days.get(&object_id) {
                Some(retention_days) => *retention_days,
                None => {
                    return Err(anyhow!(
                        "Unexpected: missing retention policy for object ID {}. This should be impossible.",
                        object_id
                    ))
                }
            };

            let num_segments = segment_ids.len();
            num_processed_segments += num_segments;
            per_object_batches.push(ObjectSegmentsBatch {
                object_id: object_id.to_owned(),
                retention_days,
                segment_ids,
            });

            tracing::debug!(
                num_segments = num_segments,
                object_id = %object_id,
                retention_days = %retention_days,
                "Added segments to batch from object with retention policy",
            );
        }
    }

    if per_object_batches.is_empty() {
        // We're done, since there are no (object_id, segment_id) pairs left to process.
        return Ok(RetentionLoopIterOutput {
            finished: true,
            stats: TimeBasedRetentionStats {
                num_processed_objects: num_processed_objects as u64,
                num_processed_segments: num_processed_segments as u64,
                segment_stats: DeleteFromSegmentStats::default(),
            },
        });
    }

    // Process each object in parallel.
    let batch_stats = try_join_all(
        per_object_batches
            .iter()
            .map(|batch| process_object_segments_batch(input, optional_input, batch)),
    )
    .await?;

    // We finished processing all objects except the last one, which will be checked for
    // additional segments on the next iteration before moving on.
    num_processed_objects += batch_stats.len() - 1;
    let segment_stats: DeleteFromSegmentStats = batch_stats.into_iter().sum();

    tracing::info!(
        num_batches = per_object_batches.len(),
        num_segments = num_processed_segments,
        num_deleted_wal_entries = segment_stats.wal_stats.num_deleted_wal_entries,
        segment_stats = ?segment_stats,
        "Completed time-based retention on objects batch",
    );

    // Update the cursor to the last processed (object_id, segment_id) pair.
    let last_batch = per_object_batches
        .last()
        .expect("Impossible: per_object_batches is empty");
    let last_segment_id = last_batch
        .segment_ids
        .last()
        .expect("Impossible: batch has empty segment_ids");
    state.cursor = Some(TimeBasedRetentionCursor {
        object_id: last_batch.object_id.to_owned(),
        segment_id: *last_segment_id,
    });

    Ok(RetentionLoopIterOutput {
        finished: false,
        stats: TimeBasedRetentionStats {
            num_processed_objects: num_processed_objects as u64,
            num_processed_segments: num_processed_segments as u64,
            segment_stats,
        },
    })
}

async fn get_retention_days_for_objects<'a, 'b>(
    control_plane_ctx: Option<&'a ControlPlaneContext>,
    object_ids: &'b [FullObjectId<'b>],
    testing_object_id_to_retention_days: Option<&HashMap<FullObjectId<'b>, i64>>,
) -> Result<HashMap<FullObjectIdOwned, i64>> {
    if let Some(ref testing_policies) = testing_object_id_to_retention_days {
        Ok(object_ids
            .iter()
            .filter_map(|&object_id| {
                testing_policies
                    .get(&object_id)
                    .map(|&retention| (object_id.to_owned(), retention))
            })
            .collect())
    } else {
        let control_plane_ctx = control_plane_ctx.ok_or(anyhow!(
            "Control plane context is required for retention policy resolution"
        ))?;
        let retention_objects: Vec<_> = object_ids
            .iter()
            .filter_map(|&object_id| {
                RetentionObjectType::try_from(object_id.object_type)
                    .ok()
                    .map(|retention_type| RetentionObject {
                        object_type: retention_type,
                        object_id: object_id.object_id.to_owned(),
                    })
            })
            .collect();
        let retention_object_to_policies = resolve_retention_policies(
            ResolveRetentionPoliciesInput {
                control_plane_ctx,
                objects: retention_objects,
            },
            ResolveRetentionPoliciesOptionalInput::default(),
        )
        .await?;

        let object_id_to_retention_days: HashMap<FullObjectIdOwned, i64> = object_ids
            .iter()
            .filter_map(|&object_id| {
                let object_id_owned = object_id.to_owned();
                retention_object_to_policies
                    .get(&object_id_owned)
                    .map(|policy| (object_id_owned, policy.retention_days()))
            })
            .collect();

        tracing::info!(
            num_object_ids = object_ids.len(),
            num_retention_policies = object_id_to_retention_days.len(),
            "Resolved retention policies for object IDs"
        );

        Ok(object_id_to_retention_days)
    }
}

/// Process segments for a single object with a single retention policy.
/// This enables parallel processing since each call requires exactly one purge_segments_up_to_xact_id call.
#[tracing::instrument(
    err,
    skip(input, optional_input, batch),
    fields(
        dry_run = input.dry_run,
        object_id = %batch.object_id,
        num_segments = batch.segment_ids.len(),
    )
)]
async fn process_object_segments_batch(
    input: &TimeBasedRetentionInput<'_>,
    optional_input: &TimeBasedRetentionOptionalInput<'_>,
    batch: &ObjectSegmentsBatch,
) -> Result<DeleteFromSegmentStats> {
    if optional_input.testing_force_error_for_object_id == Some(batch.object_id.as_ref()) {
        return Err(anyhow!("Forced error for object ID {}", batch.object_id));
    }
    for &segment_id in &batch.segment_ids {
        if optional_input.testing_force_error_for_segment_id == Some(segment_id) {
            return Err(anyhow!("Forced error for segment ID {}", segment_id));
        }
    }

    let schema = &match input.config_file_schema {
        Some(schema) => make_full_schema(schema)?,
        None => make_full_schema(&make_object_schema(batch.object_id.object_type)?)?,
    };

    // The deletion cutoff is `retention_days` before the current time. Compute the
    // transaction ID corresponding to this timestamp, then run the delete operation.
    let start_ts = Utc::now();
    let retention_cutoff_ts = start_ts - Duration::days(batch.retention_days);
    let min_retained_xact_id =
        TransactionId::from_timestamp(retention_cutoff_ts.timestamp() as u64, 0);

    let result = delete_from_segments_up_to_xact_id(
        DeleteFromSegmentsInput {
            segment_ids: &batch.segment_ids,
            index_store: input.index_store,
            schema,
            global_store: input.global_store.clone(),
            locks_manager: input.locks_manager,
            min_retained_xact_id,
            dry_run: input.dry_run,
        },
        DeleteFromSegmentsOptionalInput::default(),
        DeleteFromSegmentsOptions::default(),
    )
    .await;

    let (batch_stats, completed_ts, error) = match result {
        Ok(stats) => (Some(stats), Some(Utc::now()), None),
        Err(ref e) => (None, None, Some(e.to_string())),
    };

    input
        .global_store
        .upsert_segment_task_info(
            &batch.segment_ids,
            &TaskInfo::TimeBasedRetention(TimeBasedRetentionInfo {
                start_ts,
                completed_ts,
                retention_days: batch.retention_days,
                min_retained_xact_id,
                error,
                batch_stats,
            }),
        )
        .await?;

    result
}

#[tracing::instrument(err, skip(input, optional_input), fields(object_ids = ?input.object_ids))]
pub async fn time_based_retention_stateless<'a>(
    input: TimeBasedRetentionInput<'a>,
    optional_input: TimeBasedRetentionOptionalInput<'a>,
    options: &TimeBasedRetentionOptions,
) -> Result<TimeBasedRetentionStats> {
    // Validate that object_ids is provided and not empty
    let object_ids = input
        .object_ids
        .ok_or_else(|| anyhow!("object_ids is required for stateless retention operations"))?;
    if object_ids.is_empty() {
        tracing::debug!("No object IDs provided, skipping stateless time-based retention");
        return Ok(TimeBasedRetentionStats::default());
    }

    tracing::debug!(
        num_objects = object_ids.len(),
        "Running stateless time-based retention",
    );

    let mut local_state = TimeBasedRetentionState {
        last_successful_start_ts: None,
        current_op_start_ts: Some(Utc::now()),
        cursor: None,
        operation: TimeBasedRetentionOperation::default(),
    };
    let mut total_stats = TimeBasedRetentionStats::default();

    loop {
        let result = retention_loop_iter(
            &mut local_state,
            RetentionLoopIterArgs {
                input: &input,
                optional_input: &optional_input,
                options,
            },
        )
        .await?;

        total_stats = total_stats + result.stats;

        if result.finished {
            break;
        }
    }

    tracing::info!(
        num_objects = total_stats.num_processed_objects,
        num_segments = total_stats.num_processed_segments,
        num_deleted_wal_entries = total_stats.segment_stats.wal_stats.num_deleted_wal_entries,
        "Completed stateless time-based retention run",
    );

    Ok(total_stats)
}
