use async_stream::stream;
use async_trait::async_trait;
use futures::{stream::BoxStream, StreamExt};
use lazy_static::lazy_static;
use object_store::{
    path::Path, GetOptions, GetResult, ListResult, ObjectMeta, ObjectStore, PutO<PERSON>s, PutResult,
    Result,
};
use otel_common::opentelemetry::{metrics::Counter, KeyValue};
use std::sync::Arc;
use tracing::instrument;

use crate::timer::{OtelTimer, TimerManager};
use crate::{
    limits::{global_limits, AsyncLimit},
    otel_time,
};

struct ObjectStoreMeters {
    put: OtelTimer,
    get: OtelTimer,
    head: OtelTimer,
    delete: OtelTimer,
    list: OtelTimer,
    copy: OtelTimer,
    rename: OtelTimer,
    bytes_read: Counter<u64>,
    bytes_written: Counter<u64>,
    read_errors: Counter<u64>,
    write_errors: Counter<u64>,
    list_files_returned: Counter<u64>,
}
impl ObjectStoreMeters {
    pub fn new() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            put: OtelTimer::new(&meter, "brainstore.storage.object_store.put"),
            get: OtelTimer::new(&meter, "brainstore.storage.object_store.get"),
            head: OtelTimer::new(&meter, "brainstore.storage.object_store.head"),
            delete: OtelTimer::new(&meter, "brainstore.storage.object_store.delete"),
            list: OtelTimer::new(&meter, "brainstore.storage.object_store.list"),
            copy: OtelTimer::new(&meter, "brainstore.storage.object_store.copy"),
            rename: OtelTimer::new(&meter, "brainstore.storage.object_store.rename"),
            bytes_read: meter
                .u64_counter("brainstore.storage.object_store.read.bytes")
                .build(),
            bytes_written: meter
                .u64_counter("brainstore.storage.object_store.write.bytes")
                .build(),
            read_errors: meter
                .u64_counter("brainstore.storage.object_store.read_errors")
                .build(),
            write_errors: meter
                .u64_counter("brainstore.storage.object_store.write_errors")
                .build(),
            list_files_returned: meter
                .u64_counter("brainstore.storage.object_store.list.files_returned")
                .build(),
        }
    }
}

lazy_static! {
    static ref OBJECT_STORE_METERS: ObjectStoreMeters = ObjectStoreMeters::new();
}

/// ResourceLimitedObjectStore is a wrapper around an ObjectStore that uses the global rate limits
/// from the limits module to rate-limit individual operations.
#[derive(Debug)]
pub struct ResourceLimitedObjectStore {
    object_store: Box<dyn ObjectStore>,
    debug_timer: Arc<TimerManager>,
}

impl ResourceLimitedObjectStore {
    pub fn new(object_store: Box<dyn ObjectStore>) -> Self {
        Self {
            object_store,
            debug_timer: TimerManager::new("ObjectStore"),
        }
    }
}

impl std::fmt::Display for ResourceLimitedObjectStore {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{:?}", self)
    }
}

// NOTE: We skip instrumenting the errors here, because some of the errors like
// FileDoesNotExist are handled as expected and we don't want to spam logs.
#[async_trait]
impl ObjectStore for ResourceLimitedObjectStore {
    #[instrument(level = "debug", skip(self, payload), fields(payload_size = payload.content_length()))]
    async fn put_opts(
        &self,
        location: &Path,
        payload: object_store::PutPayload,
        opts: PutOptions,
    ) -> Result<PutResult> {
        let _permit = acquire_permit(&global_limits().object_store_write).await?;
        otel_time!(self.debug_timer, &OBJECT_STORE_METERS.put, "put");

        let payload_size = payload.content_length();
        let result = self.object_store.put_opts(location, payload, opts).await;

        match &result {
            Ok(_) => {
                OBJECT_STORE_METERS
                    .bytes_written
                    .add(payload_size as u64, &[]);
            }
            Err(_) => {
                OBJECT_STORE_METERS.write_errors.add(1, &[]);
            }
        }

        result
    }

    #[instrument(level = "debug", skip(self))]
    async fn put_multipart_opts(
        &self,
        location: &Path,
        opts: object_store::PutMultipartOpts,
    ) -> Result<Box<dyn object_store::MultipartUpload>> {
        let _permit = acquire_permit(&global_limits().object_store_write).await?;
        otel_time!(self.debug_timer, &OBJECT_STORE_METERS.put, "put_multipart");

        let result = self.object_store.put_multipart_opts(location, opts).await;

        if result.is_err() {
            OBJECT_STORE_METERS.write_errors.add(1, &[]);
        }

        result
    }

    #[instrument(level = "debug", skip(self))]
    async fn get_opts(&self, location: &Path, options: GetOptions) -> Result<GetResult> {
        let _permit = acquire_permit(&global_limits().object_store_read).await?;

        // Prepare path attribute for metrics
        let filename = location.filename().unwrap_or("");
        let path_attrs = vec![if filename == "meta.json" {
            KeyValue::new("path", "meta.json")
        } else if let Some(ext) = location.extension() {
            KeyValue::new("path", format!(".{}", ext))
        } else {
            KeyValue::new("path", "")
        }];

        otel_time!(
            self.debug_timer,
            &OBJECT_STORE_METERS.get,
            "get",
            path_attrs.clone()
        );

        let result = self.object_store.get_opts(location, options).await;

        match &result {
            Ok(get_result) => {
                // Track bytes read from meta.size
                OBJECT_STORE_METERS.bytes_read.add(
                    (get_result.range.end - get_result.range.start) as u64,
                    &path_attrs,
                );
            }
            Err(object_store::Error::NotFound { .. }) => { /* dont track not found errors */ }
            Err(_) => {
                log::error!(
                    "Error getting object at {}: {:?}",
                    location,
                    result.as_ref().err()
                );
                OBJECT_STORE_METERS.read_errors.add(1, &path_attrs);
            }
        }

        result
    }

    #[instrument(level = "debug", skip(self))]
    async fn delete(&self, location: &Path) -> Result<()> {
        let _permit = acquire_permit(&global_limits().object_store_write).await?;
        otel_time!(self.debug_timer, &OBJECT_STORE_METERS.delete, "delete");

        let result = self.object_store.delete(location).await;

        if result.is_err() {
            OBJECT_STORE_METERS.write_errors.add(1, &[]);
        }

        result
    }

    #[instrument(level = "debug", skip(self, locations))]
    fn delete_stream<'a>(
        &'a self,
        locations: BoxStream<'a, Result<Path>>,
    ) -> BoxStream<'a, Result<Path>> {
        stream! {
            let _permit = acquire_permit(&global_limits().object_store_write).await?;
            let mut out_stream = self.object_store.delete_stream(locations);
            let mut delete_count = 0u64;
            while let Some(obj) = out_stream.next().await {
                if obj.is_ok() {
                    delete_count += 1;
                }
                yield obj;
            }
            // NOTE: Delete timing won't quite be accurate, so just count the number of delete calls.
            OBJECT_STORE_METERS.delete.counter_meter.add(delete_count, &[]);
        }
        .boxed()
    }

    #[instrument(level = "debug", skip(self))]
    fn list(&self, prefix: Option<&Path>) -> BoxStream<'static, Result<ObjectMeta>> {
        let prefix = prefix.map(|p| p.to_owned());
        let debug_timer = self.debug_timer.clone();
        let mut out_stream = self.object_store.list(prefix.as_ref());
        stream! {
            let _permit = acquire_permit(&global_limits().object_store_read).await?;
            otel_time!(debug_timer, &OBJECT_STORE_METERS.list, "list");
            let mut file_count = 0u64;
            while let Some(obj) = out_stream.next().await {
                if obj.is_ok() {
                    file_count += 1;
                }
                yield obj;
            }
            OBJECT_STORE_METERS.list_files_returned.add(file_count, &[]);
        }
        .boxed()
    }

    #[instrument(level = "debug", skip(self))]
    fn list_with_offset(
        &self,
        prefix: Option<&Path>,
        offset: &Path,
    ) -> BoxStream<'static, Result<ObjectMeta>> {
        let prefix = prefix.map(|p| p.to_owned());
        let offset = offset.to_owned();
        let debug_timer = self.debug_timer.clone();
        let mut out_stream = self.object_store.list_with_offset(prefix.as_ref(), &offset);
        stream! {
            let _permit = acquire_permit(&global_limits().object_store_read).await?;
            otel_time!(debug_timer, &OBJECT_STORE_METERS.list, "list_with_offset");
            let mut file_count = 0u64;
            while let Some(obj) = out_stream.next().await {
                if obj.is_ok() {
                    file_count += 1;
                }
                yield obj;
            }
            OBJECT_STORE_METERS.list_files_returned.add(file_count, &[]);
        }
        .boxed()
    }

    #[instrument(level = "debug", skip(self))]
    async fn list_with_delimiter(&self, prefix: Option<&Path>) -> Result<ListResult> {
        let _permit = acquire_permit(&global_limits().object_store_read).await?;
        otel_time!(
            self.debug_timer,
            &OBJECT_STORE_METERS.list,
            "list_with_delimiter"
        );

        let result = self.object_store.list_with_delimiter(prefix).await;

        match &result {
            Ok(list_result) => {
                let file_count = list_result.objects.len() as u64;
                OBJECT_STORE_METERS.list_files_returned.add(file_count, &[]);
            }
            Err(_) => {
                OBJECT_STORE_METERS.read_errors.add(1, &[]);
            }
        }

        result
    }

    #[instrument(level = "debug", skip(self))]
    async fn copy(&self, from: &Path, to: &Path) -> Result<()> {
        let _permit = acquire_permit(&global_limits().object_store_write).await?;
        otel_time!(self.debug_timer, &OBJECT_STORE_METERS.copy, "copy");

        let result = self.object_store.copy(from, to).await;

        if result.is_err() {
            OBJECT_STORE_METERS.write_errors.add(1, &[]);
        }

        result
    }

    #[instrument(level = "debug", skip(self))]
    async fn rename(&self, from: &Path, to: &Path) -> Result<()> {
        let _permit = acquire_permit(&global_limits().object_store_write).await?;
        otel_time!(self.debug_timer, &OBJECT_STORE_METERS.rename, "rename");

        let result = self.object_store.rename(from, to).await;

        if result.is_err() {
            OBJECT_STORE_METERS.write_errors.add(1, &[]);
        }

        result
    }

    #[instrument(level = "debug", skip(self))]
    async fn copy_if_not_exists(&self, from: &Path, to: &Path) -> Result<()> {
        let _permit = acquire_permit(&global_limits().object_store_write).await?;
        otel_time!(
            self.debug_timer,
            &OBJECT_STORE_METERS.copy,
            "copy_if_not_exists"
        );

        let result = self.object_store.copy_if_not_exists(from, to).await;

        if result.is_err() {
            OBJECT_STORE_METERS.write_errors.add(1, &[]);
        }

        result
    }

    #[instrument(level = "debug", skip(self))]
    async fn rename_if_not_exists(&self, from: &Path, to: &Path) -> Result<()> {
        let _permit = acquire_permit(&global_limits().object_store_write).await?;
        otel_time!(
            self.debug_timer,
            &OBJECT_STORE_METERS.rename,
            "rename_if_not_exists"
        );

        let result = self.object_store.rename_if_not_exists(from, to).await;

        if result.is_err() {
            OBJECT_STORE_METERS.write_errors.add(1, &[]);
        }

        result
    }

    #[instrument(level = "debug", skip(self))]
    async fn head(&self, location: &Path) -> Result<ObjectMeta> {
        let _permit = acquire_permit(&global_limits().object_store_read).await?;
        otel_time!(self.debug_timer, &OBJECT_STORE_METERS.head, "head");

        let result = self.object_store.head(location).await;

        if result.is_err() {
            OBJECT_STORE_METERS.read_errors.add(1, &[]);
        }

        result
    }
}

#[instrument(level = "debug", skip(limit), fields(limit_name = limit.name))]
async fn acquire_permit(
    limit: &AsyncLimit,
) -> Result<tokio::sync::SemaphorePermit<'_>, object_store::Error> {
    match limit.start_task().await {
        Ok(permit) => Ok(permit),
        Err(err) => Err(object_store::Error::Generic {
            store: "ResourceLimitedObjectStore",
            source: err.into(),
        }),
    }
}
