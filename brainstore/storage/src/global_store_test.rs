use once_cell::sync::Lazy;
use std::{
    collections::{HashMap, HashSet},
    path::Path,
    sync::Arc,
};

use futures::future::join_all;
use util::{
    chrono::{Duration, TimeZone, Utc},
    system_types::{
        FullObjectId, FullObjectIdOwned, FullRowId, ObjectId, ObjectIdOwned, ObjectType,
    },
    url_util::str_to_url,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    config_with_store::url_to_store,
    directory::{
        cached_directory::{FileCacheDirectory, FileCacheOpts},
        object_store_directory::ObjectStoreDirectory,
        AsyncDirectoryArc,
    },
    global_locks_manager::{GlobalLocksManager, MemoryGlobalLocksManager},
    global_store::{
        compute_next_wal_entry_cursor, BackfillTrackingEntry, BackfillTrackingEntryId,
        BackfillTrackingEntryUpdate, DeleteFromSegmentIndexStats, DeleteFromSegmentStats,
        DeleteFromSegmentWalStats, DirectoryGlobalStore, GlobalStore, IdSegmentMembershipType,
        LastCompactedIndexMeta, LastIndexOpTokenOpts, LastIndexOperation,
        LastIndexOperationDetails, ListSegmentIdsGlobalOptionalInput, MemoryGlobalStore,
        ObjectMetadataUpdate, SegmentFieldStatistics, SegmentIdPaginationArgs, SegmentLiveness,
        SegmentMetadata, SegmentMetadataUpdate, SegmentWalEntriesCursor,
        SegmentWalEntriesXactIdStatistic, SegmentWalEntry, TaskInfo,
        TestingOnlyBackfillBrainstoreObjectAtom, TimeBasedRetentionCursor, TimeBasedRetentionInfo,
        TimeBasedRetentionOperation, TimeBasedRetentionState, TimeBasedRetentionStats,
        UpsertSegmentWalEntry,
    },
    postgres_global_store::PostgresGlobalStore,
    postgres_pool::PostgresPool,
    test_util::PostgresContainer,
    vacuum::VacuumType,
};

static FULL_OBJECT_ID0: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj0").unwrap(),
});

static FULL_OBJECT_ID1: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj1").unwrap(),
});

static FULL_OBJECT_ID_CONCURRENT: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj_concurrent").unwrap(),
});

static FULL_OBJECT_ID_RETENTION: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj_retention").unwrap(),
});

fn sorted_segments(segments: Vec<Uuid>) -> Vec<Uuid> {
    let mut segments = segments;
    segments.sort();
    segments
}

fn sorted_segments2(mut segments: Vec<Vec<Uuid>>) -> Vec<Vec<Uuid>> {
    for segs in &mut segments {
        segs.sort();
    }
    segments
}

async fn get_last_processed_xact_ids(
    store: &dyn GlobalStore,
    object_ids: &[FullObjectId<'_>],
) -> Vec<Option<TransactionId>> {
    store
        .query_object_metadatas(object_ids)
        .await
        .unwrap()
        .into_iter()
        .map(|x| x.last_processed_xact_id)
        .collect()
}

async fn get_last_processed_xact_id(
    store: &dyn GlobalStore,
    object_id: FullObjectId<'_>,
) -> Option<TransactionId> {
    get_last_processed_xact_ids(store, &[object_id])
        .await
        .remove(0)
}

async fn cas_last_processed_xact_id(
    store: &dyn GlobalStore,
    object_id: FullObjectId<'_>,
    old: Option<TransactionId>,
    new: Option<TransactionId>,
) -> bool {
    store
        .upsert_object_metadatas(
            [(
                object_id,
                ObjectMetadataUpdate {
                    last_processed_xact_id: Some((old, new)),
                },
            )]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap()
}

async fn get_wal_tokens(store: &dyn GlobalStore, object_ids: &[FullObjectId<'_>]) -> Vec<Uuid> {
    store
        .query_object_metadatas(object_ids)
        .await
        .unwrap()
        .into_iter()
        .map(|x| x.wal_token)
        .collect()
}

async fn test_global_store_impl(store: Arc<dyn GlobalStore>) {
    // Status check should succeed.
    store.status().await.unwrap();

    test_initial_segment_id_ops(&*store).await;
    test_object_metadata_ops(&*store).await;
    test_segment_metadata_ops(&*store).await;
    test_segment_row_info(&*store).await;
    test_concurrent_update_segment_ids(&*store).await;
    test_enormous_row_id_root_span_id_queries(&*store).await;
    test_segment_wal_entries(&*store).await;
    test_last_index_operations(&*store).await;
    test_query_recently_updated_objects_and_list_object_ids(&*store).await;
    test_field_statistics(&*store).await;
    test_wal_entries_existence_enormous(&*store).await;
    test_retention(store.clone()).await;
    test_retention_restoration(&*store).await;
    test_retention_worker(&*store).await;
    test_vacuum(&*store).await;
    test_backfill_functionality(&*store).await;
}

async fn test_initial_segment_id_ops(store: &dyn GlobalStore) {
    // Initially should be empty
    assert!(store
        .list_segment_ids_global(None)
        .await
        .unwrap()
        .is_empty());
    assert!(store
        .list_segment_ids(&[*FULL_OBJECT_ID0], None)
        .await
        .unwrap()
        .remove(0)
        .is_empty());
    assert_eq!(
        get_last_processed_xact_id(store, *FULL_OBJECT_ID0).await,
        None
    );

    let seg1 = Uuid::new_v4();
    let seg2 = Uuid::new_v4();
    let seg3 = Uuid::new_v4();

    // Add some segments
    store
        .update_segment_ids(&[
            (*FULL_OBJECT_ID0, &[seg1, seg2], &[]),
            (*FULL_OBJECT_ID1, &[seg3], &[]),
        ])
        .await
        .unwrap();
    // Empty inputs should also work.
    store
        .update_segment_ids(&[(*FULL_OBJECT_ID1, &[], &[])])
        .await
        .unwrap();

    // Check global list
    assert_eq!(
        sorted_segments(store.list_segment_ids_global(None).await.unwrap()),
        sorted_segments(vec![seg1, seg2, seg3])
    );

    // Check global list with pagination.
    let sorted_segment_ids = sorted_segments(vec![seg1, seg2, seg3]);
    assert_eq!(
        store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: Some(SegmentIdPaginationArgs {
                    segment_id_cursor: None,
                    limit: 2,
                }),
                ..Default::default()
            }))
            .await
            .unwrap(),
        sorted_segment_ids[..2].to_vec(),
    );
    assert_eq!(
        store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: Some(SegmentIdPaginationArgs {
                    segment_id_cursor: Some(Uuid::nil()),
                    limit: 4,
                }),
                ..Default::default()
            }))
            .await
            .unwrap(),
        sorted_segment_ids.to_vec(),
    );
    assert_eq!(
        store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: Some(SegmentIdPaginationArgs {
                    segment_id_cursor: Some(sorted_segment_ids[0]),
                    limit: 1,
                }),
                ..Default::default()
            }))
            .await
            .unwrap(),
        sorted_segment_ids[1..2].to_vec(),
    );
    assert_eq!(
        store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: Some(SegmentIdPaginationArgs {
                    segment_id_cursor: Some(sorted_segment_ids[2]),
                    limit: 1,
                }),
                ..Default::default()
            }))
            .await
            .unwrap(),
        Vec::<Uuid>::new(),
    );

    // There are no dead segments yet, so global list with `is_live = false` should return nothing.
    assert_eq!(
        store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: None,
                is_live: false,
            }))
            .await
            .unwrap(),
        Vec::<Uuid>::new(),
    );
    assert_eq!(
        store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: Some(SegmentIdPaginationArgs {
                    segment_id_cursor: Some(Uuid::nil()),
                    limit: 4,
                }),
                is_live: false,
            }))
            .await
            .unwrap(),
        Vec::<Uuid>::new(),
    );

    // Check per-object lists
    assert_eq!(
        sorted_segments2(
            store
                .list_segment_ids(&[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1], None)
                .await
                .unwrap()
        ),
        sorted_segments2(vec![vec![seg1, seg2], vec![seg3]]),
    );

    // Check liveness.
    assert_eq!(
        store
            .query_segment_liveness(&[seg1, seg2, seg3])
            .await
            .unwrap(),
        vec![
            SegmentLiveness {
                object_id: FULL_OBJECT_ID0.to_owned(),
                is_live: true
            },
            SegmentLiveness {
                object_id: FULL_OBJECT_ID0.to_owned(),
                is_live: true
            },
            SegmentLiveness {
                object_id: FULL_OBJECT_ID1.to_owned(),
                is_live: true
            },
        ]
    );
    assert_eq!(store.query_segment_liveness(&[]).await.unwrap(), vec![]);

    // Remove some segments
    store
        .update_segment_ids(&[(*FULL_OBJECT_ID0, &[], &[seg1])])
        .await
        .unwrap();
    assert_eq!(
        sorted_segments2(
            store
                .list_segment_ids(&[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1], None)
                .await
                .unwrap()
        ),
        vec![vec![seg2], vec![seg3]],
    );
    assert_eq!(
        sorted_segments(store.list_segment_ids_global(None).await.unwrap()),
        sorted_segments(vec![seg2, seg3])
    );
    assert_eq!(
        sorted_segments(
            store
                .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                    pagination_args: None,
                    is_live: false,
                }))
                .await
                .unwrap()
        ),
        sorted_segments(vec![seg1])
    );
    store
        .update_segment_ids(&[(*FULL_OBJECT_ID0, &[], &[])])
        .await
        .unwrap();

    assert_eq!(
        store.query_segment_liveness(&[seg1, seg3]).await.unwrap(),
        vec![
            SegmentLiveness {
                object_id: FULL_OBJECT_ID0.to_owned(),
                is_live: false
            },
            SegmentLiveness {
                object_id: FULL_OBJECT_ID1.to_owned(),
                is_live: true
            },
        ]
    );

    // Insert more segments, mark them non-live, then verify that global list pagination
    // with the `is_live = false` filter works.
    let dead_seg1 = Uuid::new_v4();
    let dead_seg2 = Uuid::new_v4();
    store
        .update_segment_ids(&[(*FULL_OBJECT_ID1, &[dead_seg1, dead_seg2], &[])])
        .await
        .unwrap();
    store
        .update_segment_ids(&[(*FULL_OBJECT_ID1, &[], &[dead_seg1, dead_seg2])])
        .await
        .unwrap();
    let sorted_dead_segment_ids = sorted_segments(vec![seg1, dead_seg1, dead_seg2]);
    assert_eq!(
        store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: Some(SegmentIdPaginationArgs {
                    segment_id_cursor: None,
                    limit: 2,
                }),
                is_live: false,
            }))
            .await
            .unwrap(),
        sorted_dead_segment_ids[..2].to_vec(),
    );
    assert_eq!(
        store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: Some(SegmentIdPaginationArgs {
                    segment_id_cursor: Some(Uuid::nil()),
                    limit: 4,
                }),
                is_live: false,
            }))
            .await
            .unwrap(),
        sorted_dead_segment_ids.to_vec(),
    );
    assert_eq!(
        store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: Some(SegmentIdPaginationArgs {
                    segment_id_cursor: Some(sorted_dead_segment_ids[0]),
                    limit: 1,
                }),
                is_live: false,
            }))
            .await
            .unwrap(),
        sorted_dead_segment_ids[1..2].to_vec(),
    );
    assert_eq!(
        store
            .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                pagination_args: Some(SegmentIdPaginationArgs {
                    segment_id_cursor: Some(sorted_dead_segment_ids[2]),
                    limit: 1,
                }),
                is_live: false,
            }))
            .await
            .unwrap(),
        Vec::<Uuid>::new(),
    );

    // Test error cases
    let seg_nonexistent = Uuid::new_v4();
    assert!(store
        .update_segment_ids(&[(*FULL_OBJECT_ID0, &[seg2], &[])])
        .await
        .is_err()); // Duplicate
    assert!(store
        .update_segment_ids(&[(*FULL_OBJECT_ID1, &[seg1], &[])])
        .await
        .is_err()); // Duplicate of removed
    assert!(store
        .update_segment_ids(&[(*FULL_OBJECT_ID0, &[], &[seg_nonexistent])])
        .await
        .is_err()); // Not found
    assert!(store
        .update_segment_ids(&[(*FULL_OBJECT_ID1, &[], &[seg_nonexistent])])
        .await
        .is_err()); // Not found
    assert!(store
        .update_segment_ids(&[(*FULL_OBJECT_ID0, &[], &[seg3])])
        .await
        .is_err()); // Part of a different object

    // Test atomicity of failed updates.
    assert!(store
        .update_segment_ids(&[(*FULL_OBJECT_ID0, &[], &[seg2, seg3])])
        .await
        .is_err());
    assert_eq!(
        sorted_segments2(
            store
                .list_segment_ids(&[*FULL_OBJECT_ID0], None)
                .await
                .unwrap()
        ),
        vec![vec![seg2]],
    );

    let seg4 = Uuid::new_v4();
    assert!(store
        .update_segment_ids(&[(*FULL_OBJECT_ID0, &[seg4, seg2], &[])])
        .await
        .is_err());
    assert_eq!(
        sorted_segments2(
            store
                .list_segment_ids(&[*FULL_OBJECT_ID0], None)
                .await
                .unwrap()
        ),
        vec![vec![seg2]],
    );

    // Cannot purge live segments.
    assert!(store.purge_segment_ids(&[seg2]).await.is_err());
    assert!(store.purge_segment_metadatas(&[seg2]).await.is_err());

    // But once we mark everything as deleted, we can purge away.
    store
        .update_segment_ids(&[
            (*FULL_OBJECT_ID0, &[], &[seg2]),
            (*FULL_OBJECT_ID1, &[], &[seg3]),
        ])
        .await
        .unwrap();

    // Purging is item-by-item. Also fine to re-purge and purge nothing.
    for seg in [seg1, seg2, seg3] {
        store.query_segment_liveness(&[seg]).await.unwrap();
    }
    store.purge_segment_ids(&[seg1, seg2, seg3]).await.unwrap();
    store.purge_segment_ids(&[seg1, seg2, seg3]).await.unwrap();
    store.purge_segment_ids(&[]).await.unwrap();
    for seg in [seg1, seg2, seg3] {
        assert!(store.query_segment_liveness(&[seg]).await.is_err());
    }
}

async fn test_object_metadata_ops(store: &dyn GlobalStore) {
    // Test object last-processed xact ID.
    assert!(
        cas_last_processed_xact_id(store, *FULL_OBJECT_ID0, None, Some(TransactionId(42))).await
    );
    assert!(
        !cas_last_processed_xact_id(store, *FULL_OBJECT_ID0, None, Some(TransactionId(43))).await
    );
    assert_eq!(
        get_last_processed_xact_id(store, *FULL_OBJECT_ID0).await,
        Some(TransactionId(42))
    );
    assert!(
        cas_last_processed_xact_id(
            store,
            *FULL_OBJECT_ID0,
            Some(TransactionId(42)),
            Some(TransactionId(43))
        )
        .await
    );
    assert!(
        cas_last_processed_xact_id(store, *FULL_OBJECT_ID1, None, Some(TransactionId(44))).await
    );
    assert_eq!(
        get_last_processed_xact_ids(store, &[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1]).await,
        vec![Some(TransactionId(43)), Some(TransactionId(44))]
    );

    // Test WAL tokens.
    let orig_wal_tokens = get_wal_tokens(store, &[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1]).await;
    assert_eq!(orig_wal_tokens.len(), 2);
    assert_eq!(
        get_wal_tokens(store, &[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1]).await,
        orig_wal_tokens
    );

    assert!(
        get_last_processed_xact_ids(store, &[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1])
            .await
            .iter()
            .all(|x| x.is_some())
    );
    store
        .purge_object_metadatas(&[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1])
        .await
        .unwrap();
    assert!(
        get_last_processed_xact_ids(store, &[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1])
            .await
            .iter()
            .all(|x| x.is_none())
    );
    let new_wal_tokens = get_wal_tokens(store, &[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1]).await;
    assert_eq!(new_wal_tokens.len(), 2);
    assert_ne!(orig_wal_tokens[0], new_wal_tokens[0]);
    assert_ne!(orig_wal_tokens[1], new_wal_tokens[1]);
}

async fn test_segment_metadata_ops(store: &dyn GlobalStore) {
    let seg1 = Uuid::new_v4();
    let seg2 = Uuid::new_v4();
    let seg3 = Uuid::new_v4();

    // Test segment metadatas.
    assert!(store
        .upsert_segment_metadatas(
            [
                (seg1, SegmentMetadataUpdate::default()),
                (
                    seg2,
                    SegmentMetadataUpdate {
                        add_num_rows: Some(10),
                        ..Default::default()
                    }
                ),
            ]
            .into_iter()
            .collect()
        )
        .await
        .unwrap());
    assert_eq!(
        store.query_segment_metadatas(&[seg1, seg2]).await.unwrap(),
        vec![
            SegmentMetadata::default(),
            SegmentMetadata {
                num_rows: 10,
                ..Default::default()
            },
        ]
    );
    assert!(store.query_segment_metadatas(&[seg1, seg3]).await.is_err());
    assert!(store
        .upsert_segment_metadatas(
            [(
                seg1,
                SegmentMetadataUpdate {
                    last_compacted_index_meta: Some((
                        None,
                        Some(LastCompactedIndexMeta {
                            xact_id: TransactionId(1),
                            ..Default::default()
                        })
                    )),
                    minimum_pagination_key: Some((PaginationKey(0), PaginationKey(10))),
                    add_num_rows: Some(20),
                }
            ),]
            .into_iter()
            .collect()
        )
        .await
        .unwrap());
    assert_eq!(
        store.query_segment_metadatas(&[seg1]).await.unwrap(),
        vec![SegmentMetadata {
            last_compacted_index_meta: Some(LastCompactedIndexMeta {
                xact_id: TransactionId(1),
                ..Default::default()
            }),
            minimum_pagination_key: PaginationKey(10),
            num_rows: 20,
        }]
    );
    assert!(!store
        .upsert_segment_metadatas(
            [(
                seg1,
                SegmentMetadataUpdate {
                    last_compacted_index_meta: Some((
                        Some(LastCompactedIndexMeta {
                            xact_id: TransactionId(1),
                            ..Default::default()
                        }),
                        Some(LastCompactedIndexMeta {
                            xact_id: TransactionId(2),
                            ..Default::default()
                        })
                    )),
                    minimum_pagination_key: Some((PaginationKey(0), PaginationKey(20))),
                    ..Default::default()
                }
            ),]
            .into_iter()
            .collect()
        )
        .await
        .unwrap());
    assert_eq!(
        store.query_segment_metadatas(&[seg1]).await.unwrap(),
        vec![SegmentMetadata {
            last_compacted_index_meta: Some(LastCompactedIndexMeta {
                xact_id: TransactionId(1),
                ..Default::default()
            }),
            minimum_pagination_key: PaginationKey(10),
            num_rows: 20,
        }]
    );
    assert!(store
        .upsert_segment_metadatas(HashMap::new())
        .await
        .unwrap());
    assert_eq!(store.query_segment_metadatas(&[]).await.unwrap(), vec![]);
}

async fn test_segment_row_info(store: &dyn GlobalStore) {
    let seg1 = Uuid::new_v4();
    let seg2 = Uuid::new_v4();
    let seg3 = Uuid::new_v4();

    let full_row_id0 = FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "row0");
    let full_row_id1 = FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "row1");
    let full_row_id2 = FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "row2");

    // Add row IDs
    store
        .add_id_segment_membership(
            IdSegmentMembershipType::RowId,
            [
                (seg1, vec![full_row_id0, full_row_id1]),
                (seg2, vec![full_row_id0]),
            ]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    // Add root span IDs
    store
        .add_id_segment_membership(
            IdSegmentMembershipType::RootSpanId,
            [
                (
                    seg1,
                    vec![
                        FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0"),
                        FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span1"),
                    ],
                ),
                (
                    seg2,
                    vec![FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0")],
                ),
            ]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();
    // Query row IDs
    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RowId,
                &[seg1, seg3],
                &[full_row_id0],
            )
            .await
            .unwrap(),
        [(full_row_id0.to_owned(), seg1)].into_iter().collect()
    );

    // Query root span IDs
    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RootSpanId,
                &[seg1, seg3],
                &[FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span1")]
            )
            .await
            .unwrap(),
        [(
            FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span1").to_owned(),
            seg1
        )]
        .into_iter()
        .collect()
    );
    // Okay to query across overlapping segments if the info we query does not overlap.
    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RowId,
                &[seg1, seg2],
                &[full_row_id1]
            )
            .await
            .unwrap(),
        [(full_row_id1.to_owned(), seg1)].into_iter().collect()
    );
    // Test with empty inputs
    store
        .add_id_segment_membership(IdSegmentMembershipType::RowId, HashMap::new())
        .await
        .unwrap();
    store
        .add_id_segment_membership(IdSegmentMembershipType::RootSpanId, HashMap::new())
        .await
        .unwrap();

    // Empty queries should return empty results
    assert_eq!(
        store
            .query_id_segment_membership(IdSegmentMembershipType::RowId, &[seg1, seg3], &[])
            .await
            .unwrap(),
        HashMap::new()
    );
    assert_eq!(
        store
            .query_id_segment_membership(IdSegmentMembershipType::RootSpanId, &[seg1, seg3], &[])
            .await
            .unwrap(),
        HashMap::new()
    );

    // Queries with no segments should return empty results
    assert_eq!(
        store
            .query_id_segment_membership(IdSegmentMembershipType::RowId, &[], &[full_row_id0])
            .await
            .unwrap(),
        HashMap::new()
    );
    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RootSpanId,
                &[],
                &[FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span1")]
            )
            .await
            .unwrap(),
        HashMap::new()
    );
    let seg_to_delete = Uuid::new_v4();

    // Add row IDs and root span IDs to the segment.
    store
        .add_id_segment_membership(
            IdSegmentMembershipType::RowId,
            [(seg_to_delete, vec![full_row_id0])].into_iter().collect(),
        )
        .await
        .unwrap();

    store
        .add_id_segment_membership(
            IdSegmentMembershipType::RootSpanId,
            [(
                seg_to_delete,
                vec![FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0")],
            )]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    // Verify row IDs were added
    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RowId,
                &[seg_to_delete],
                &[full_row_id0]
            )
            .await
            .unwrap(),
        [(full_row_id0.to_owned(), seg_to_delete)]
            .into_iter()
            .collect()
    );

    // Verify root span IDs were added
    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RootSpanId,
                &[seg_to_delete],
                &[
                    FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0"),
                    FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span1"),
                    FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span2")
                ]
            )
            .await
            .unwrap(),
        [(
            FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0").to_owned(),
            seg_to_delete
        )]
        .into_iter()
        .collect()
    );

    // Test copying row id info.
    let seg4 = Uuid::new_v4();
    let seg_copied = Uuid::new_v4();

    // Add row IDs and root span IDs to seg4
    store
        .add_id_segment_membership(
            IdSegmentMembershipType::RowId,
            [(seg4, vec![full_row_id2])].into_iter().collect(),
        )
        .await
        .unwrap();

    store
        .add_id_segment_membership(
            IdSegmentMembershipType::RootSpanId,
            [(
                seg4,
                vec![FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span2")],
            )]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    // Copy from seg1 and seg4 to seg_copied
    store
        .copy_id_segment_membership(&[seg1, seg4], seg_copied)
        .await
        .unwrap();

    // Verify row IDs were copied
    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RowId,
                &[seg_copied],
                &[full_row_id0, full_row_id2]
            )
            .await
            .unwrap(),
        [
            (full_row_id0.to_owned(), seg_copied),
            (full_row_id2.to_owned(), seg_copied),
        ]
        .into_iter()
        .collect()
    );

    // Verify root span IDs were copied
    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RootSpanId,
                &[seg_copied],
                &[
                    FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0"),
                    FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span1"),
                    FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span2")
                ]
            )
            .await
            .unwrap(),
        [
            (
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0").to_owned(),
                seg_copied
            ),
            (
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span1").to_owned(),
                seg_copied
            ),
            (
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span2").to_owned(),
                seg_copied
            ),
        ]
        .into_iter()
        .collect()
    );
    // Recopying is fine.
    store
        .copy_id_segment_membership(&[seg1, seg4], seg_copied)
        .await
        .unwrap();
    // Copying nothing is fine.
    store
        .copy_id_segment_membership(&[], seg_copied)
        .await
        .unwrap();

    // Verify data is still correct after recopying
    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RowId,
                &[seg_copied],
                &[full_row_id0, full_row_id2]
            )
            .await
            .unwrap(),
        [
            (full_row_id0.to_owned(), seg_copied),
            (full_row_id2.to_owned(), seg_copied),
        ]
        .into_iter()
        .collect()
    );

    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RootSpanId,
                &[seg_copied],
                &[
                    FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0"),
                    FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span1"),
                    FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span2")
                ]
            )
            .await
            .unwrap(),
        [
            (
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0").to_owned(),
                seg_copied
            ),
            (
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span1").to_owned(),
                seg_copied
            ),
            (
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span2").to_owned(),
                seg_copied
            ),
        ]
        .into_iter()
        .collect()
    );

    // Cannot query row id info across segments with overlapping info.
    assert!(store
        .query_id_segment_membership(
            IdSegmentMembershipType::RowId,
            &[seg1, seg2],
            &[full_row_id0]
        )
        .await
        .is_err());
    assert!(store
        .query_id_segment_membership(
            IdSegmentMembershipType::RootSpanId,
            &[seg1, seg2],
            &[FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0")]
        )
        .await
        .is_err());

    // Purge the ID segment membership for seg_to_delete
    store
        .purge_id_segment_membership(&[seg_to_delete])
        .await
        .unwrap();

    // Verify data was purged
    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RowId,
                &[seg_to_delete],
                &[full_row_id0]
            )
            .await
            .unwrap(),
        HashMap::new()
    );

    assert_eq!(
        store
            .query_id_segment_membership(
                IdSegmentMembershipType::RootSpanId,
                &[seg_to_delete],
                &[FullRowId::from_full_object_id(*FULL_OBJECT_ID0, "span0")]
            )
            .await
            .unwrap(),
        HashMap::new()
    );
}

async fn test_concurrent_update_segment_ids(store: &dyn GlobalStore) {
    // Test concurrent adding and removing segment ids.
    let concurrent_segments = (0..100).map(|_| Uuid::new_v4()).collect::<Vec<_>>();
    join_all((0..100).map(|i| {
        let concurrent_segments = &concurrent_segments;
        async move {
            store
                .update_segment_ids(&[(*FULL_OBJECT_ID_CONCURRENT, &[concurrent_segments[i]], &[])])
                .await
                .unwrap();
        }
    }))
    .await;
    let obj_concurrent_segments = sorted_segments2(
        store
            .list_segment_ids(&[*FULL_OBJECT_ID_CONCURRENT], None)
            .await
            .unwrap(),
    );
    assert_eq!(
        obj_concurrent_segments,
        vec![sorted_segments(concurrent_segments.clone())]
    );

    join_all((0..100).map(|i| {
        let concurrent_segments = &concurrent_segments;
        async move {
            store
                .update_segment_ids(&[(*FULL_OBJECT_ID_CONCURRENT, &[], &[concurrent_segments[i]])])
                .await
                .unwrap();
        }
    }))
    .await;
    let obj_concurrent_segments = store
        .list_segment_ids(&[*FULL_OBJECT_ID_CONCURRENT], None)
        .await
        .unwrap()
        .remove(0);
    assert!(obj_concurrent_segments.is_empty());
}

async fn test_enormous_row_id_root_span_id_queries(store: &dyn GlobalStore) {
    let seg_big = Uuid::new_v4();
    const NUM_ENTRIES: usize = 100000;
    let segment_id = seg_big;
    let object_id = ObjectId::new("obj_big").unwrap();
    let row_ids = (0..NUM_ENTRIES)
        .map(|i| format!("row{}", i))
        .collect::<Vec<_>>();
    let root_span_ids = (0..NUM_ENTRIES)
        .map(|i| format!("root_span{}", i))
        .collect::<Vec<_>>();
    let _root_span_id_refs = root_span_ids.iter().map(|s| s.as_str()).collect::<Vec<_>>();
    let full_row_ids = row_ids
        .iter()
        .map(|row_id| FullRowId {
            object_type: ObjectType::Experiment,
            object_id,
            id: row_id,
        })
        .collect::<Vec<_>>();

    // Add row IDs
    let row_add_input = [(segment_id, full_row_ids.clone())]
        .into_iter()
        .collect::<HashMap<_, _>>();

    store
        .add_id_segment_membership(IdSegmentMembershipType::RowId, row_add_input)
        .await
        .unwrap();

    // Add root span IDs
    let span_add_input: HashMap<Uuid, Vec<FullRowId<'_, '_>>> = [(
        segment_id,
        root_span_ids
            .iter()
            .map(|s| FullRowId::from_full_object_id(*FULL_OBJECT_ID0, s))
            .collect(),
    )]
    .into_iter()
    .collect();
    store
        .add_id_segment_membership(IdSegmentMembershipType::RootSpanId, span_add_input)
        .await
        .unwrap();

    // Query row IDs
    let row_res = store
        .query_id_segment_membership(IdSegmentMembershipType::RowId, &[segment_id], &full_row_ids)
        .await
        .unwrap();

    let expected_row_mapping = full_row_ids
        .iter()
        .map(|full_row_id| (full_row_id.to_owned(), segment_id))
        .collect::<HashMap<_, _>>();

    assert_eq!(row_res, expected_row_mapping);

    // Query root span IDs
    let root_span_id_full_row_ids: Vec<FullRowId<'_, '_>> = root_span_ids
        .iter()
        .map(|s| FullRowId::from_full_object_id(*FULL_OBJECT_ID0, s))
        .collect();
    let span_res = store
        .query_id_segment_membership(
            IdSegmentMembershipType::RootSpanId,
            &[segment_id],
            &root_span_id_full_row_ids,
        )
        .await
        .unwrap();

    let expected_span_mapping = root_span_ids
        .iter()
        .map(|root_span_id| {
            (
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, root_span_id).to_owned(),
                segment_id,
            )
        })
        .collect::<HashMap<_, _>>();

    assert_eq!(span_res, expected_span_mapping);

    // Clean up
    store
        .purge_id_segment_membership(&[segment_id])
        .await
        .unwrap();
}

async fn test_segment_wal_entries(store: &dyn GlobalStore) {
    let seg_wal = Uuid::new_v4();
    assert!(store
        .query_segment_wal_entries_batch(seg_wal, None, None, None)
        .await
        .unwrap()
        .is_empty());
    assert_eq!(
        store
            .query_segment_wal_entries_xact_id_statistic(
                &[seg_wal],
                SegmentWalEntriesXactIdStatistic::Min,
                None
            )
            .await
            .unwrap(),
        [None]
    );

    // Test basic upsert and query. Ensure the first transaction has a greater wal filename than
    // the second in order to validate that we do pagination correctly.
    let mut wal1 = Uuid::new_v4();
    let mut wal2 = Uuid::new_v4();
    if wal1 < wal2 {
        std::mem::swap(&mut wal1, &mut wal2);
    }
    assert_eq!(
        store
            .upsert_segment_wal_entries(
                [(
                    seg_wal,
                    [
                        (
                            wal1,
                            vec![UpsertSegmentWalEntry {
                                is_compacted: false,
                                xact_id: TransactionId(1),
                                byte_range_start: 0,
                                byte_range_end: 100,
                                digest: Some(1),
                            }],
                        ),
                        (
                            wal2,
                            vec![UpsertSegmentWalEntry {
                                is_compacted: false,
                                xact_id: TransactionId(2),
                                byte_range_start: 100,
                                byte_range_end: 200,
                                digest: Some(2),
                            }],
                        ),
                    ]
                    .into_iter()
                    .collect(),
                )]
                .into_iter()
                .collect(),
            )
            .await
            .unwrap(),
        2
    );

    // Cannot upsert multiple entries with the same xact_id in the same file.
    assert!(store
        .upsert_segment_wal_entries(
            [(
                seg_wal,
                [(
                    wal1,
                    vec![
                        UpsertSegmentWalEntry {
                            is_compacted: false,
                            xact_id: TransactionId(1),
                            byte_range_start: 0,
                            byte_range_end: 100,
                            digest: Some(1),
                        },
                        UpsertSegmentWalEntry {
                            is_compacted: false,
                            xact_id: TransactionId(1),
                            byte_range_start: 100,
                            byte_range_end: 200,
                            digest: Some(2),
                        },
                    ],
                ),]
                .into_iter()
                .collect(),
            )]
            .into_iter()
            .collect(),
        )
        .await
        .is_err());

    let entries = store
        .query_segment_wal_entries_batch(seg_wal, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 2);
    assert_eq!(
        entries[0],
        SegmentWalEntry {
            xact_id: TransactionId(1),
            wal_filename: wal1,
            byte_range_start: 0,
            byte_range_end: 100,
            is_compacted: false,
            digest: Some(1),
            deleted_at: None,
        }
    );
    assert_eq!(
        entries[1],
        SegmentWalEntry {
            xact_id: TransactionId(2),
            wal_filename: wal2,
            byte_range_start: 100,
            byte_range_end: 200,
            is_compacted: false,
            digest: Some(2),
            deleted_at: None,
        }
    );

    // Test upsert with existing entries - should only update is_compacted
    assert_eq!(
        store
            .upsert_segment_wal_entries(
                [(
                    seg_wal,
                    [(
                        wal1,
                        vec![UpsertSegmentWalEntry {
                            is_compacted: true,
                            xact_id: TransactionId(1),
                            byte_range_start: 50, // Should be ignored
                            byte_range_end: 150,  // Should be ignored
                            digest: Some(1),
                        }],
                    )]
                    .into_iter()
                    .collect(),
                )]
                .into_iter()
                .collect(),
            )
            .await
            .unwrap(),
        1
    );

    let entries = store
        .query_segment_wal_entries_batch(seg_wal, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 2);
    assert_eq!(
        entries[0],
        SegmentWalEntry {
            xact_id: TransactionId(1),
            wal_filename: wal1,
            byte_range_start: 0,
            byte_range_end: 100,
            is_compacted: true,
            digest: Some(1),
            deleted_at: None,
        }
    );
    assert_eq!(
        entries[1],
        SegmentWalEntry {
            xact_id: TransactionId(2),
            wal_filename: wal2,
            byte_range_start: 100,
            byte_range_end: 200,
            is_compacted: false,
            digest: Some(2),
            deleted_at: None,
        }
    );

    // Test cursor-based queries
    let entries = store
        .query_segment_wal_entries_batch(
            seg_wal,
            Some(SegmentWalEntriesCursor::XactIdGe(TransactionId(2))),
            None,
            None,
        )
        .await
        .unwrap();
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].xact_id, TransactionId(2));

    // Test limit and cursor.
    let entries = store
        .query_segment_wal_entries_batch(seg_wal, None, Some(1), None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].xact_id, TransactionId(1));
    let entries = store
        .query_segment_wal_entries_batch(
            seg_wal,
            compute_next_wal_entry_cursor(&entries),
            Some(1),
            None,
        )
        .await
        .unwrap();
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].xact_id, TransactionId(2));
    let entries = store
        .query_segment_wal_entries_batch(
            seg_wal,
            compute_next_wal_entry_cursor(&entries),
            Some(1),
            None,
        )
        .await
        .unwrap();
    assert!(entries.is_empty());
    assert_eq!(compute_next_wal_entry_cursor(&entries), None);

    // Test is_compacted filter
    let entries = store
        .query_segment_wal_entries_batch(seg_wal, None, None, Some(true))
        .await
        .unwrap();
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].xact_id, TransactionId(1));

    // Test statistics
    assert!(store
        .query_segment_wal_entries_xact_id_statistic(
            &[],
            SegmentWalEntriesXactIdStatistic::Min,
            None
        )
        .await
        .unwrap()
        .is_empty());
    assert_eq!(
        store
            .query_segment_wal_entries_xact_id_statistic(
                &[seg_wal],
                SegmentWalEntriesXactIdStatistic::Min,
                None
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(1))
    );

    assert_eq!(
        store
            .query_segment_wal_entries_xact_id_statistic(
                &[seg_wal],
                SegmentWalEntriesXactIdStatistic::Max,
                None
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(2))
    );

    // Test updating is_compacted flag
    let entries = store
        .query_segment_wal_entries_batch(seg_wal, None, None, None)
        .await
        .unwrap();
    let entries_to_update: Vec<_> = entries
        .iter()
        .map(|e| (seg_wal, e.xact_id, e.wal_filename))
        .collect();

    // Should fail if we try to update non-existent entries
    assert!(store
        .update_segment_wal_entries_is_compacted_non_atomic(
            &[(seg_wal, TransactionId(999), wal1)],
            true
        )
        .await
        .is_err());

    // First, set up segment metadata and verify initial earliest_uncompacted_xact_id
    store
        .update_segment_ids(&[(*FULL_OBJECT_ID0, &[seg_wal], &[])])
        .await
        .unwrap();
    store
        .upsert_segment_metadatas(
            [(
                seg_wal,
                SegmentMetadataUpdate {
                    minimum_pagination_key: Some((PaginationKey(0), PaginationKey(100))),
                    add_num_rows: Some(100),
                    ..Default::default()
                },
            )]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    // Should succeed updating all entries
    store
        .update_segment_wal_entries_is_compacted_non_atomic(&entries_to_update, true)
        .await
        .unwrap();
    let entries = store
        .query_segment_wal_entries_batch(seg_wal, None, None, None)
        .await
        .unwrap();
    assert!(entries.iter().all(|e| e.is_compacted));

    // Should succeed updating back to false. To exercise the partial-update
    // semantics, we add a large number of duplicate entries in front, then add
    // a fake entry at the end. The operation should fail, but the entries
    // should still be set to false.
    {
        let mut this_entries_to_update = vec![];
        for _ in 0..100000 {
            this_entries_to_update.extend(entries_to_update.iter().cloned());
        }
        this_entries_to_update.push((seg_wal, TransactionId(999), wal1));
        assert!(store
            .update_segment_wal_entries_is_compacted_non_atomic(&this_entries_to_update, false)
            .await
            .is_err());
    }
    let entries = store
        .query_segment_wal_entries_batch(seg_wal, None, None, None)
        .await
        .unwrap();
    assert!(entries.iter().all(|e| !e.is_compacted));

    // Test update_all_segment_wal_entries_is_compacted_non_atomic
    let seg_wal_all1 = Uuid::new_v4();
    let seg_wal_all2 = Uuid::new_v4();
    let wal_all = Uuid::new_v4();

    // Add some entries to both segments
    assert_eq!(
        store
            .upsert_segment_wal_entries(
                [
                    (
                        seg_wal_all1,
                        [(
                            wal_all,
                            vec![
                                UpsertSegmentWalEntry {
                                    is_compacted: false,
                                    xact_id: TransactionId(10),
                                    byte_range_start: 0,
                                    byte_range_end: 100,
                                    digest: Some(10),
                                },
                                UpsertSegmentWalEntry {
                                    is_compacted: false,
                                    xact_id: TransactionId(11),
                                    byte_range_start: 100,
                                    byte_range_end: 200,
                                    digest: Some(11),
                                },
                            ],
                        )]
                        .into_iter()
                        .collect(),
                    ),
                    (
                        seg_wal_all2,
                        [(
                            wal_all,
                            vec![UpsertSegmentWalEntry {
                                is_compacted: false,
                                xact_id: TransactionId(20),
                                byte_range_start: 0,
                                byte_range_end: 100,
                                digest: Some(20),
                            },],
                        )]
                        .into_iter()
                        .collect(),
                    ),
                ]
                .into_iter()
                .collect(),
            )
            .await
            .unwrap(),
        3
    );

    // Set up segment metadata for both segments
    store
        .update_segment_ids(&[(*FULL_OBJECT_ID0, &[seg_wal_all1, seg_wal_all2], &[])])
        .await
        .unwrap();
    store
        .upsert_segment_metadatas(
            [
                (
                    seg_wal_all1,
                    SegmentMetadataUpdate {
                        minimum_pagination_key: Some((PaginationKey(0), PaginationKey(100))),
                        add_num_rows: Some(100),
                        ..Default::default()
                    },
                ),
                (
                    seg_wal_all2,
                    SegmentMetadataUpdate {
                        minimum_pagination_key: Some((PaginationKey(0), PaginationKey(100))),
                        add_num_rows: Some(100),
                        ..Default::default()
                    },
                ),
            ]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    // Update all entries in both segments to compacted
    store
        .update_all_segment_wal_entries_is_compacted_non_atomic(&[seg_wal_all1, seg_wal_all2], true)
        .await
        .unwrap();

    // Verify all entries are now compacted
    let entries1 = store
        .query_segment_wal_entries_batch(seg_wal_all1, None, None, None)
        .await
        .unwrap();
    assert!(entries1.iter().all(|e| e.is_compacted));
    assert_eq!(entries1.len(), 2);

    let entries2 = store
        .query_segment_wal_entries_batch(seg_wal_all2, None, None, None)
        .await
        .unwrap();
    assert!(entries2.iter().all(|e| e.is_compacted));
    assert_eq!(entries2.len(), 1);

    // Update only one segment back to uncompacted
    store
        .update_all_segment_wal_entries_is_compacted_non_atomic(&[seg_wal_all1], false)
        .await
        .unwrap();

    // Verify only the specified segment was updated
    let entries1 = store
        .query_segment_wal_entries_batch(seg_wal_all1, None, None, None)
        .await
        .unwrap();
    assert!(entries1.iter().all(|e| !e.is_compacted));

    let entries2 = store
        .query_segment_wal_entries_batch(seg_wal_all2, None, None, None)
        .await
        .unwrap();
    assert!(entries2.iter().all(|e| e.is_compacted));

    // Test copying WAL entries from multiple segments
    let seg_wal_src1 = Uuid::new_v4();
    let seg_wal_src2 = Uuid::new_v4();
    assert_eq!(
        store
            .upsert_segment_wal_entries(
                [(
                    seg_wal_src1,
                    [(
                        wal1,
                        vec![UpsertSegmentWalEntry {
                            is_compacted: false,
                            xact_id: TransactionId(3),
                            byte_range_start: 0,
                            byte_range_end: 100,
                            digest: Some(3),
                        }],
                    )]
                    .into_iter()
                    .collect(),
                )]
                .into_iter()
                .collect(),
            )
            .await
            .unwrap(),
        1
    );

    let wal2 = Uuid::new_v4();
    assert_eq!(
        store
            .upsert_segment_wal_entries(
                [(
                    seg_wal_src2,
                    [(
                        wal2,
                        vec![UpsertSegmentWalEntry {
                            is_compacted: true,
                            xact_id: TransactionId(4),
                            byte_range_start: 100,
                            byte_range_end: 200,
                            digest: Some(4),
                        }],
                    )]
                    .into_iter()
                    .collect(),
                )]
                .into_iter()
                .collect(),
            )
            .await
            .unwrap(),
        1
    );

    // Copy from both source segments to destination
    let seg_wal_dst = Uuid::new_v4();
    store
        .copy_segment_wal_entries(&[seg_wal_src1, seg_wal_src2], seg_wal_dst)
        .await
        .unwrap();

    let entries = store
        .query_segment_wal_entries_batch(seg_wal_dst, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 2);
    assert_eq!(
        entries[0],
        SegmentWalEntry {
            xact_id: TransactionId(3),
            wal_filename: wal1,
            byte_range_start: 0,
            byte_range_end: 100,
            is_compacted: false,
            digest: Some(3),
            deleted_at: None,
        }
    );
    assert_eq!(
        entries[1],
        SegmentWalEntry {
            xact_id: TransactionId(4),
            wal_filename: wal2,
            byte_range_start: 100,
            byte_range_end: 200,
            is_compacted: true,
            digest: Some(4),
            deleted_at: None,
        }
    );

    // Test re-copying updates is_compacted flag
    assert_eq!(
        store
            .upsert_segment_wal_entries(
                [(
                    seg_wal_src1,
                    [(
                        wal1,
                        vec![UpsertSegmentWalEntry {
                            is_compacted: true, // Changed to true
                            xact_id: TransactionId(3),
                            byte_range_start: 0,
                            byte_range_end: 100,
                            digest: Some(3),
                        }],
                    )]
                    .into_iter()
                    .collect(),
                )]
                .into_iter()
                .collect(),
            )
            .await
            .unwrap(),
        1
    );

    store
        .copy_segment_wal_entries(&[seg_wal_src1, seg_wal_src2], seg_wal_dst)
        .await
        .unwrap();

    let entries = store
        .query_segment_wal_entries_batch(seg_wal_dst, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 2);
    assert_eq!(
        entries[0],
        SegmentWalEntry {
            xact_id: TransactionId(3),
            wal_filename: wal1,
            byte_range_start: 0,
            byte_range_end: 100,
            is_compacted: true, // Updated to true
            digest: Some(3),
            deleted_at: None,
        }
    );
    assert_eq!(
        entries[1],
        SegmentWalEntry {
            xact_id: TransactionId(4),
            wal_filename: wal2,
            byte_range_start: 100,
            byte_range_end: 200,
            is_compacted: true,
            digest: Some(4),
            deleted_at: None,
        }
    );

    // Test existence queries in batch
    let non_existent_seg = Uuid::new_v4();
    let queries = vec![
        // Basic existence checks
        (seg_wal, None),
        (seg_wal_dst, None),
        (non_existent_seg, None),
        // Cursor checks
        (
            seg_wal,
            Some(SegmentWalEntriesCursor::XactIdGe(TransactionId(2))),
        ),
        (
            seg_wal,
            Some(SegmentWalEntriesCursor::XactIdGe(TransactionId(3))),
        ),
        // XactIdWalFilenameGt cursor check
        (
            seg_wal,
            Some(SegmentWalEntriesCursor::XactIdWalFilenameGt {
                xact_id: TransactionId(1),
                wal_filename: wal1,
            }),
        ),
        // Duplicate segment checks to verify consistent results
        (seg_wal_dst, None),
        (non_existent_seg, None),
    ];

    // Test without compaction filter
    let results = store
        .query_segment_wal_entries_existence(&queries, None)
        .await
        .unwrap();
    assert_eq!(
        results,
        vec![
            true,  // seg_wal exists
            true,  // seg_wal_dst exists
            false, // non_existent_seg doesn't exist
            true,  // seg_wal has entries >= txn 2
            false, // seg_wal has no entries >= txn 3
            true,  // seg_wal has entries with (xact_id, wal_filename) > (1, wal1)
            true,  // seg_wal_dst exists (duplicate check)
            false, // non_existent_seg doesn't exist (duplicate check)
        ]
    );

    // Test with is_compacted=true filter
    let results = store
        .query_segment_wal_entries_existence(&queries, Some(true))
        .await
        .unwrap();
    assert_eq!(
        results,
        vec![
            false, // seg_wal has no compacted entries
            true,  // seg_wal_dst has compacted entries
            false, // non_existent_seg doesn't exist
            false, // seg_wal has no compacted entries
            false, // seg_wal has no entries >= txn 3
            false, // seg_wal has no compacted entries with (xact_id, wal_filename) > (1, wal1)
            true,  // seg_wal_dst has compacted entries (duplicate)
            false, // non_existent_seg doesn't exist (duplicate)
        ]
    );

    // Test with is_compacted=false filter
    let results = store
        .query_segment_wal_entries_existence(&queries, Some(false))
        .await
        .unwrap();
    assert_eq!(
        results,
        vec![
            true,  // seg_wal has uncompacted entries
            false, // seg_wal_dst has no uncompacted entries
            false, // non_existent_seg doesn't exist
            true,  // seg_wal has uncompacted entries >= txn 2
            false, // seg_wal has no entries >= txn 3
            true,  // seg_wal has uncompacted entries with (xact_id, wal_filename) > (1, wal1)
            false, // seg_wal_dst has no uncompacted entries (duplicate)
            false, // non_existent_seg doesn't exist (duplicate)
        ]
    );

    // Test digest-based deduplication
    let seg_wal_digest = Uuid::new_v4();
    let wal_digest1 = Uuid::new_v4();
    let wal_digest2 = Uuid::new_v4();

    // First insert with a specific digest
    assert_eq!(
        store
            .upsert_segment_wal_entries(
                [(
                    seg_wal_digest,
                    [(
                        wal_digest1,
                        vec![UpsertSegmentWalEntry {
                            is_compacted: false,
                            xact_id: TransactionId(5),
                            byte_range_start: 0,
                            byte_range_end: 100,
                            digest: Some(1001),
                        }],
                    )]
                    .into_iter()
                    .collect(),
                )]
                .into_iter()
                .collect(),
            )
            .await
            .unwrap(),
        1
    );

    // Try to insert another entry with the same digest but different WAL file
    // This should be deduplicated and not inserted
    assert_eq!(
        store
            .upsert_segment_wal_entries(
                [(
                    seg_wal_digest,
                    [(
                        wal_digest2,
                        vec![UpsertSegmentWalEntry {
                            is_compacted: false,
                            xact_id: TransactionId(5), // Same transaction ID
                            byte_range_start: 200,     // Different range
                            byte_range_end: 300,       // Different range
                            digest: Some(1001),        // Same digest
                        }],
                    )]
                    .into_iter()
                    .collect(),
                )]
                .into_iter()
                .collect(),
            )
            .await
            .unwrap(),
        0
    ); // Should be 0 because it was deduplicated

    // Verify only one entry exists
    let entries = store
        .query_segment_wal_entries_batch(seg_wal_digest, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 1);
    assert_eq!(
        entries[0],
        SegmentWalEntry {
            xact_id: TransactionId(5),
            wal_filename: wal_digest1,
            byte_range_start: 0,
            byte_range_end: 100,
            is_compacted: false,
            digest: Some(1001),
            deleted_at: None,
        }
    );

    // Insert a different entry with a different digest but same transaction ID
    assert_eq!(
        store
            .upsert_segment_wal_entries(
                [(
                    seg_wal_digest,
                    [(
                        wal_digest2,
                        vec![UpsertSegmentWalEntry {
                            is_compacted: false,
                            xact_id: TransactionId(5), // Same transaction ID
                            byte_range_start: 200,
                            byte_range_end: 300,
                            digest: Some(1002), // Different digest
                        }],
                    )]
                    .into_iter()
                    .collect(),
                )]
                .into_iter()
                .collect(),
            )
            .await
            .unwrap(),
        1
    ); // Should be 1 because it's a new entry

    // Verify both entries exist now
    let entries = store
        .query_segment_wal_entries_batch(seg_wal_digest, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 2);

    // Test copying with digests
    let seg_wal_copy_dest = Uuid::new_v4();
    store
        .copy_segment_wal_entries(&[seg_wal_digest], seg_wal_copy_dest)
        .await
        .unwrap();

    // Verify copied entries have the same digests
    let entries = store
        .query_segment_wal_entries_batch(seg_wal_copy_dest, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 2);

    // Sort entries by xact_id and byte_range_start for consistent comparison
    let mut sorted_entries = entries.clone();
    sorted_entries.sort_by_key(|e| (e.xact_id, e.byte_range_start));

    assert_eq!(
        sorted_entries[0],
        SegmentWalEntry {
            xact_id: TransactionId(5),
            wal_filename: wal_digest1,
            byte_range_start: 0,
            byte_range_end: 100,
            is_compacted: false,
            digest: Some(1001),
            deleted_at: None,
        }
    );

    assert_eq!(
        sorted_entries[1],
        SegmentWalEntry {
            xact_id: TransactionId(5),
            wal_filename: wal_digest2,
            byte_range_start: 200,
            byte_range_end: 300,
            is_compacted: false,
            digest: Some(1002),
            deleted_at: None,
        }
    );

    store
        .purge_segment_wal_entries(&[seg_wal_src1, seg_wal_src2])
        .await
        .unwrap();

    // Verify entries are gone
    let entries = store
        .query_segment_wal_entries_batch(seg_wal_src1, None, None, None)
        .await
        .unwrap();
    assert!(entries.is_empty());
    let entries = store
        .query_segment_wal_entries_batch(seg_wal_src2, None, None, None)
        .await
        .unwrap();
    assert!(entries.is_empty());

    // Verify statistics are empty
    assert_eq!(
        store
            .query_segment_wal_entries_xact_id_statistic(
                &[seg_wal_src1, seg_wal_src2],
                SegmentWalEntriesXactIdStatistic::Min,
                None
            )
            .await
            .unwrap(),
        [None, None]
    );

    // But our original seg_wal and seg_dst are still there until we purge them.
    assert_eq!(
        store
            .query_segment_wal_entries_batch(seg_wal, None, None, None)
            .await
            .unwrap()
            .len(),
        2
    );
    assert_eq!(
        store
            .query_segment_wal_entries_batch(seg_wal_dst, None, None, None)
            .await
            .unwrap()
            .len(),
        2
    );
    assert_eq!(
        store
            .query_segment_wal_entries_xact_id_statistic(
                &[seg_wal, seg_wal_dst],
                SegmentWalEntriesXactIdStatistic::Min,
                None
            )
            .await
            .unwrap(),
        [Some(TransactionId(1)), Some(TransactionId(3)),]
    );

    store
        .purge_segment_wal_entries(&[seg_wal, seg_wal_dst, seg_wal_src1])
        .await
        .unwrap();
    assert_eq!(
        store
            .query_segment_wal_entries_xact_id_statistic(
                &[seg_wal, seg_wal_dst],
                SegmentWalEntriesXactIdStatistic::Min,
                None
            )
            .await
            .unwrap(),
        [None, None]
    );
}

async fn test_last_index_operations(store: &dyn GlobalStore) {
    let seg1 = Uuid::new_v4();

    // Add test for last index operations after testing segment metadatas
    assert_eq!(
        store.query_last_index_operations(&[seg1]).await.unwrap()[0],
        None
    );

    // Test upserting a new operation
    let op_token = Uuid::new_v4();
    let operation = LastIndexOperation {
        finished: Some(false),
        estimated_progress: Some(0.5),
        stage: Some("processing".to_string()),
        error: None,
        details: Some(LastIndexOperationDetails::Compact {
            num_wal_entries: 42,
        }),
    };

    store
        .upsert_last_index_operation(seg1, operation.clone(), op_token, Default::default())
        .await
        .unwrap();

    // Verify the operation was stored
    let result = store
        .query_last_index_operations(&[seg1])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(result.operation, operation);
    assert!(result.start <= result.last_updated);

    // Test updating an existing operation
    let updated_operation = LastIndexOperation {
        finished: Some(true),
        estimated_progress: Some(1.0),
        stage: Some("completed".to_string()),
        error: None,
        details: Some(LastIndexOperationDetails::Merge {
            merges: vec![vec![seg1.to_string(), seg1.to_string()]],
        }),
    };
    store
        .upsert_last_index_operation(
            seg1,
            updated_operation.clone(),
            op_token,
            Default::default(),
        )
        .await
        .unwrap();

    // Verify the update preserved the start time but updated last_updated
    let updated_result = store
        .query_last_index_operations(&[seg1])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(updated_result.operation, updated_operation);
    assert_eq!(updated_result.start, result.start);
    assert!(updated_result.last_updated >= result.last_updated);

    // Test operation with error
    let error_operation = LastIndexOperation {
        finished: Some(true),
        estimated_progress: None,
        stage: None,
        error: Some("Failed to process".to_string()),
        details: None,
    };
    store
        .upsert_last_index_operation(seg1, error_operation.clone(), op_token, Default::default())
        .await
        .unwrap();

    // Verify error was stored
    let error_result = store
        .query_last_index_operations(&[seg1])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(error_result.operation, error_operation);

    // Test bump_last_index_operation_updated_ts
    let initial_result = store
        .query_last_index_operations(&[seg1])
        .await
        .unwrap()
        .remove(0)
        .unwrap();

    // Sleep to ensure timestamps can be different
    std::thread::sleep(std::time::Duration::from_millis(50));

    // Wrong op token should not update timestamp
    store
        .bump_last_index_operation_updated_ts(seg1, Uuid::new_v4())
        .await
        .unwrap();
    let after_wrong_token = store
        .query_last_index_operations(&[seg1])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(initial_result.last_updated, after_wrong_token.last_updated);

    // Correct op token should update timestamp
    store
        .bump_last_index_operation_updated_ts(seg1, op_token)
        .await
        .unwrap();
    let after_correct_token = store
        .query_last_index_operations(&[seg1])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert!(after_correct_token.last_updated > initial_result.last_updated);
    assert_eq!(initial_result.start, after_correct_token.start); // Start time should not change

    // Update is rejected if we use the wrong op token.
    let op_token2 = Uuid::new_v4();
    store
        .upsert_last_index_operation(seg1, operation.clone(), op_token2, Default::default())
        .await
        .unwrap();
    let result = store
        .query_last_index_operations(&[seg1])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(result.operation, error_operation);

    // But it's fine if we upsert in override mode. Also will update the start time.
    let prev_start_time = result.start;
    std::thread::sleep(std::time::Duration::from_millis(50));
    store
        .upsert_last_index_operation(
            seg1,
            operation.clone(),
            op_token2,
            LastIndexOpTokenOpts {
                always_update: true,
                ..Default::default()
            },
        )
        .await
        .unwrap();
    let result = store
        .query_last_index_operations(&[seg1])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(result.operation, operation);
    assert!((result.start - prev_start_time).num_milliseconds() > 40);
    assert!((result.start - result.last_updated).num_milliseconds() < 5);

    // Test out clearing the op token.
    store
        .upsert_last_index_operation(
            seg1,
            operation.clone(),
            op_token2,
            LastIndexOpTokenOpts {
                clear_value: true,
                ..Default::default()
            },
        )
        .await
        .unwrap();
    let result = store
        .query_last_index_operations(&[seg1])
        .await
        .unwrap()
        .remove(0)
        .unwrap();
    assert_eq!(result.current_op_token, None);

    // Test querying non-existent segment
    let seg_nonexistent = Uuid::new_v4();
    assert_eq!(
        store
            .query_last_index_operations(&[seg_nonexistent])
            .await
            .unwrap()[0],
        None
    );
}

async fn test_query_recently_updated_objects_and_list_object_ids(store: &dyn GlobalStore) {
    // add a few objects that have last processed xact ids

    let full_object_id2 = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj2").unwrap(),
    };
    let full_object_id3 = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj3").unwrap(),
    };
    let full_object_id4 = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj4").unwrap(),
    };
    let full_object_id5 = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj5").unwrap(),
    };
    store
        .upsert_object_metadatas(HashMap::from([
            (
                full_object_id2,
                ObjectMetadataUpdate {
                    last_processed_xact_id: Some((None, Some(TransactionId(12000)))),
                },
            ),
            (
                full_object_id3,
                ObjectMetadataUpdate {
                    last_processed_xact_id: Some((None, Some(TransactionId(12000)))),
                },
            ),
            (
                full_object_id4,
                ObjectMetadataUpdate {
                    last_processed_xact_id: Some((None, Some(TransactionId(3000)))),
                },
            ),
            // This object should never show up
            (
                full_object_id5,
                ObjectMetadataUpdate {
                    last_processed_xact_id: Some((None, None)),
                },
            ),
        ]))
        .await
        .unwrap();

    let results = store
        .query_recently_updated_objects(None, 2, None)
        .await
        .unwrap();
    assert_eq!(results.len(), 2);
    assert!(results[0].object_id.as_ref() == full_object_id3);
    assert!(results[1].object_id.as_ref() == full_object_id2);

    let cursor = results.iter().map(|x| x.cursor()).min().unwrap();

    let results = store
        .query_recently_updated_objects(Some(cursor), 2, None)
        .await
        .unwrap();
    assert_eq!(results.len(), 1);
    assert!(results[0].object_id.as_ref() == full_object_id4);

    let cursor = results[0].cursor();
    let results = store
        .query_recently_updated_objects(Some(cursor), 2, None)
        .await
        .unwrap();
    assert_eq!(results.len(), 0);

    // Begin: test list_object_ids

    let all_object_ids = [
        full_object_id2,
        full_object_id3,
        full_object_id4,
        full_object_id5,
    ];

    let filter_to_all_object_ids = |x: Vec<FullObjectIdOwned>| -> Vec<FullObjectIdOwned> {
        x.into_iter()
            .filter(|x| all_object_ids.contains(&x.as_ref()))
            .collect()
    };

    // Objects should be returned in order.
    let result = filter_to_all_object_ids(store.list_object_ids(None, 10, None).await.unwrap());
    assert_eq!(result.len(), all_object_ids.len());
    for (i, expected_id) in all_object_ids.iter().enumerate() {
        assert_eq!(result[i].as_ref(), *expected_id);
    }

    // The limit and cursor should be respected.
    let cursor = Some(full_object_id2);
    let result = filter_to_all_object_ids(store.list_object_ids(cursor, 2, None).await.unwrap());
    assert_eq!(result.len(), 2);
    assert!(result[0].as_ref() == full_object_id3);
    assert!(result[1].as_ref() == full_object_id4);

    let result = store.list_object_ids(cursor, 0, None).await.unwrap();
    assert!(result.is_empty());

    // Test with a cursor equal to the highest object ID.
    let highest_object = all_object_ids.last().unwrap();
    let result = filter_to_all_object_ids(
        store
            .list_object_ids(Some(*highest_object), 10, None)
            .await
            .unwrap(),
    );
    assert!(result.is_empty());

    // Test with a cursor between two object IDs.
    let object_id_in_between = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("obj3x").unwrap(),
    };
    let result = filter_to_all_object_ids(
        store
            .list_object_ids(Some(object_id_in_between), 10, None)
            .await
            .unwrap(),
    );
    assert_eq!(result.len(), 2);
    assert!(result[0].as_ref() == full_object_id4);
    assert!(result[1].as_ref() == full_object_id5);

    // Test that `object_ids_filter` is applied.
    let result = filter_to_all_object_ids(
        store
            .list_object_ids(None, 10, Some(&[full_object_id2, full_object_id4]))
            .await
            .unwrap(),
    );
    assert_eq!(result.len(), 2);
    assert!(result[0].as_ref() == full_object_id2);
    assert!(result[1].as_ref() == full_object_id4);

    // Test query_recently_updated_objects with min_lag_compaction_seconds
    {
        // Set up test data: create segments with different compaction lags
        let test_object_1 = FullObjectId {
            object_type: ObjectType::Experiment,
            object_id: ObjectId::new("lag_test_1").unwrap(),
        };
        let test_object_2 = FullObjectId {
            object_type: ObjectType::Experiment,
            object_id: ObjectId::new("lag_test_2").unwrap(),
        };

        let seg_id_1 = Uuid::new_v4();
        let seg_id_2 = Uuid::new_v4();

        // Add segments for the test objects
        store
            .update_segment_ids(&[
                (test_object_1, &[seg_id_1], &[]),
                (test_object_2, &[seg_id_2], &[]),
            ])
            .await
            .unwrap();

        // Set up object metadata with recent processing times
        let current_time = Utc::now().timestamp() as u64;
        let xact_id_current = TransactionId::from_timestamp(current_time, 0);
        let xact_id_5min_ago = TransactionId::from_timestamp(current_time - 300, 0);
        let xact_id_10min_ago = TransactionId::from_timestamp(current_time - 600, 0);

        store
            .upsert_object_metadatas(HashMap::from([
                (
                    test_object_1,
                    ObjectMetadataUpdate {
                        last_processed_xact_id: Some((None, Some(xact_id_current))),
                    },
                ),
                (
                    test_object_2,
                    ObjectMetadataUpdate {
                        last_processed_xact_id: Some((None, Some(xact_id_current))),
                    },
                ),
            ]))
            .await
            .unwrap();

        // Set up segment metadata with different compaction times
        store
            .upsert_segment_metadatas(HashMap::from([
                (
                    seg_id_1,
                    SegmentMetadataUpdate {
                        last_compacted_index_meta: Some((
                            None,
                            Some(LastCompactedIndexMeta {
                                xact_id: xact_id_5min_ago, // 5 minutes lag
                                tantivy_meta: Default::default(),
                            }),
                        )),
                        ..Default::default()
                    },
                ),
                (
                    seg_id_2,
                    SegmentMetadataUpdate {
                        last_compacted_index_meta: Some((
                            None,
                            Some(LastCompactedIndexMeta {
                                xact_id: xact_id_10min_ago, // 10 minutes lag
                                tantivy_meta: Default::default(),
                            }),
                        )),
                        ..Default::default()
                    },
                ),
            ]))
            .await
            .unwrap();

        // Test without min_lag filter - should return both objects
        let results = store
            .query_recently_updated_objects(None, 10, None)
            .await
            .unwrap();
        assert!(results
            .iter()
            .any(|r| r.object_id.as_ref() == test_object_1));
        assert!(results
            .iter()
            .any(|r| r.object_id.as_ref() == test_object_2));

        // Test with 6 minute min_lag filter - should only return object 2 (10 min lag)
        let results = store
            .query_recently_updated_objects(None, 10, Some(360))
            .await
            .unwrap();
        assert!(!results
            .iter()
            .any(|r| r.object_id.as_ref() == test_object_1));
        assert!(results
            .iter()
            .any(|r| r.object_id.as_ref() == test_object_2));

        // Test with 15 minute min_lag filter - should return neither
        let results = store
            .query_recently_updated_objects(None, 10, Some(900))
            .await
            .unwrap();
        assert!(!results
            .iter()
            .any(|r| r.object_id.as_ref() == test_object_1));
        assert!(!results
            .iter()
            .any(|r| r.object_id.as_ref() == test_object_2));
    }
}

async fn test_field_statistics(store: &dyn GlobalStore) {
    // Test field statistics
    let seg_stats = Uuid::new_v4();

    // Initially no statistics exist
    let results = store
        .query_field_statistics(&[seg_stats], &["field1"])
        .await
        .unwrap();
    assert!(results.is_empty());

    // Test upserting statistics
    store
        .upsert_field_statistics(vec![
            (
                seg_stats,
                "field1",
                SegmentFieldStatistics::new(1, 10).unwrap(),
            ),
            (
                seg_stats,
                "field2",
                SegmentFieldStatistics::new(5, 15).unwrap(),
            ),
        ])
        .await
        .unwrap();

    // Query multiple fields at once
    let results = store
        .query_field_statistics(&[seg_stats], &["field1", "field2"])
        .await
        .unwrap();

    assert_eq!(results.len(), 1);
    let field_stats = results.get(&seg_stats).unwrap();
    assert_eq!(field_stats.len(), 2);
    assert_eq!(
        field_stats.get("field1").unwrap(),
        &SegmentFieldStatistics::new(1, 10).unwrap()
    );
    assert_eq!(
        field_stats.get("field2").unwrap(),
        &SegmentFieldStatistics::new(5, 15).unwrap()
    );

    // Test validation - min must be <= max
    assert!(SegmentFieldStatistics::new(10, 1).is_err());

    // Test querying multiple segments for the same field
    let seg_stats2 = Uuid::new_v4();
    store
        .upsert_field_statistics(vec![
            (
                seg_stats,
                "field3",
                SegmentFieldStatistics::new(0, 100).unwrap(),
            ),
            (
                seg_stats2,
                "field3",
                SegmentFieldStatistics::new(50, 150).unwrap(),
            ),
        ])
        .await
        .unwrap();

    let results = store
        .query_field_statistics(&[seg_stats, seg_stats2], &["field3"])
        .await
        .unwrap();

    assert_eq!(results.len(), 2);
    assert_eq!(
        results.get(&seg_stats).unwrap().get("field3").unwrap(),
        &SegmentFieldStatistics::new(0, 100).unwrap()
    );
    assert_eq!(
        results.get(&seg_stats2).unwrap().get("field3").unwrap(),
        &SegmentFieldStatistics::new(50, 150).unwrap()
    );

    // Test atomic updates with upsert_segment_metadatas_and_field_statistics
    let seg_atomic = Uuid::new_v4();

    // Initial state - set up some metadata and statistics
    store
        .upsert_segment_metadatas(
            [(
                seg_atomic,
                SegmentMetadataUpdate {
                    last_compacted_index_meta: Some((
                        None,
                        Some(LastCompactedIndexMeta {
                            xact_id: TransactionId(1),
                            ..Default::default()
                        }),
                    )),
                    ..Default::default()
                },
            )]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    store
        .upsert_field_statistics(vec![(
            seg_atomic,
            "field_atomic",
            SegmentFieldStatistics::new(1, 10).unwrap(),
        )])
        .await
        .unwrap();

    // Try an update that should fail the CAS check on metadata
    let failed_update = store
        .upsert_segment_metadatas_and_field_statistics(
            [(
                seg_atomic,
                (
                    SegmentMetadataUpdate {
                        last_compacted_index_meta: Some((
                            Some(LastCompactedIndexMeta {
                                xact_id: TransactionId(999), // Wrong previous value
                                ..Default::default()
                            }),
                            Some(LastCompactedIndexMeta {
                                xact_id: TransactionId(2),
                                ..Default::default()
                            }),
                        )),
                        ..Default::default()
                    },
                    vec![("field_atomic", SegmentFieldStatistics::new(5, 15).unwrap())],
                ),
            )]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    assert!(!failed_update); // Update should fail

    // Verify neither metadata nor statistics were updated
    let metadata = store.query_segment_metadatas(&[seg_atomic]).await.unwrap()[0].clone();
    assert_eq!(
        metadata.last_compacted_index_meta.unwrap().xact_id,
        TransactionId(1)
    );

    let stats = store
        .query_field_statistics(&[seg_atomic], &["field_atomic"])
        .await
        .unwrap()
        .get(&seg_atomic)
        .unwrap()
        .get("field_atomic")
        .unwrap()
        .clone();
    assert_eq!(stats, SegmentFieldStatistics::new(1, 10).unwrap());

    // Try a successful update
    let successful_update = store
        .upsert_segment_metadatas_and_field_statistics(
            [(
                seg_atomic,
                (
                    SegmentMetadataUpdate {
                        last_compacted_index_meta: Some((
                            Some(LastCompactedIndexMeta {
                                xact_id: TransactionId(1), // Correct previous value
                                ..Default::default()
                            }),
                            Some(LastCompactedIndexMeta {
                                xact_id: TransactionId(2),
                                ..Default::default()
                            }),
                        )),
                        ..Default::default()
                    },
                    vec![("field_atomic", SegmentFieldStatistics::new(5, 15).unwrap())],
                ),
            )]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    assert!(successful_update); // Update should succeed

    // Verify both metadata and statistics were updated
    let metadata = store.query_segment_metadatas(&[seg_atomic]).await.unwrap()[0].clone();
    assert_eq!(
        metadata.last_compacted_index_meta.unwrap().xact_id,
        TransactionId(2)
    );

    let stats = store
        .query_field_statistics(&[seg_atomic], &["field_atomic"])
        .await
        .unwrap()
        .get(&seg_atomic)
        .unwrap()
        .get("field_atomic")
        .unwrap()
        .clone();
    assert_eq!(stats, SegmentFieldStatistics::new(5, 15).unwrap());
}

async fn test_wal_entries_existence_enormous(store: &dyn GlobalStore) {
    // This is calibrated to be larger than the 1644 limit for number of projected columns in a
    // postgres query.
    const NUM_ENTRIES: usize = 2000;
    let segment_ids = [Uuid::new_v4(); NUM_ENTRIES];
    let segment_id_to_wal_filename_to_entries: HashMap<
        Uuid,
        HashMap<Uuid, Vec<UpsertSegmentWalEntry>>,
    > = segment_ids
        .iter()
        .map(|segment_id| {
            (
                *segment_id,
                [(Uuid::new_v4(), vec![UpsertSegmentWalEntry::default()])]
                    .into_iter()
                    .collect(),
            )
        })
        .collect();

    store
        .upsert_segment_wal_entries(segment_id_to_wal_filename_to_entries)
        .await
        .unwrap();

    let existences = store
        .query_segment_wal_entries_existence(
            &segment_ids
                .map(|s| (s, None))
                .into_iter()
                .collect::<Vec<_>>(),
            None,
        )
        .await
        .unwrap();
    assert_eq!(existences.len(), NUM_ENTRIES);
    assert!(existences.iter().all(|e| *e));

    store.purge_segment_wal_entries(&segment_ids).await.unwrap();
}

async fn test_retention(store: Arc<dyn GlobalStore>) {
    let segment_id = Uuid::new_v4();
    store
        .update_segment_ids(&[(*FULL_OBJECT_ID_RETENTION, &[segment_id], &[])])
        .await
        .unwrap();

    store
        .upsert_segment_wal_entries(
            [(
                segment_id,
                [(
                    Uuid::new_v4(),
                    vec![
                        UpsertSegmentWalEntry {
                            xact_id: TransactionId(1),
                            byte_range_start: 0,
                            byte_range_end: 100,
                            is_compacted: false,
                            digest: Some(1),
                        },
                        UpsertSegmentWalEntry {
                            xact_id: TransactionId(2),
                            byte_range_start: 100,
                            byte_range_end: 200,
                            is_compacted: false,
                            digest: Some(2),
                        },
                        UpsertSegmentWalEntry {
                            xact_id: TransactionId(3),
                            byte_range_start: 200,
                            byte_range_end: 300,
                            is_compacted: false,
                            digest: Some(3),
                        },
                    ],
                )]
                .into_iter()
                .collect(),
            )]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    // Verify that the inserted WAL entries all have the same WAL filename,
    // and store it for later.
    let entries = store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 3);
    let first_wal_filenames_set = entries
        .iter()
        .map(|entry| entry.wal_filename)
        .collect::<HashSet<_>>();
    assert_eq!(first_wal_filenames_set.len(), 1);
    let first_wal_filename = first_wal_filenames_set.iter().next().unwrap();

    // Purge up to xact_id 2.
    store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(2))
        .await
        .unwrap();

    // Verify that the entry with xact_id = 1 was deleted.
    let entries = store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 2);
    assert_eq!(entries[0].xact_id, TransactionId(2));
    assert_eq!(entries[1].xact_id, TransactionId(3));

    // Purge up to xact_id 3.
    store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(3))
        .await
        .unwrap();

    // Verify that the entry with xact_id = 2 was deleted.
    let entries = store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].xact_id, TransactionId(3));

    // Verify that `query_purged_wal_filenames` returns an empty result, since
    // this WAL file still contains one live entry.
    let wal_filenames = store
        .query_purged_wal_filenames(segment_id, &[*first_wal_filename], None)
        .await
        .unwrap();
    assert!(wal_filenames.is_empty());

    // Add another batch of WAL entries.
    store
        .upsert_segment_wal_entries(
            [(
                segment_id,
                [(
                    Uuid::new_v4(),
                    vec![
                        UpsertSegmentWalEntry {
                            xact_id: TransactionId(4),
                            byte_range_start: 300,
                            byte_range_end: 400,
                            is_compacted: false,
                            digest: Some(4),
                        },
                        UpsertSegmentWalEntry {
                            xact_id: TransactionId(5),
                            byte_range_start: 400,
                            byte_range_end: 500,
                            is_compacted: false,
                            digest: Some(5),
                        },
                    ],
                )]
                .into_iter()
                .collect(),
            )]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    // Verify we now have 3 entries (one compacted, two uncompacted).
    let entries = store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 3);
    assert_eq!(entries[0].xact_id, TransactionId(3));
    assert_eq!(entries[1].xact_id, TransactionId(4));
    assert_eq!(entries[2].xact_id, TransactionId(5));

    // Verify that the new WAL entries all have the same WAL filename,
    // and store it for later.
    let wal_filenames_set = entries
        .iter()
        .map(|entry| entry.wal_filename)
        .collect::<HashSet<_>>();
    assert_eq!(wal_filenames_set.len(), 2);
    assert!(wal_filenames_set.contains(first_wal_filename));
    let second_wal_filename = wal_filenames_set
        .iter()
        .find(|&filename| *filename != *first_wal_filename)
        .unwrap();

    // Verify that deleting a nonexistent segment has no effect.
    let non_existent_segment = Uuid::new_v4();
    store
        .delete_segment_wal_entries_up_to_xact_id(&[non_existent_segment], TransactionId(100))
        .await
        .unwrap();

    let entries = store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(
        entries.len(),
        3,
        "Deleting a nonexistent segment should not affect existing entries"
    );
    assert_eq!(entries[0].xact_id, TransactionId(3));
    assert_eq!(entries[1].xact_id, TransactionId(4));
    assert_eq!(entries[2].xact_id, TransactionId(5));

    // Test concurrent delete operations.
    let mut futures = Vec::new();
    for i in 3..6 {
        let store = store.clone();
        futures.push(tokio::spawn(async move {
            store
                .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(i))
                .await
                .unwrap();
        }));
    }
    futures::future::join_all(futures).await;

    // Verify that only the last entry is still live.
    let entries = store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].xact_id, TransactionId(5));

    // Verify that `query_purged_wal_filenames` returns nothing, since the first WAL
    // entry was soft deleted, not fully purged.
    let wal_filenames = store
        .query_purged_wal_filenames(
            segment_id,
            &[*first_wal_filename, *second_wal_filename],
            None,
        )
        .await
        .unwrap();
    assert!(wal_filenames.is_empty());

    // But if we run the same query with `treat_files_as_purged_if_soft_deleted_seconds_ago`,
    // we should get it.
    let wal_filenames = store
        .query_purged_wal_filenames(
            segment_id,
            &[*first_wal_filename, *second_wal_filename],
            Some(0),
        )
        .await
        .unwrap();
    assert_eq!(wal_filenames.len(), 1);
    assert_eq!(wal_filenames[0], *first_wal_filename);

    // Purge the segment with an expiration_seconds of 60. Since the first WAL entry
    // was deleted moments ago, it should not get purged.
    let num_counted = store
        .count_deleted_segment_wal_entries(&[segment_id], 60)
        .await
        .unwrap();
    let num_purged = store
        .purge_deleted_segment_wal_entries(&[segment_id], 60)
        .await
        .unwrap();
    assert_eq!(num_counted, 0);
    assert_eq!(num_purged, 0);

    // Now fully purge the segment. Three entries from the first WAL file should be
    // purged, alongside one from the second WAL file.
    let num_counted = store
        .count_deleted_segment_wal_entries(&[segment_id], 0)
        .await
        .unwrap();
    let num_purged = store
        .purge_deleted_segment_wal_entries(&[segment_id], 0)
        .await
        .unwrap();
    assert_eq!(num_counted, 4);
    assert_eq!(num_purged, 4);

    let wal_filenames = store
        .query_purged_wal_filenames(
            segment_id,
            &[*first_wal_filename, *second_wal_filename],
            None,
        )
        .await
        .unwrap();
    assert_eq!(wal_filenames.len(), 1);
    assert_eq!(wal_filenames[0], *first_wal_filename);

    // Now purge through the end of the WAL and verify that no entries remain.
    store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(6))
        .await
        .unwrap();
    let entries = store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await
        .unwrap();
    assert!(entries.is_empty());

    // Verify that `query_purged_wal_filenames` still only returns the first file.
    let wal_filenames = store
        .query_purged_wal_filenames(
            segment_id,
            &[*first_wal_filename, *second_wal_filename],
            None,
        )
        .await
        .unwrap();
    assert_eq!(wal_filenames.len(), 1);
    assert_eq!(wal_filenames[0], *first_wal_filename);

    // The same query with `treat_soft_deleted_files_as_purged` should return both.
    let wal_filenames = store
        .query_purged_wal_filenames(
            segment_id,
            &[*first_wal_filename, *second_wal_filename],
            Some(0),
        )
        .await
        .unwrap();
    assert_eq!(wal_filenames.len(), 2);
    assert!(wal_filenames.contains(first_wal_filename));
    assert!(wal_filenames.contains(second_wal_filename));

    // Now purge the segment's deleted WAL entries. Only one entry from the second
    // WAL file should be purged.
    let num_counted = store
        .count_deleted_segment_wal_entries(&[segment_id], 0)
        .await
        .unwrap();
    let num_purged = store
        .purge_deleted_segment_wal_entries(&[segment_id], 0)
        .await
        .unwrap();
    assert_eq!(num_counted, 1);
    assert_eq!(num_purged, 1);

    // Verify that `query_purged_wal_filenames` now returns both WAL files,
    // since all contained entries have been purged.
    let wal_filenames = store
        .query_purged_wal_filenames(
            segment_id,
            &[*first_wal_filename, *second_wal_filename],
            None,
        )
        .await
        .unwrap();
    assert_eq!(wal_filenames.len(), 2);
    assert!(wal_filenames.contains(first_wal_filename));
    assert!(wal_filenames.contains(second_wal_filename));

    store
        .update_segment_ids(&[(*FULL_OBJECT_ID_RETENTION, &[], &[segment_id])])
        .await
        .unwrap();
    store.purge_segment_metadatas(&[segment_id]).await.unwrap();
    store
        .purge_object_metadatas(&[*FULL_OBJECT_ID_RETENTION])
        .await
        .unwrap();
}

async fn test_retention_restoration(store: &dyn GlobalStore) {
    let restore_segment_id = Uuid::new_v4();
    store
        .update_segment_ids(&[(*FULL_OBJECT_ID_RETENTION, &[restore_segment_id], &[])])
        .await
        .unwrap();

    // Add WAL entries for restoration testing
    store
        .upsert_segment_wal_entries(
            [(
                restore_segment_id,
                [(
                    Uuid::new_v4(),
                    vec![
                        UpsertSegmentWalEntry {
                            xact_id: TransactionId(1),
                            byte_range_start: 0,
                            byte_range_end: 100,
                            is_compacted: false,
                            digest: Some(1),
                        },
                        UpsertSegmentWalEntry {
                            xact_id: TransactionId(2),
                            byte_range_start: 100,
                            byte_range_end: 200,
                            is_compacted: false,
                            digest: Some(2),
                        },
                        UpsertSegmentWalEntry {
                            xact_id: TransactionId(3),
                            byte_range_start: 200,
                            byte_range_end: 300,
                            is_compacted: false,
                            digest: Some(3),
                        },
                    ],
                )]
                .into_iter()
                .collect(),
            )]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    // Soft delete entries up to xact_id 2
    store
        .delete_segment_wal_entries_up_to_xact_id(&[restore_segment_id], TransactionId(2))
        .await
        .unwrap();

    // Verify only entry with xact_id 2 and 3 remain visible
    let entries = store
        .query_segment_wal_entries_batch(restore_segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 2);
    assert_eq!(entries[0].xact_id, TransactionId(2));
    assert_eq!(entries[1].xact_id, TransactionId(3));

    // Test restoring entry with xact_id 1
    let num_restored = store
        .restore_segment_wal_entries_up_to_xact_id(&[restore_segment_id], TransactionId(2))
        .await
        .unwrap();
    assert_eq!(num_restored, 1);

    // Verify entry 1 is now visible again
    let entries = store
        .query_segment_wal_entries_batch(restore_segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 3);
    assert_eq!(entries[0].xact_id, TransactionId(1));

    // Test restoring with threshold that matches no deleted entries
    let num_restored = store
        .restore_segment_wal_entries_up_to_xact_id(&[restore_segment_id], TransactionId(5))
        .await
        .unwrap();
    assert_eq!(num_restored, 0);

    // Test restoring from non-existent segment
    let non_existent_segment = Uuid::new_v4();
    let num_restored = store
        .restore_segment_wal_entries_up_to_xact_id(&[non_existent_segment], TransactionId(10))
        .await
        .unwrap();
    assert_eq!(num_restored, 0);

    // Test restoring with empty segment list
    let num_restored = store
        .restore_segment_wal_entries_up_to_xact_id(&[], TransactionId(10))
        .await
        .unwrap();
    assert_eq!(num_restored, 0);

    // Test interaction with purge_deleted_segment_wal_entries
    // Delete all entries again to set up the test
    store
        .delete_segment_wal_entries_up_to_xact_id(&[restore_segment_id], TransactionId(4))
        .await
        .unwrap();

    // Restore only entry 1
    let num_restored = store
        .restore_segment_wal_entries_up_to_xact_id(&[restore_segment_id], TransactionId(2))
        .await
        .unwrap();
    assert_eq!(num_restored, 1);

    // Verify entry 1 is visible again
    let entries = store
        .query_segment_wal_entries_batch(restore_segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].xact_id, TransactionId(1));

    // Purge deleted entries - should only purge entries 2 and 3 (not the restored entry 1)
    let num_counted = store
        .count_deleted_segment_wal_entries(&[restore_segment_id], 0)
        .await
        .unwrap();
    let num_purged = store
        .purge_deleted_segment_wal_entries(&[restore_segment_id], 0)
        .await
        .unwrap();
    assert_eq!(num_counted, 2);
    assert_eq!(num_purged, 2); // Only entries 2 and 3 should be purged

    // Verify entry 1 is still visible (was not purged because it was restored)
    let entries = store
        .query_segment_wal_entries_batch(restore_segment_id, None, None, None)
        .await
        .unwrap();
    assert_eq!(entries.len(), 1);
    assert_eq!(entries[0].xact_id, TransactionId(1));

    // Clean up
    store
        .update_segment_ids(&[(*FULL_OBJECT_ID_RETENTION, &[], &[restore_segment_id])])
        .await
        .unwrap();
    store
        .purge_segment_ids(&[restore_segment_id])
        .await
        .unwrap();
    store
        .purge_object_metadatas(&[*FULL_OBJECT_ID_RETENTION])
        .await
        .unwrap();
}

async fn test_retention_worker(store: &dyn GlobalStore) {
    // Create test data for retention worker tests
    let first_obj = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("retention_worker_test").unwrap(),
    };

    // Create an ordered vec of segment IDs
    let mut retention_segments = vec![
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
    ];
    retention_segments.sort();

    // Create a second object with its own segment
    let second_obj = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("second_retention_test").unwrap(),
    };
    let second_obj_segment = Uuid::new_v4();

    // Add segments to both test objects
    store
        .update_segment_ids(&[
            (first_obj, &retention_segments, &[]),
            (second_obj, &[second_obj_segment], &[]),
        ])
        .await
        .unwrap();

    // Test list_object_segment_ids_batch
    // Test with no limit
    let result = store
        .list_object_segment_ids(first_obj, Uuid::nil(), 0)
        .await
        .unwrap();
    assert!(result.is_empty(), "Limit of 0 should return empty result");

    // Test with full list (all segments)
    let result = store
        .list_object_segment_ids(first_obj, Uuid::nil(), 10)
        .await
        .unwrap();
    assert_eq!(result.len(), 5, "Should return all 5 segments");
    assert_eq!(
        result, retention_segments,
        "Segments should be returned in order"
    );

    // Test pagination with specific segment verification
    let result1 = store
        .list_object_segment_ids(first_obj, Uuid::nil(), 2)
        .await
        .unwrap();
    assert_eq!(result1.len(), 2, "Should return first 2 segments");
    assert_eq!(
        result1,
        &retention_segments[0..2],
        "First batch should have first 2 segments"
    );

    let result2 = store
        .list_object_segment_ids(first_obj, result1[1], 2)
        .await
        .unwrap();
    assert_eq!(result2.len(), 2, "Should return next 2 segments");
    assert_eq!(
        result2,
        &retention_segments[2..4],
        "Second batch should have segments 3-4"
    );

    let result3 = store
        .list_object_segment_ids(first_obj, result2[1], 2)
        .await
        .unwrap();
    assert_eq!(result3.len(), 1, "Should return the last segment");
    assert_eq!(
        result3,
        &retention_segments[4..5],
        "Third batch should have segment 5"
    );

    // List segments for the second object
    let second_obj_segments = store
        .list_object_segment_ids(second_obj, Uuid::nil(), 10)
        .await
        .unwrap();
    assert_eq!(
        second_obj_segments.len(),
        1,
        "Should return one segment for second object"
    );
    assert_eq!(
        second_obj_segments[0], second_obj_segment,
        "Should return the correct segment for second object"
    );
    assert!(
        !retention_segments.contains(&second_obj_segments[0]),
        "Second object segment should not be in first object segments"
    );

    // Test list_object_ids_segment_ids
    let result = store
        .list_object_ids_segment_ids(&[first_obj, second_obj], 0)
        .await
        .unwrap();
    assert!(result.is_empty(), "Limit of 0 should return empty result");

    // Test with empty object_ids array
    let result = store.list_object_ids_segment_ids(&[], 10).await.unwrap();
    assert!(
        result.is_empty(),
        "Empty object_ids should return empty result"
    );

    // Test with single object (first object)
    let result = store
        .list_object_ids_segment_ids(&[first_obj], 10)
        .await
        .unwrap();
    assert_eq!(
        result.len(),
        5,
        "Should return all 5 segments for first object"
    );
    for (i, (obj_id, segment_id)) in result.iter().enumerate() {
        assert_eq!(
            *obj_id,
            first_obj.to_owned(),
            "Object ID should match first object"
        );
        assert_eq!(
            *segment_id, retention_segments[i],
            "Segment ID should match expected order"
        );
    }

    // Test with single object (second object)
    let result = store
        .list_object_ids_segment_ids(&[second_obj], 10)
        .await
        .unwrap();
    assert_eq!(result.len(), 1, "Should return 1 segment for second object");
    assert_eq!(
        result[0].0,
        second_obj.to_owned(),
        "Object ID should match second object"
    );
    assert_eq!(
        result[0].1, second_obj_segment,
        "Segment ID should match second object segment"
    );

    // Test with both objects - should return results ordered by object_id, segment_id
    let result = store
        .list_object_ids_segment_ids(&[first_obj, second_obj], 20)
        .await
        .unwrap();
    assert_eq!(result.len(), 6, "Should return all 6 segments total");

    // Verify ordering: all first_obj segments come first (alphabetically), then second_obj segments
    for i in 0..5 {
        assert_eq!(
            result[i].0,
            first_obj.to_owned(),
            "First 5 results should be from first object"
        );
        assert_eq!(
            result[i].1, retention_segments[i],
            "Segment IDs should be in order"
        );
    }
    assert_eq!(
        result[5].0,
        second_obj.to_owned(),
        "Last result should be from second object"
    );
    assert_eq!(
        result[5].1, second_obj_segment,
        "Last segment should match second object segment"
    );

    // Test with both objects in reverse order - should still return same ordered results
    let result = store
        .list_object_ids_segment_ids(&[second_obj, first_obj], 20)
        .await
        .unwrap();
    assert_eq!(
        result.len(),
        6,
        "Should return all 6 segments regardless of input order"
    );

    // Results should still be ordered by object_id, segment_id (not input order)
    for i in 0..5 {
        assert_eq!(
            result[i].0,
            first_obj.to_owned(),
            "First 5 results should be from first object"
        );
        assert_eq!(
            result[i].1, retention_segments[i],
            "Segment IDs should be in order"
        );
    }
    assert_eq!(
        result[5].0,
        second_obj.to_owned(),
        "Last result should be from second object"
    );
    assert_eq!(
        result[5].1, second_obj_segment,
        "Last segment should match second object segment"
    );

    // Test pagination with both objects
    let result1 = store
        .list_object_ids_segment_ids(&[first_obj, second_obj], 3)
        .await
        .unwrap();
    assert_eq!(result1.len(), 3, "Should return first 3 results");
    for i in 0..3 {
        assert_eq!(
            result1[i].0,
            first_obj.to_owned(),
            "First 3 results should be from first object"
        );
        assert_eq!(
            result1[i].1, retention_segments[i],
            "Segment IDs should match first object segments"
        );
    }

    let result2 = store
        .list_object_ids_segment_ids(&[first_obj, second_obj], 2)
        .await
        .unwrap();
    assert_eq!(result2.len(), 2, "Should return first 2 results");
    for i in 0..2 {
        assert_eq!(
            result2[i].0,
            first_obj.to_owned(),
            "First 2 results should be from first object"
        );
        assert_eq!(
            result2[i].1, retention_segments[i],
            "Segment IDs should match first object segments"
        );
    }

    // Test with limit that spans across objects
    let result = store
        .list_object_ids_segment_ids(&[first_obj, second_obj], 5)
        .await
        .unwrap();
    assert_eq!(result.len(), 5, "Should return exactly 5 results");
    for i in 0..5 {
        assert_eq!(
            result[i].0,
            first_obj.to_owned(),
            "All 5 results should be from first object"
        );
        assert_eq!(
            result[i].1, retention_segments[i],
            "Segment IDs should match first object segments"
        );
    }

    // Test with limit of 6 to get all segments from both objects
    let result = store
        .list_object_ids_segment_ids(&[first_obj, second_obj], 6)
        .await
        .unwrap();
    assert_eq!(result.len(), 6, "Should return all 6 segments");
    for i in 0..5 {
        assert_eq!(
            result[i].0,
            first_obj.to_owned(),
            "First 5 should be from first object"
        );
        assert_eq!(
            result[i].1, retention_segments[i],
            "Segment IDs should match"
        );
    }
    assert_eq!(
        result[5].0,
        second_obj.to_owned(),
        "6th should be from second object"
    );
    assert_eq!(
        result[5].1, second_obj_segment,
        "6th segment should match second object"
    );

    // Clean up both objects and all segments
    store
        .update_segment_ids(&[
            (first_obj, &[], &retention_segments),
            (second_obj, &[], &[second_obj_segment]),
        ])
        .await
        .unwrap();
    store
        .purge_segment_metadatas(&[&retention_segments[..], &[second_obj_segment]].concat())
        .await
        .unwrap();
    store
        .purge_object_metadatas(&[first_obj, second_obj])
        .await
        .unwrap();

    // Test time-based retention state. Initially, the query should return the default state.
    let initial_state = store.query_time_based_retention_state().await.unwrap();
    assert_eq!(
        initial_state,
        TimeBasedRetentionState::default(),
        "Initial state should match default"
    );

    // Test upserting state
    let base_ts = Utc.with_ymd_and_hms(2024, 1, 2, 3, 4, 5).unwrap() + Duration::microseconds(6);
    let test_state = TimeBasedRetentionState {
        last_successful_start_ts: Some(base_ts - Duration::days(1)),
        current_op_start_ts: Some(base_ts),
        cursor: Some(TimeBasedRetentionCursor {
            object_id: first_obj.to_owned(),
            segment_id: retention_segments[2],
        }),
        operation: TimeBasedRetentionOperation {
            error: Some("test error".to_string()),
            completed_ts: Some(base_ts + Duration::hours(1)),
            stats: TimeBasedRetentionStats {
                num_processed_objects: 1,
                num_processed_segments: 0,
                segment_stats: DeleteFromSegmentStats {
                    wal_stats: DeleteFromSegmentWalStats {
                        planned_num_deleted_wal_entries: 2,
                        num_deleted_wal_entries: 0,
                    },
                    index_stats: DeleteFromSegmentIndexStats {
                        planned_num_deleted_index_docs: 4,
                        num_deleted_index_docs: 0,
                        num_write_locks: 1,
                    },
                },
            },
        },
    };

    // Test upserting state
    store
        .upsert_time_based_retention_state(&test_state)
        .await
        .unwrap();
    let updated_state = store.query_time_based_retention_state().await.unwrap();
    assert_eq!(updated_state, test_state, "States should match exactly");

    // Clean up the state
    store
        .upsert_time_based_retention_state(&TimeBasedRetentionState::default())
        .await
        .unwrap();
    let reset_state = store.query_time_based_retention_state().await.unwrap();
    assert_eq!(
        reset_state,
        TimeBasedRetentionState::default(),
        "State should be reset to default"
    );

    // Test upserting task infos
    let retention_task_info = TimeBasedRetentionInfo {
        start_ts: base_ts,
        completed_ts: Some(base_ts + Duration::hours(1)),
        retention_days: 30,
        min_retained_xact_id: TransactionId(99),
        error: Some("test error".to_string()),
        batch_stats: Some(DeleteFromSegmentStats {
            wal_stats: DeleteFromSegmentWalStats {
                planned_num_deleted_wal_entries: 2,
                num_deleted_wal_entries: 0,
            },
            index_stats: DeleteFromSegmentIndexStats {
                planned_num_deleted_index_docs: 4,
                num_deleted_index_docs: 0,
                num_write_locks: 1,
            },
        }),
    };
    store
        .upsert_segment_task_info(
            &[retention_segments[0]],
            &TaskInfo::TimeBasedRetention(retention_task_info.clone()),
        )
        .await
        .unwrap();
    let queried_task_infos = store
        .query_segment_task_infos(&retention_segments)
        .await
        .unwrap();
    let queried_retention_infos = queried_task_infos
        .iter()
        .map(|info| info.time_based_retention.as_ref())
        .collect::<Vec<_>>();
    assert_eq!(queried_retention_infos.len(), retention_segments.len());
    for (i, &queried_retention_info) in queried_retention_infos.iter().enumerate() {
        if i == 0 {
            assert_eq!(
                queried_retention_info,
                Some(&retention_task_info),
                "The first segment's retention info should match the upserted value"
            );
        } else {
            assert!(
                queried_retention_info.is_none(),
                "All other segments' retention infos should be None"
            );
        }
    }
}

async fn test_vacuum(store: &dyn GlobalStore) {
    for vacuum_type in [VacuumType::VacuumIndex, VacuumType::VacuumSegmentWal] {
        test_vacuum_type(store, vacuum_type).await.unwrap();
    }
}

async fn test_vacuum_type(
    store: &dyn GlobalStore,
    vacuum_type: VacuumType,
) -> Result<(), Box<dyn std::error::Error>> {
    // Count how many live segments exist before we start the vacuum test
    let pre_vacuum_live_segments = store
        .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
            pagination_args: None,
            is_live: true,
        }))
        .await
        .unwrap()
        .len();

    let segments = vec![
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
        Uuid::new_v4(),
    ];
    let obj_uuid = Uuid::new_v4().to_string();
    let test_obj = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new(&obj_uuid).unwrap(),
    };

    store
        .update_segment_ids(&[(test_obj, &segments, &[])])
        .await
        .unwrap();

    let segment_metadatas = segments
        .iter()
        .map(|segment_id| (*segment_id, SegmentMetadataUpdate::default()))
        .collect();
    assert!(store
        .upsert_segment_metadatas(segment_metadatas)
        .await
        .unwrap());

    // Use 1 minute as the filter interval.
    let max_last_successful_start_ts_minus_last_written_seconds = 60;
    let vacuum_period = 2 * 60;

    // Initially, all segments should be eligible for vacuum since last_successful_start_ts is null.
    // This should work both when specifying object_ids and globally (object_ids = None).
    let test_obj_slice = &[test_obj];
    let object_ids_opts: &[Option<&[FullObjectId<'_>]>] = &[Some(test_obj_slice), None];

    for &object_ids_opt in object_ids_opts {
        let eligible_segments = store
            .query_vacuum_segment_ids(
                vacuum_type,
                10,
                max_last_successful_start_ts_minus_last_written_seconds,
                vacuum_period,
                Utc::now(),
                object_ids_opt,
            )
            .await
            .unwrap();

        // When querying for a specific object, we should get exactly 5 segments
        // When querying globally (None), we might get additional segments from previous tests
        if object_ids_opt.is_some() {
            assert_eq!(eligible_segments.len(), 5);
        } else {
            assert_eq!(eligible_segments.len(), 5 + pre_vacuum_live_segments);
        }
    }

    // If we query for a nonexistent object, we should get no vacuumable segments back.
    let fake_obj_uuid = Uuid::new_v4().to_string();
    let fake_obj = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new(&fake_obj_uuid).unwrap(),
    };
    let eligible_segments = store
        .query_vacuum_segment_ids(
            vacuum_type,
            10,
            max_last_successful_start_ts_minus_last_written_seconds,
            vacuum_period,
            Utc::now(),
            Some(&[fake_obj]),
        )
        .await
        .unwrap();
    assert!(eligible_segments.is_empty());

    // The query should respect the input batch size.
    for &object_ids_opt in object_ids_opts {
        let eligible_segments = store
            .query_vacuum_segment_ids(
                vacuum_type,
                2,
                max_last_successful_start_ts_minus_last_written_seconds,
                vacuum_period,
                Utc::now(),
                object_ids_opt,
            )
            .await
            .unwrap();
        assert_eq!(eligible_segments.len(), 2);
    }

    // Set `last_written_ts` for the first 3 segments to 5 minutes ago.
    // All segments should still be eligible because vacuum_index_last_successful_start is null.
    let now = Utc::now();
    store
        .upsert_segment_last_written_ts(&segments[0..3], Some(now - Duration::minutes(5)))
        .await
        .unwrap();
    let eligible_segments = store
        .query_vacuum_segment_ids(
            vacuum_type,
            10,
            max_last_successful_start_ts_minus_last_written_seconds,
            vacuum_period,
            Utc::now(),
            Some(&[test_obj]),
        )
        .await
        .unwrap();
    assert_eq!(eligible_segments.len(), 5);

    // For segment 0, set the last_successful_start_ts to a little over 4 minutes ago.
    let before_cutoff_start_ts = now - Duration::minutes(4) - Duration::milliseconds(1);
    store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segments[0]],
            vacuum_type,
            before_cutoff_start_ts,
        )
        .await
        .unwrap();

    let after_cutoff_start_ts = now - Duration::minutes(4) + Duration::milliseconds(1);
    store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segments[1]],
            vacuum_type,
            after_cutoff_start_ts,
        )
        .await
        .unwrap();

    // Now segment 0 should be eligible for vacuum (more than filter_interval_seconds between
    // last write and last vacuum), but segment 1 should not.
    for &object_ids_opt in object_ids_opts {
        let eligible_segments = store
            .query_vacuum_segment_ids(
                vacuum_type,
                10,
                max_last_successful_start_ts_minus_last_written_seconds,
                vacuum_period,
                Utc::now(),
                object_ids_opt,
            )
            .await
            .unwrap();

        // When querying for a specific object, we should get exactly 4 segments
        // When querying globally (None), we might get additional segments from previous tests
        if object_ids_opt.is_some() {
            assert_eq!(eligible_segments.len(), 4);
        } else {
            assert_eq!(eligible_segments.len(), 4 + pre_vacuum_live_segments);
        }
        assert!(eligible_segments.contains(&segments[0]));
        assert!(!eligible_segments.contains(&segments[1]));

        // Check result order only when querying for specific object
        // When querying globally, the order includes segments from other tests
        let never_vacuumed_segments: HashSet<_> = segments[2..5].iter().cloned().collect();
        if object_ids_opt.is_some() {
            // Check result order. The first 3 segments should be the ones that have never been vacuumed
            // (segments 2, 3, 4). Segment 1 was last vacuumed enough time after last_written_ts that it
            // does not need to be vacuumed again.
            let first_three_eligible_segments: HashSet<_> =
                eligible_segments[0..3].iter().cloned().collect();
            // Check if the first three elements are exactly segments 2, 3, and 4.
            assert_eq!(first_three_eligible_segments, never_vacuumed_segments);
            // The 4th element should be segment 0, not segment 1.
            assert_eq!(eligible_segments[3], segments[0]);
        }

        // With a max_last_successful_start_ts_minus_last_written_seconds of 2 minutes, all segments
        // should be eligible for vacuum.
        let eligible_segments = store
            .query_vacuum_segment_ids(
                vacuum_type,
                10,
                2 * 60,
                vacuum_period,
                Utc::now(),
                object_ids_opt,
            )
            .await
            .unwrap();

        if object_ids_opt.is_some() {
            assert_eq!(eligible_segments.len(), 5);
            let first_three_eligible_segments: HashSet<_> =
                eligible_segments[0..3].iter().cloned().collect();
            assert_eq!(first_three_eligible_segments, never_vacuumed_segments);
            // Segment 0 should come before segment 1 because it was last vacuumed longer ago.
            assert_eq!(eligible_segments[3], segments[0]);
            assert_eq!(eligible_segments[4], segments[1]);
        } else {
            assert_eq!(eligible_segments.len(), 5 + pre_vacuum_live_segments);
        }
    }

    // If we set the vacuum period to 4 minutes, then segment 1 should not be eligible
    // because it was last vacuumed within the past 4 miunutes.
    let eligible_segments = store
        .query_vacuum_segment_ids(
            vacuum_type,
            10,
            max_last_successful_start_ts_minus_last_written_seconds,
            4 * 60,
            Utc::now(),
            Some(&[test_obj]),
        )
        .await
        .unwrap();
    assert_eq!(eligible_segments.len(), 4);
    assert!(!eligible_segments.contains(&segments[1]));

    // Check result order. The first 3 segments should be the ones that have never been vacuumed
    // (segments 2, 3, 4). As before, segment 1 was last vacuumed too recently, so it should not be
    // eligible.
    let never_vacuumed_segments: HashSet<_> = segments[2..5].iter().cloned().collect();
    let first_three_eligible_segments: HashSet<_> =
        eligible_segments[0..3].iter().cloned().collect();

    // Check if the first three elements are exactly segments 2, 3, and 4.
    assert_eq!(first_three_eligible_segments, never_vacuumed_segments);
    // The 4th element should be segment 0, not segment 1.
    assert_eq!(eligible_segments[3], segments[0]);

    // Update last_written_ts for all segments to be a second from now.
    // Now all segments should be eligible for vacuum.
    let next_second = now + Duration::seconds(1);
    store
        .upsert_segment_last_written_ts(&segments, Some(next_second))
        .await
        .unwrap();

    let eligible_segments = store
        .query_vacuum_segment_ids(
            vacuum_type,
            10,
            max_last_successful_start_ts_minus_last_written_seconds,
            vacuum_period,
            Utc::now(),
            Some(&[test_obj]),
        )
        .await
        .unwrap();
    assert_eq!(eligible_segments.len(), 5);
    let first_three_eligible_segments: HashSet<_> =
        eligible_segments[0..3].iter().cloned().collect();
    assert_eq!(first_three_eligible_segments, never_vacuumed_segments);
    // Segment 0 should come before segment 1 because it was last vacuumed longer ago.
    assert_eq!(eligible_segments[3], segments[0]);
    assert_eq!(eligible_segments[4], segments[1]);

    // Now mark segments 0 and 4 non-live. They should not be eligible for index vacuum.
    store
        .update_segment_ids(&[(test_obj, &[], &[segments[0], segments[4]])])
        .await
        .unwrap();
    for &object_ids_opt in object_ids_opts {
        let eligible_segments = store
            .query_vacuum_segment_ids(
                vacuum_type,
                10,
                max_last_successful_start_ts_minus_last_written_seconds,
                0,
                Utc::now(),
                object_ids_opt,
            )
            .await
            .unwrap();

        // When querying for a specific object, we should get exactly 2 segments
        // When querying globally (None), we might get additional segments from previous tests
        if object_ids_opt.is_some() {
            assert_eq!(eligible_segments.len(), 3);
        } else {
            assert_eq!(eligible_segments.len(), 3 + pre_vacuum_live_segments);
        }
        assert!(!eligible_segments.contains(&segments[0]));
        assert!(!eligible_segments.contains(&segments[4]));
    }

    // Mark remaining segments non-live and clean up all objects and segments.
    store
        .update_segment_ids(&[(test_obj, &[], &segments[1..4])])
        .await
        .unwrap();
    store.purge_segment_metadatas(&segments).await.unwrap();
    store.purge_object_metadatas(&[test_obj]).await.unwrap();

    Ok(())
}

async fn test_backfill_functionality(store: &dyn GlobalStore) {
    // Create test data
    let tracking_entries = vec![
        BackfillTrackingEntry {
            project_id: "project1".to_string(),
            object_type: ObjectType::Experiment,
            last_processed_sequence_id: 0,
            last_encountered_sequence_id: 300,
            last_processed_sequence_id_2: 0,
            last_encountered_sequence_id_2: 300,
            completed_initial_backfill_ts: Some(Utc::now()),
        },
        BackfillTrackingEntry {
            project_id: "project2".to_string(),
            object_type: ObjectType::Dataset,
            last_processed_sequence_id: 0,
            last_encountered_sequence_id: 500,
            last_processed_sequence_id_2: 0,
            last_encountered_sequence_id_2: 500,
            completed_initial_backfill_ts: Some(Utc::now()),
        },
        BackfillTrackingEntry {
            project_id: "project_unbackfilled".to_string(),
            object_type: ObjectType::Dataset,
            last_processed_sequence_id: 0,
            last_encountered_sequence_id: 500,
            last_processed_sequence_id_2: 0,
            last_encountered_sequence_id_2: 500,
            completed_initial_backfill_ts: None,
        },
    ];

    let brainstore_objects = vec![
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "project1".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 100,
            xact_id: TransactionId(1),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "project1".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 200,
            xact_id: TransactionId(2),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "project1".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj2".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 300,
            xact_id: TransactionId(3),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "project1".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj2".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 400,
            xact_id: TransactionId(4),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "project2".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Dataset,
                object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 500,
            xact_id: TransactionId(5),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "project2".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Dataset,
                object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 600,
            xact_id: TransactionId(6),
        },
        // Add some logs2 objects for testing
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "project1".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj3".to_string()).unwrap(),
            },
            is_logs2: true,
            sequence_id: 150,
            xact_id: TransactionId(7),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "project1".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj3".to_string()).unwrap(),
            },
            is_logs2: true,
            sequence_id: 250,
            xact_id: TransactionId(8),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "project2".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Dataset,
                object_id: ObjectIdOwned::new("obj2".to_string()).unwrap(),
            },
            is_logs2: true,
            sequence_id: 400,
            xact_id: TransactionId(9),
        },
    ];

    // Insert test data
    store
        .testing_only_insert_backfill_data(tracking_entries.clone(), brainstore_objects.clone())
        .await
        .unwrap();

    // Test querying tracking entries
    let queried_entries = store
        .query_unbackfilled_tracking_entries_ordered(true, None, 10)
        .await
        .unwrap();
    assert_eq!(queried_entries.len(), 2);
    assert_eq!(queried_entries[0].project_id, "project1");
    assert_eq!(queried_entries[0].object_type, ObjectType::Experiment);
    assert_eq!(queried_entries[1].project_id, "project2");
    assert_eq!(queried_entries[1].object_type, ObjectType::Dataset);

    // Also test querying un-backfilled tracking entries.
    {
        let unbackfilled_entries = store
            .query_unbackfilled_tracking_entries_ordered(false, None, 10)
            .await
            .unwrap();
        assert_eq!(unbackfilled_entries.len(), 1);
        assert_eq!(unbackfilled_entries[0].project_id, "project_unbackfilled");
        assert_eq!(unbackfilled_entries[0].object_type, ObjectType::Dataset);
    }

    // Test updating tracking entries
    let updates = vec![
        (
            queried_entries[0].id(),
            BackfillTrackingEntryUpdate {
                last_processed_sequence_id: Some(200),
                last_processed_sequence_id_2: Some(200),
            },
        ),
        (
            queried_entries[1].id(),
            BackfillTrackingEntryUpdate {
                last_processed_sequence_id: Some(400),
                last_processed_sequence_id_2: Some(400),
            },
        ),
    ];
    let updated_entries = store
        .update_backfill_tracking_entries(updates)
        .await
        .unwrap();

    // Verify updates
    assert_eq!(updated_entries[0].last_processed_sequence_id, 200);
    assert_eq!(updated_entries[0].last_processed_sequence_id_2, 200);
    assert_eq!(updated_entries[1].last_processed_sequence_id, 400);
    assert_eq!(updated_entries[1].last_processed_sequence_id_2, 400);

    // Test querying brainstore objects
    let tracking_entry_ids = vec![updated_entries[0].id(), updated_entries[1].id()];
    let objects = store
        .query_backfill_brainstore_objects(&tracking_entry_ids, false, 0, 500)
        .await
        .unwrap();
    assert_eq!(objects.len(), 2);
    assert_eq!(objects[0].len(), 2); // project1 has 2 objects (obj1 and obj2)
    assert_eq!(objects[1].len(), 1); // project2 has 1 object

    // Test querying with sequence ID range
    let objects = store
        .query_backfill_brainstore_objects(&tracking_entry_ids, false, 200, 300)
        .await
        .unwrap();
    assert_eq!(objects.len(), 2);
    assert_eq!(objects[0].len(), 2); // project1 has 2 objects in range (obj1 and obj2)
    assert_eq!(objects[1].len(), 0); // project2 has no objects in range

    // Test atomicity: batch update with both valid and invalid entries should fail entirely
    let non_existent_entry = BackfillTrackingEntryId {
        project_id: "non_existent_project",
        object_type: ObjectType::Experiment,
    };
    let valid_entry_id = updated_entries[0].id();
    let batch_updates = vec![
        (
            valid_entry_id.clone(),
            BackfillTrackingEntryUpdate {
                last_processed_sequence_id: Some(999),
                last_processed_sequence_id_2: Some(999),
            },
        ),
        (
            non_existent_entry,
            BackfillTrackingEntryUpdate {
                last_processed_sequence_id: Some(100),
                last_processed_sequence_id_2: Some(100),
            },
        ),
    ];
    let result = store.update_backfill_tracking_entries(batch_updates).await;
    assert!(
        result.is_err(),
        "Batch update with non-existent entry should fail"
    );

    // Verify that the valid entry wasn't updated (atomicity check)
    let entries_after_failed_batch = store
        .query_unbackfilled_tracking_entries_ordered(true, None, 10)
        .await
        .unwrap();
    let unchanged_entry = entries_after_failed_batch
        .iter()
        .find(|e| e.id() == valid_entry_id)
        .expect("Valid entry should still exist");
    assert_eq!(
        unchanged_entry.last_processed_sequence_id, 200,
        "Valid entry should remain unchanged after batch failure"
    );
    assert_eq!(
        unchanged_entry.last_processed_sequence_id_2, 200,
        "Valid entry should remain unchanged after batch failure"
    );

    // Test querying brainstore objects with full range
    let tracking_entry_ids = vec![updated_entries[0].id(), updated_entries[1].id()];
    let objects = store
        .query_backfill_brainstore_objects(&tracking_entry_ids, false, 0, 1000)
        .await
        .unwrap();
    assert_eq!(objects.len(), 2);
    assert_eq!(objects[0].len(), 2); // project1 has 2 objects
    assert_eq!(objects[1].len(), 1); // project2 has 1 object

    // Test querying with restricted sequence ID range
    let objects = store
        .query_backfill_brainstore_objects(&tracking_entry_ids, false, 150, 250)
        .await
        .unwrap();
    assert_eq!(objects.len(), 2);
    assert_eq!(objects[0].len(), 1); // project1 has 1 object in range (obj1 only)
    assert_eq!(objects[1].len(), 0); // project2 has no objects in range

    // Test querying with logs2 flag
    let objects = store
        .query_backfill_brainstore_objects(&tracking_entry_ids, true, 0, 1000)
        .await
        .unwrap();
    assert_eq!(objects.len(), 2);
    assert_eq!(objects[0].len(), 1); // project1 has 1 logs2 object (obj3)
    assert_eq!(objects[1].len(), 1); // project2 has 1 logs2 object (obj2)

    // Test querying logs2 objects with restricted range
    let objects = store
        .query_backfill_brainstore_objects(&tracking_entry_ids, true, 200, 300)
        .await
        .unwrap();
    assert_eq!(objects.len(), 2);
    assert_eq!(objects[0].len(), 1); // project1 has 1 logs2 object in range (obj3)
    assert_eq!(objects[1].len(), 0); // project2 has no logs2 objects in range

    // Test querying logs2 objects with range that excludes all
    let objects = store
        .query_backfill_brainstore_objects(&tracking_entry_ids, true, 50, 100)
        .await
        .unwrap();
    assert_eq!(objects.len(), 2);
    assert_eq!(objects[0].len(), 0); // project1 has no logs2 objects in range
    assert_eq!(objects[1].len(), 0); // project2 has no logs2 objects in range

    // Test query_backfill_tracking_entries method
    let entry_ids = vec![
        BackfillTrackingEntryId {
            project_id: "project1",
            object_type: ObjectType::Experiment,
        },
        BackfillTrackingEntryId {
            project_id: "project2",
            object_type: ObjectType::Dataset,
        },
        BackfillTrackingEntryId {
            project_id: "nonexistent",
            object_type: ObjectType::Experiment,
        },
    ];

    let queried_by_id = store
        .query_backfill_tracking_entries_by_ids(&entry_ids)
        .await
        .unwrap();

    assert_eq!(queried_by_id.len(), 3);

    // First entry should exist
    assert!(queried_by_id[0].is_some());
    let entry1 = queried_by_id[0].as_ref().unwrap();
    assert_eq!(entry1.project_id, "project1");
    assert_eq!(entry1.object_type, ObjectType::Experiment);
    assert_eq!(entry1.last_processed_sequence_id, 200);
    assert_eq!(entry1.last_encountered_sequence_id, 300);
    assert_eq!(entry1.last_processed_sequence_id_2, 200);
    assert_eq!(entry1.last_encountered_sequence_id_2, 300);

    // Second entry should exist
    assert!(queried_by_id[1].is_some());
    let entry2 = queried_by_id[1].as_ref().unwrap();
    assert_eq!(entry2.project_id, "project2");
    assert_eq!(entry2.object_type, ObjectType::Dataset);
    assert_eq!(entry2.last_processed_sequence_id, 400);
    assert_eq!(entry2.last_encountered_sequence_id, 500);
    assert_eq!(entry2.last_processed_sequence_id_2, 400);
    assert_eq!(entry2.last_encountered_sequence_id_2, 500);

    // Third entry should not exist
    assert!(queried_by_id[2].is_none());

    // Test with empty input
    let empty_result = store
        .query_backfill_tracking_entries_by_ids(&[])
        .await
        .unwrap();
    assert_eq!(empty_result.len(), 0);
}

#[tokio::test]
async fn test_memory_global_store() {
    let store = Arc::new(MemoryGlobalStore::default());
    test_global_store_impl(store).await;
}

#[tokio::test]
async fn test_directory_global_store() {
    let temp_dir = tempfile::tempdir().unwrap();
    let temp_dir_url = str_to_url(temp_dir.path().to_str().unwrap(), None).unwrap();
    let (store, _, prefix) = url_to_store(&temp_dir_url).unwrap();
    let directory = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));
    let locks_manager =
        Arc::new(MemoryGlobalLocksManager::default()) as Arc<dyn GlobalLocksManager>;
    let metadata_prefix = Path::new(prefix.as_ref()).to_owned();
    let store = Arc::new(DirectoryGlobalStore::new(
        directory,
        locks_manager,
        metadata_prefix,
    ));
    test_global_store_impl(store).await;
}

#[tokio::test]
async fn test_directory_global_store_cached() {
    let temp_dir = tempfile::tempdir().unwrap();
    let temp_dir_url = str_to_url(temp_dir.path().to_str().unwrap(), None).unwrap();
    let (store, _, prefix) = url_to_store(&temp_dir_url).unwrap();
    let directory = AsyncDirectoryArc::new(ObjectStoreDirectory::new(Arc::from(store)));

    let cached_directory = AsyncDirectoryArc::new(
        FileCacheDirectory::new(directory, None, FileCacheOpts::default()).unwrap(),
    );
    let locks_manager =
        Arc::new(MemoryGlobalLocksManager::default()) as Arc<dyn GlobalLocksManager>;
    let metadata_prefix = Path::new(prefix.as_ref()).join("metadata").to_owned();
    let store = Arc::new(DirectoryGlobalStore::new(
        cached_directory,
        locks_manager,
        metadata_prefix,
    ));
    test_global_store_impl(store).await;
}

pub const POSTGRES_EXTERNAL_MIGRATION: &str = r#"
create extension if not exists "pgcrypto";
"#;

pub fn get_postgres_global_store_migration() -> String {
    let filepath = Path::new(env!("CARGO_MANIFEST_DIR")).join("../../api-schema/schema.sql");
    let contents = std::fs::read_to_string(filepath).unwrap();

    let mut content_slices: Vec<&str> = Vec::new();
    let marker_pairs = [
        ("-- BEGIN main_db_tables", "-- END main_db_tables"),
        (
            "-- BEGIN brainstore_global_store",
            "-- END brainstore_global_store",
        ),
        (
            "-- BEGIN brainstore_global_locks_manager",
            "-- END brainstore_global_locks_manager",
        ),
        ("-- BEGIN brainstore_backfill", "-- END brainstore_backfill"),
    ];

    for (start_marker, end_marker) in marker_pairs {
        let start_idx = contents
            .find(start_marker)
            .expect("Could not find start marker");
        let end_idx = contents
            .find(end_marker)
            .expect("Could not find end marker");
        content_slices.push(&contents[start_idx..end_idx]);
    }

    // Manually insert a partition into the logs2 table so that we can use it
    // for testing.
    content_slices.push(
        r#"
        CREATE TABLE logs2_test_partition_global_store PARTITION OF logs2
        FOR VALUES FROM (0) TO (1000000000);
    "#,
    );

    content_slices.join("\n")
}

#[tokio::test]
async fn test_postgres_global_store() {
    let container = PostgresContainer::new().await;
    container
        .run_migration(POSTGRES_EXTERNAL_MIGRATION)
        .await
        .unwrap();
    container
        .run_migration(&get_postgres_global_store_migration())
        .await
        .unwrap();
    let store = Arc::new(PostgresGlobalStore::new(&container.connection_url).unwrap());
    test_global_store_impl(store).await;
}

#[tokio::test]
async fn test_postgres_global_store_backwards_compatibility() {
    let container = PostgresContainer::new().await;
    container
        .run_migration(POSTGRES_EXTERNAL_MIGRATION)
        .await
        .unwrap();
    container
        .run_migration(&get_postgres_global_store_migration())
        .await
        .unwrap();

    // Create a store instance
    let store = Arc::new(PostgresGlobalStore::new(&container.connection_url).unwrap());
    // Create a connection to directly interact with the database.
    let pgpool = PostgresPool::new(&container.connection_url, 1).unwrap();

    // Create test data
    let seg1 = Uuid::new_v4();
    let seg2 = Uuid::new_v4();
    let object_id = ObjectId::new("obj_compat_test").unwrap();

    // Add segments to the store
    store
        .update_segment_ids(&[(
            FullObjectId {
                object_type: ObjectType::Experiment,
                object_id,
            },
            &[seg1, seg2],
            &[],
        )])
        .await
        .unwrap();

    // Create some row IDs and root span IDs
    let row_id1 = "row1";
    let row_id2 = "row2";
    let row_id3 = "row3";
    let row_id4 = "row4";
    let span_id1 = "span1";
    let span_id2 = "span2";
    let span_id3 = "span3";
    let span_id4 = "span4";

    let full_row_id1 = FullRowId {
        object_type: ObjectType::Experiment,
        object_id,
        id: row_id1,
    };
    let full_row_id2 = FullRowId {
        object_type: ObjectType::Experiment,
        object_id,
        id: row_id2,
    };
    let full_row_id3 = FullRowId {
        object_type: ObjectType::Experiment,
        object_id,
        id: row_id3,
    };
    let full_row_id4 = FullRowId {
        object_type: ObjectType::Experiment,
        object_id,
        id: row_id4,
    };
    let full_row_id1_str = full_row_id1.to_string();
    let full_row_id2_str = full_row_id2.to_string();

    // Insert data directly into the legacy table for seg1
    let insert_legacy_query = r#"
        INSERT INTO brainstore_global_store_segment_id_to_row_info
        (segment_id, row_id, root_span_id)
        VALUES ($1, $2, $3)
    "#;

    pgpool
        .get_client()
        .await
        .unwrap()
        .execute(insert_legacy_query, &[&seg1, &full_row_id1_str, &span_id1])
        .await
        .unwrap();

    pgpool
        .get_client()
        .await
        .unwrap()
        .execute(insert_legacy_query, &[&seg1, &full_row_id2_str, &span_id2])
        .await
        .unwrap();

    // Use the new API to add entries to seg1 and seg2.
    store
        .add_id_segment_membership(
            IdSegmentMembershipType::RowId,
            [
                (seg1, vec![full_row_id1, full_row_id2]),
                (seg2, vec![full_row_id3, full_row_id4]),
            ]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    store
        .add_id_segment_membership(
            IdSegmentMembershipType::RootSpanId,
            [
                (
                    seg1,
                    vec![
                        FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id1),
                        FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id2),
                    ],
                ),
                (
                    seg2,
                    vec![
                        FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id3),
                        FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id4),
                    ],
                ),
            ]
            .into_iter()
            .collect(),
        )
        .await
        .unwrap();

    // Verify we can query row IDs from both tables
    let row_id_results = store
        .query_id_segment_membership(
            IdSegmentMembershipType::RowId,
            &[seg1, seg2],
            &[full_row_id1, full_row_id3],
        )
        .await
        .unwrap();

    assert_eq!(row_id_results.len(), 2);
    assert_eq!(row_id_results[&full_row_id1.to_owned()], seg1);
    assert_eq!(row_id_results[&full_row_id3.to_owned()], seg2);

    // Verify we can query root span IDs from both tables
    let span_id_results = store
        .query_id_segment_membership(
            IdSegmentMembershipType::RootSpanId,
            &[seg1, seg2],
            &[
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id1),
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id3),
            ],
        )
        .await
        .unwrap();

    assert_eq!(span_id_results.len(), 2);
    assert_eq!(
        span_id_results[&FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id1).to_owned()],
        seg1
    );
    assert_eq!(
        span_id_results[&FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id3).to_owned()],
        seg2
    );

    // Verify that copying works across both tables
    let seg_copy = Uuid::new_v4();
    store
        .copy_id_segment_membership(&[seg1, seg2], seg_copy)
        .await
        .unwrap();

    // Verify all entries were copied
    let all_row_ids = store
        .query_id_segment_membership(
            IdSegmentMembershipType::RowId,
            &[seg_copy],
            &[full_row_id1, full_row_id2, full_row_id3, full_row_id4],
        )
        .await
        .unwrap();

    assert_eq!(all_row_ids.len(), 4);
    assert_eq!(all_row_ids[&full_row_id1.to_owned()], seg_copy);
    assert_eq!(all_row_ids[&full_row_id2.to_owned()], seg_copy);
    assert_eq!(all_row_ids[&full_row_id3.to_owned()], seg_copy);
    assert_eq!(all_row_ids[&full_row_id4.to_owned()], seg_copy);

    let all_span_ids = store
        .query_id_segment_membership(
            IdSegmentMembershipType::RootSpanId,
            &[seg_copy],
            &[
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id1),
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id2),
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id3),
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id4),
            ],
        )
        .await
        .unwrap();

    assert_eq!(all_span_ids.len(), 4);
    assert_eq!(
        all_span_ids[&FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id1).to_owned()],
        seg_copy
    );
    assert_eq!(
        all_span_ids[&FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id2).to_owned()],
        seg_copy
    );
    assert_eq!(
        all_span_ids[&FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id3).to_owned()],
        seg_copy
    );
    assert_eq!(
        all_span_ids[&FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id4).to_owned()],
        seg_copy
    );

    // Verify purging works for both tables
    store.purge_id_segment_membership(&[seg1]).await.unwrap();

    // Check that entries from seg1 are gone
    let remaining_row_ids = store
        .query_id_segment_membership(
            IdSegmentMembershipType::RowId,
            &[seg1],
            &[full_row_id1, full_row_id2],
        )
        .await
        .unwrap();

    assert_eq!(remaining_row_ids.len(), 0);

    let remaining_span_ids = store
        .query_id_segment_membership(
            IdSegmentMembershipType::RootSpanId,
            &[seg1],
            &[
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id1),
                FullRowId::from_full_object_id(*FULL_OBJECT_ID0, span_id2),
            ],
        )
        .await
        .unwrap();

    assert_eq!(remaining_span_ids.len(), 0);

    // But entries in seg2 should still be there
    let seg2_row_ids = store
        .query_id_segment_membership(
            IdSegmentMembershipType::RowId,
            &[seg2],
            &[full_row_id3, full_row_id4],
        )
        .await
        .unwrap();

    assert_eq!(seg2_row_ids.len(), 2);
}
