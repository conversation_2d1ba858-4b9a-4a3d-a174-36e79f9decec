use futures::TryStreamExt;
use object_store::path::Path;
use serde_json::json;
use std::collections::HashSet;
use util::{
    anyhow::{anyhow, Result},
    chrono::{DateTime, Duration, Utc},
    system_types::{FullObjectId, ObjectId, ObjectType},
    test_util::assert_hashmap_eq,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    config_with_store::StoreInfo,
    json_value_store::write_json_value,
    process_wal::{
        clear_compacted_index, compact_segment_wal, process_object_wal, ClearCompactedIndexInput,
    },
    retention_test::{run_test_with_global_stores, TestFixture},
    test_util::make_compacted_wal_entries,
    vacuum::CommonVacuumOptions,
    vacuum::{VacuumType, DELETE_OPS_DIR, DRY_RUN_DELETE_OPS_DIR},
    vacuum_segment_wal::{
        vacuum_segment_wal, vacuum_segment_wal_stateless, VacuumSegmentWalFullOptions,
        VacuumSegmentWalInput, VacuumSegmentWalOptionalInput, VacuumSegmentWalOptions,
    },
    vacuum_test_util::{
        default_common_vacuum_opts_for_testing, default_vacuum_segment_wal_full_opts_for_testing,
    },
    wal::{WALScope, Wal},
    wal_entry::WalEntry,
};

fn wal_inserts() -> Vec<Vec<WalEntry>> {
    vec![
        vec![
            WalEntry {
                _pagination_key: PaginationKey(0),
                _xact_id: TransactionId(0),
                created: DateTime::<Utc>::from_timestamp_nanos(1000),
                id: "row0".to_string(),
                data: json!({
                    "field1": "foo",
                    "field3": json!({ "input": "bar" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(1),
                _xact_id: TransactionId(1),
                created: DateTime::<Utc>::from_timestamp_nanos(2000),
                _is_merge: Some(true),
                id: "row1".to_string(),
                data: json!({
                    "field1": "bar",
                    "field2": -1,
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
        ],
        vec![
            WalEntry {
                _pagination_key: PaginationKey(2),
                _xact_id: TransactionId(2),
                created: DateTime::<Utc>::from_timestamp_nanos(3000),
                id: "row2".to_string(),
                data: json!({
                    "field1": "baz",
                    "field2": -2,
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(3),
                _xact_id: TransactionId(3),
                created: DateTime::<Utc>::from_timestamp_nanos(4000),
                id: "row3".to_string(),
                data: json!({
                    "field1": "qux",
                    "field2": -3,
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
        ],
    ]
}

fn more_wal_inserts() -> Vec<Vec<WalEntry>> {
    vec![vec![WalEntry {
        _pagination_key: PaginationKey(4),
        _xact_id: TransactionId(4),
        created: DateTime::<Utc>::from_timestamp_nanos(2000),
        _is_merge: Some(true),
        id: "row1".to_string(),
        data: json!({
            "field1": "zzz",
        })
        .as_object()
        .unwrap()
        .clone(),
        ..Default::default()
    }]]
}

#[tokio::test]
async fn test_vacuum_grace_period() -> Result<()> {
    run_test_with_global_stores(test_vacuum_grace_period_inner).await
}

async fn test_vacuum_grace_period_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;
    let vacuum_type = VacuumType::VacuumSegmentWal;

    let segment_id = Uuid::new_v4();
    let wal = fixture.make_segment_wal();
    fixture.initialize_segment_metadata(segment_id).await;

    let entries_seq = wal_inserts();
    for entries in entries_seq.iter() {
        wal.insert(WALScope::Segment(segment_id), entries.clone())
            .await?;
    }

    let num_purged = fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(4))
        .await?;
    assert_eq!(num_purged, 4);

    // With default grace period (10 days), vacuuming shouldn't delete any files.
    let result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &VacuumSegmentWalFullOptions::default(),
    )
    .await?;
    assert_eq!(result.num_processed_segments, 1);
    assert_eq!(result.num_purged_entries, 0);
    assert_eq!(result.num_deleted_files, 0);

    // Now use a grace period of 0. Vacuum shouldn't run again yet because it just ran...
    let result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &default_vacuum_segment_wal_full_opts_for_testing(),
    )
    .await?;
    assert_eq!(result.num_processed_segments, 0);
    assert_eq!(result.num_purged_entries, 0);
    assert_eq!(result.num_deleted_files, 0);

    // ...but we set the last successful vacuum start_ts to 2 day ago, vacuum should now
    // process the segment.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(2),
        )
        .await?;
    let result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &default_vacuum_segment_wal_full_opts_for_testing(),
    )
    .await?;
    assert_eq!(result.num_processed_segments, 1);
    assert_eq!(result.num_purged_entries, 4);
    assert_eq!(result.num_deleted_files, 2);

    Ok(())
}

#[tokio::test]
async fn test_vacuum_purged_wal_files() -> Result<()> {
    run_test_with_global_stores(test_vacuum_purged_wal_files_inner).await
}

async fn test_vacuum_purged_wal_files_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;
    let vacuum_type = VacuumType::VacuumSegmentWal;

    let segment_id = Uuid::new_v4();
    let wal = fixture.make_segment_wal();
    fixture.initialize_segment_metadata(segment_id).await;

    let entries_seq = wal_inserts();
    for entries in entries_seq.iter() {
        wal.insert(WALScope::Segment(segment_id), entries.clone())
            .await?;
    }

    let inserted_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(inserted_entries.len(), 4);

    assert_eq!(inserted_entries[0].0, TransactionId(0));
    assert_eq!(inserted_entries[0].1[0].id, "row0");
    assert_eq!(inserted_entries[1].0, TransactionId(1));
    assert_eq!(inserted_entries[1].1[0].id, "row1");
    assert_eq!(inserted_entries[2].0, TransactionId(2));
    assert_eq!(inserted_entries[2].1[0].id, "row2");
    assert_eq!(inserted_entries[3].0, TransactionId(3));
    assert_eq!(inserted_entries[3].1[0].id, "row3");

    let segment_wal_entries = wal
        .global_store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await?;
    assert_eq!(segment_wal_entries.len(), 4);

    let wal_filename0 = segment_wal_entries[0].wal_filename;
    let wal_filename1 = segment_wal_entries[1].wal_filename;
    assert_eq!(wal_filename0, wal_filename1);
    let wal_filename2 = segment_wal_entries[2].wal_filename;
    let wal_filename3 = segment_wal_entries[3].wal_filename;
    assert_eq!(wal_filename2, wal_filename3);
    assert_ne!(wal_filename0, wal_filename2);

    let wal_directory = wal.wal_directory(WALScope::Segment(segment_id));

    let wal_path_a = wal_directory.join(wal_filename0.to_string());
    let wal_path_b = wal_directory.join(wal_filename2.to_string());

    // Check that both WAL files exist at the expected locations.
    assert!(wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);

    // Create additional invalid files in the directory. Vacuuming should clean these up too:
    // - A valid UUID that isn't a real object.
    let nonexistent_uuid = Uuid::new_v4();
    let nonexistent_uuid_path = wal_directory.join(nonexistent_uuid.to_string());
    wal.directory
        .async_atomic_write(&nonexistent_uuid_path, b"Nonexistent")
        .await?;
    // - A file with a filename that isn't a valid UUID. This file will only get cleaned
    //   up if `delete_junk_files` is set to true, otherwise it will be ignored.
    let junk_path = wal_directory.join("not-a-valid-uuid");
    wal.directory
        .async_atomic_write(&junk_path, b"Invalid filename")
        .await?;

    // Verify all created files/paths exist.
    assert!(wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);
    assert!(wal.directory.async_exists(&nonexistent_uuid_path).await?);
    assert!(wal.directory.async_exists(&junk_path).await?);

    let num_purged = fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(2))
        .await?;
    assert_eq!(num_purged, 2);

    // The segment files should still exist after purging but before vacuuming.
    assert!(wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);

    // Vacuuming shouldn't delete anything if we're not yet past the expiration time
    // for WAL entries.
    let result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &VacuumSegmentWalFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 0,
                ..Default::default()
            },
            vacuum_segment_wal_opts: VacuumSegmentWalOptions {
                vacuum_segment_wal_entry_expiration_seconds: 60 * 60, // 1 hour
                vacuum_segment_wal_period_seconds: 24 * 60 * 60,      // 1 day
                vacuum_segment_wal_delete_unrecognized_files: false,
                ..Default::default()
            },
        },
    )
    .await?;
    // Only the nonexistent UUID file should get wiped.
    assert_eq!(result.num_processed_segments, 1);
    assert_eq!(result.num_purged_entries, 0);
    assert_eq!(result.num_deleted_files, 1);
    assert!(wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);
    assert!(!wal.directory.async_exists(&nonexistent_uuid_path).await?);
    assert!(wal.directory.async_exists(&junk_path).await?);

    // Now set the expiration time to 0. Vacuuming should delete the first WAL file
    // since all of its entries should have been purged (xact_id < 2).
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(1),
        )
        .await?;
    let result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &VacuumSegmentWalFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 0,
                ..Default::default()
            },
            vacuum_segment_wal_opts: VacuumSegmentWalOptions {
                vacuum_segment_wal_entry_expiration_seconds: 0,
                vacuum_segment_wal_period_seconds: 24 * 60 * 60, // 1 day
                vacuum_segment_wal_delete_unrecognized_files: false,
                ..Default::default()
            },
        },
    )
    .await?;
    assert_eq!(result.num_processed_segments, 1);
    assert_eq!(result.num_purged_entries, 2);
    assert_eq!(result.num_deleted_files, 1);
    assert!(!wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);
    assert!(!wal.directory.async_exists(&nonexistent_uuid_path).await?);
    assert!(wal.directory.async_exists(&junk_path).await?);

    // To remove the "random junk" file, we'll have to set delete_unrecognized_files to true
    // and set last_successful_start_ts to more than vacuum_period seconds ago.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(1),
        )
        .await?;

    let result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &VacuumSegmentWalFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 0,
                ..Default::default()
            },
            vacuum_segment_wal_opts: VacuumSegmentWalOptions {
                vacuum_segment_wal_entry_expiration_seconds: 0,
                vacuum_segment_wal_period_seconds: 24 * 60 * 60, // 1 day
                vacuum_segment_wal_delete_unrecognized_files: true,
                ..Default::default()
            },
        },
    )
    .await?;
    assert_eq!(result.num_processed_segments, 1);
    assert_eq!(result.num_purged_entries, 0);
    assert_eq!(result.num_deleted_files, 1);
    assert!(wal.directory.async_exists(&wal_path_b).await?);
    assert!(!wal.directory.async_exists(&junk_path).await?);

    // Now purge up to xact_id 3.
    let num_purged = fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(3))
        .await?;
    assert_eq!(num_purged, 1);

    // Vacuuming should not delete the second WAL file, since it still contains
    // a live entry (only one of its two entries was purged).
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(2),
        )
        .await?;
    let result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &default_vacuum_segment_wal_full_opts_for_testing(),
    )
    .await?;
    assert_eq!(result.num_processed_segments, 1);
    assert_eq!(result.num_purged_entries, 1);
    assert_eq!(result.num_deleted_files, 0);
    assert!(wal.directory.async_exists(&wal_path_b).await?);

    // Now purge up to xact_id 4, which should finally purge the second entry.
    let num_purged = fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(4))
        .await?;
    assert_eq!(num_purged, 1);

    // Vacuuming should now delete the second file since all of its entries
    // have been purged.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(3),
        )
        .await?;
    let result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &default_vacuum_segment_wal_full_opts_for_testing(),
    )
    .await?;
    assert_eq!(result.num_processed_segments, 1);
    assert_eq!(result.num_purged_entries, 1);
    assert_eq!(result.num_deleted_files, 1);
    assert!(!wal.directory.async_exists(&wal_path_a).await?);
    assert!(!wal.directory.async_exists(&wal_path_b).await?);

    Ok(())
}

#[tokio::test]
async fn test_vacuum_dry_run() -> Result<()> {
    run_test_with_global_stores(test_vacuum_dry_run_inner).await
}

async fn test_vacuum_dry_run_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;
    let vacuum_type = VacuumType::VacuumSegmentWal;

    let segment_id = Uuid::new_v4();
    let wal = fixture.make_segment_wal();
    fixture.initialize_segment_metadata(segment_id).await;

    let entries_seq = wal_inserts();
    for entries in entries_seq.iter() {
        wal.insert(WALScope::Segment(segment_id), entries.clone())
            .await?;
    }

    // Soft-delete the first 3 entries.
    let num_deleted_entries = fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(3))
        .await?;
    assert_eq!(num_deleted_entries, 3);

    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 1);
    assert_eq!(remaining_entries[0].0, TransactionId(3));
    assert_eq!(remaining_entries[0].1.len(), 1);
    assert_eq!(remaining_entries[0].1[0].id, "row3");
    assert_eq!(remaining_entries[0].1[0].data["field1"], json!("qux"));

    // Do a vacuum dry run. Because it shouldn't purge the soft-deleted entries,
    // we should be able to restore them afterwards.
    let dry_run_result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: true,
        },
        VacuumSegmentWalOptionalInput::default(),
        &default_vacuum_segment_wal_full_opts_for_testing(),
    )
    .await?;
    assert_eq!(dry_run_result.num_processed_segments, 1);
    assert_eq!(dry_run_result.planned_num_purged_entries, 3);
    assert_eq!(dry_run_result.num_purged_entries, 0);
    assert_eq!(dry_run_result.planned_num_deleted_files, 0);
    assert_eq!(dry_run_result.num_deleted_files, 0);

    // Restore the soft-deleted WAL entries.
    let num_restored_entries = fixture
        .config()
        .global_store
        .restore_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(3))
        .await
        .unwrap();
    assert_eq!(num_restored_entries, 3);

    let visible_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(visible_entries.len(), 4);

    // Vacuuming now should do nothing because there are no soft-deleted entries to purge.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(2),
        )
        .await?;
    let real_run_result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &default_vacuum_segment_wal_full_opts_for_testing(),
    )
    .await?;
    assert_eq!(real_run_result.num_processed_segments, 1);
    assert_eq!(real_run_result.planned_num_purged_entries, 0);
    assert_eq!(real_run_result.num_purged_entries, 0);
    assert_eq!(real_run_result.planned_num_deleted_files, 0);
    assert_eq!(real_run_result.num_deleted_files, 0);

    // Soft delete the same entries again and check that vacuuming purges them.
    let num_deleted_entries = fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(3))
        .await?;
    assert_eq!(num_deleted_entries, 3);

    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 1);
    assert_eq!(remaining_entries[0].0, TransactionId(3));
    assert_eq!(remaining_entries[0].1.len(), 1);
    assert_eq!(remaining_entries[0].1[0].id, "row3");
    assert_eq!(remaining_entries[0].1[0].data["field1"], json!("qux"));
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(2),
        )
        .await?;
    let real_run_result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &default_vacuum_segment_wal_full_opts_for_testing(),
    )
    .await?;
    assert_eq!(real_run_result.num_processed_segments, 1);
    assert_eq!(real_run_result.planned_num_purged_entries, 3);
    assert_eq!(real_run_result.num_purged_entries, 3);
    assert_eq!(real_run_result.planned_num_deleted_files, 1);
    assert_eq!(real_run_result.num_deleted_files, 1);

    let final_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(final_entries.len(), 1);

    // Restoring entries should do nothing now because they were permanently purged.
    let num_restored_entries = fixture
        .config()
        .global_store
        .restore_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(4))
        .await
        .unwrap();
    assert_eq!(num_restored_entries, 0);

    Ok(())
}

#[tokio::test]
async fn test_vacuum_delete_ops_logging() -> Result<()> {
    run_test_with_global_stores(test_vacuum_delete_ops_logging_inner).await
}

async fn test_vacuum_delete_ops_logging_inner(use_postgres_global_store: bool) -> Result<()> {
    use crate::vacuum::{DELETE_OPS_DIR, DRY_RUN_DELETE_OPS_DIR};

    let fixture = TestFixture::new(use_postgres_global_store).await;
    let vacuum_type = VacuumType::VacuumSegmentWal;

    let segment_id = Uuid::new_v4();
    let wal = fixture.make_segment_wal();
    fixture.initialize_segment_metadata(segment_id).await;

    let entries_seq = wal_inserts();
    for entries in entries_seq.iter() {
        wal.insert(WALScope::Segment(segment_id), entries.clone())
            .await?;
    }

    // Purge entries to set up files for deletion
    let num_purged = fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(4))
        .await?;
    assert_eq!(num_purged, 4);

    // Set vacuum timestamp to allow processing
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(2),
        )
        .await?;

    // Create some junk files in the segment WAL directory.
    let segment_index_wal = fixture.make_segment_wal();
    let wal_directory = segment_index_wal.wal_directory(crate::wal::WALScope::Segment(segment_id));
    let junk_filenames = vec!["junk1.txt", "junk2.txt"];
    for filename in &junk_filenames {
        let junk_path = wal_directory.join(filename);
        write_json_value(
            segment_index_wal.directory.as_ref(),
            &junk_path,
            &serde_json::Value::String("junk data".to_string()),
        )
        .await?;
    }

    // Do a dry run vacuum.
    let dry_run_result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: true,
        },
        VacuumSegmentWalOptionalInput::default(),
        &VacuumSegmentWalFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_delete_ops_batch_size: 1, // Force creation of multiple delete ops files
                ..default_common_vacuum_opts_for_testing()
            },
            ..default_vacuum_segment_wal_full_opts_for_testing()
        },
    )
    .await?;
    assert_eq!(dry_run_result.planned_num_purged_entries, 4);
    assert_eq!(dry_run_result.num_purged_entries, 0);
    assert_eq!(dry_run_result.planned_num_deleted_files, 2);
    assert_eq!(dry_run_result.num_deleted_files, 0);

    // Check that dry run delete ops files were created
    let index_store = fixture.config().index.clone();
    let dry_run_delete_ops_path = index_store.prefix.join(DRY_RUN_DELETE_OPS_DIR);
    let dry_run_delete_ops_path_str = dry_run_delete_ops_path
        .to_str()
        .ok_or("Failed to convert prefix to string")
        .unwrap();
    let dry_run_delete_ops_prefix = Path::from(dry_run_delete_ops_path_str);
    let dry_run_files = index_store
        .store
        .list(Some(&dry_run_delete_ops_prefix))
        .try_collect::<Vec<_>>()
        .await?;
    assert!(
        !dry_run_files.is_empty(),
        "Should have created dry run delete ops files"
    );

    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(2),
        )
        .await?;

    // Test delete ops logging for actual run
    let actual_result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &VacuumSegmentWalFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_delete_ops_batch_size: 1, // Force creation of multiple delete ops files
                ..default_common_vacuum_opts_for_testing()
            },
            vacuum_segment_wal_opts: VacuumSegmentWalOptions {
                vacuum_segment_wal_entry_expiration_seconds: 0,
                vacuum_segment_wal_period_seconds: 2 * 24 * 60 * 60, // 2 days
                vacuum_segment_wal_delete_unrecognized_files: true, // Enable deletion of junk files
                ..Default::default()
            },
        },
    )
    .await?;

    assert_eq!(actual_result.planned_num_purged_entries, 4);
    assert_eq!(actual_result.num_purged_entries, 4);
    assert_eq!(actual_result.planned_num_deleted_files, 4);
    assert_eq!(actual_result.num_deleted_files, 4); // Should delete 2 WAL files + 2 junk files

    // Check that actual delete ops files were created
    let delete_ops_path = index_store.prefix.join(DELETE_OPS_DIR);
    let delete_ops_path_str = delete_ops_path
        .to_str()
        .ok_or("Failed to convert prefix to string")
        .unwrap();
    let delete_ops_prefix = Path::from(delete_ops_path_str);
    let delete_ops_files = index_store
        .store
        .list(Some(&delete_ops_prefix))
        .try_collect::<Vec<_>>()
        .await?;
    assert!(
        !delete_ops_files.is_empty(),
        "Should have created delete ops files"
    );

    Ok(())
}

async fn read_delete_ops_paths(
    index_store: &StoreInfo,
    delete_ops_dir: &str,
) -> Result<Vec<String>> {
    use futures::TryStreamExt;
    use object_store::path::Path;

    let delete_ops_path = index_store.prefix.join(delete_ops_dir);
    let delete_ops_path_str = delete_ops_path
        .to_str()
        .ok_or_else(|| anyhow!("Failed to convert prefix to string"))?;
    let delete_ops_prefix = Path::from(delete_ops_path_str);

    let files = index_store
        .store
        .list(Some(&delete_ops_prefix))
        .try_collect::<Vec<_>>()
        .await?;

    let mut paths = Vec::new();
    for file_meta in files {
        let content = index_store
            .store
            .get(&file_meta.location)
            .await?
            .bytes()
            .await?;
        let content_str = String::from_utf8(content.to_vec())?;
        for line in content_str.lines() {
            if !line.trim().is_empty() {
                paths.push(line.to_string());
            }
        }
    }
    Ok(paths)
}

#[tokio::test]
async fn test_vacuum_segment_wal_comprehensive_delete_ops() -> Result<()> {
    run_test_with_global_stores(test_vacuum_segment_wal_comprehensive_delete_ops_inner).await
}

async fn test_vacuum_segment_wal_comprehensive_delete_ops_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;
    let vacuum_type = VacuumType::VacuumSegmentWal;

    let segment_id = Uuid::new_v4();
    let wal = fixture.make_segment_wal();
    fixture.initialize_segment_metadata(segment_id).await;

    let entries_seq = [wal_inserts(), more_wal_inserts()].concat();
    for entries in entries_seq.iter() {
        wal.insert(WALScope::Segment(segment_id), entries.clone())
            .await?;
    }

    // Process the WAL entries and compact the segment.
    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let segment_wal_entries = wal
        .global_store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await?;
    assert_eq!(segment_wal_entries.len(), 5);

    let wal_filename0 = segment_wal_entries[0].wal_filename;
    let wal_filename1 = segment_wal_entries[1].wal_filename;
    assert_eq!(wal_filename0, wal_filename1);
    let wal_filename2 = segment_wal_entries[2].wal_filename;
    let wal_filename3 = segment_wal_entries[3].wal_filename;
    assert_eq!(wal_filename2, wal_filename3);
    let wal_filename4 = segment_wal_entries[4].wal_filename;
    assert_ne!(wal_filename0, wal_filename4);

    let wal_directory = wal.wal_directory(WALScope::Segment(segment_id));

    let wal_path_a = wal_directory.join(wal_filename0.to_string());
    let wal_path_b = wal_directory.join(wal_filename2.to_string());
    let wal_path_c = wal_directory.join(wal_filename4.to_string());

    // Check that the WAL files exist at the expected locations.
    assert!(wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);
    assert!(wal.directory.async_exists(&wal_path_c).await?);

    // Purge entries to wipe the first 4 WAL entries (first 2 WAL files).
    let num_purged = fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(4))
        .await?;
    assert_eq!(num_purged, 4);

    // Create some junk files in the segment WAL directory to test delete ops logging.
    let segment_index_wal = fixture.make_segment_wal();
    let wal_directory = segment_index_wal.wal_directory(crate::wal::WALScope::Segment(segment_id));
    let junk_filenames = vec!["junk1.txt", "junk2.txt"];
    let junk_paths = junk_filenames
        .iter()
        .map(|filename| wal_directory.join(filename))
        .collect::<Vec<_>>();
    for junk_path in &junk_paths {
        write_json_value(
            segment_index_wal.directory.as_ref(),
            &junk_path,
            &serde_json::Value::String("junk data".to_string()),
        )
        .await?;
    }

    // The first 2 files and the 2 junk files should get deleted by vacuum.
    let expected_deletion_paths: HashSet<String> = [
        wal_path_a.clone(),
        wal_path_b.clone(),
        junk_paths[0].clone(),
        junk_paths[1].clone(),
    ]
    .iter()
    .map(|path| path.to_string_lossy().to_string())
    .collect();

    // Vacuum dry run should create delete ops files in dry_run_delete_ops directory.
    let dry_run_result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: true,
        },
        VacuumSegmentWalOptionalInput::default(),
        &VacuumSegmentWalFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_delete_ops_batch_size: 1, // Force creation of multiple files
                ..default_common_vacuum_opts_for_testing()
            },
            ..default_vacuum_segment_wal_full_opts_for_testing()
        },
    )
    .await?;
    assert_eq!(dry_run_result.planned_num_purged_entries, 4);
    assert_eq!(dry_run_result.num_purged_entries, 0); // Should not actually purge in dry run
    assert_eq!(dry_run_result.planned_num_deleted_files, 2);
    assert_eq!(dry_run_result.num_deleted_files, 0);
    assert!(wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);
    assert!(wal.directory.async_exists(&wal_path_c).await?);
    assert!(wal.directory.async_exists(&junk_paths[0]).await?);
    assert!(wal.directory.async_exists(&junk_paths[1]).await?);

    // Verify dry run delete ops files were created and contain the expected paths.
    let dry_run_delete_ops_paths: HashSet<String> =
        read_delete_ops_paths(&fixture.config().index, DRY_RUN_DELETE_OPS_DIR)
            .await?
            .into_iter()
            .collect();
    assert_eq!(
        dry_run_delete_ops_paths.len(),
        2,
        "Should have 2 files planned for deletion in dry run"
    );

    // Vacuuming for real should actually delete the files and log the paths to the delete log.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(2),
        )
        .await?;
    let actual_result = vacuum_segment_wal(
        VacuumSegmentWalInput {
            object_ids: None,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: false,
        },
        VacuumSegmentWalOptionalInput::default(),
        &VacuumSegmentWalFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_delete_ops_batch_size: 1, // Force creation of multiple files
                ..default_common_vacuum_opts_for_testing()
            },
            ..default_vacuum_segment_wal_full_opts_for_testing()
        },
    )
    .await?;
    assert_eq!(actual_result.planned_num_purged_entries, 4);
    assert_eq!(actual_result.num_purged_entries, 4);
    assert_eq!(actual_result.planned_num_deleted_files, 4);
    assert_eq!(actual_result.num_deleted_files, 4);

    // Verify actual delete ops files were created and contain the expected paths.
    let delete_ops_paths: HashSet<String> =
        read_delete_ops_paths(&fixture.config().index, DELETE_OPS_DIR)
            .await?
            .into_iter()
            .collect();
    assert_eq!(
        delete_ops_paths.len(),
        4,
        "Should have 4 files recorded in delete ops"
    );
    assert_eq!(
        delete_ops_paths, expected_deletion_paths,
        "Actual run should delete all files (WAL + junk): actual={:?}, expected={:?}",
        delete_ops_paths, expected_deletion_paths
    );

    // Check that the WAL files no longer exist.
    assert!(!wal.directory.async_exists(&wal_path_a).await?);
    assert!(!wal.directory.async_exists(&wal_path_b).await?);
    assert!(wal.directory.async_exists(&wal_path_c).await?);
    assert!(!wal.directory.async_exists(&junk_paths[0]).await?);
    assert!(!wal.directory.async_exists(&junk_paths[1]).await?);

    // Clear the index, rerun compaction, and verify that only the final row is left.
    clear_compacted_index(ClearCompactedIndexInput {
        segment_id,
        global_store: fixture.config().global_store.clone(),
        locks_manager: &*fixture.config().locks_manager,
    })
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let expected_compacted_docs = make_compacted_wal_entries(entries_seq[2].clone());
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 1);
    assert_hashmap_eq(&docs, &expected_compacted_docs);

    Ok(())
}

#[tokio::test]
async fn test_vacuum_segment_wal_stateless() -> Result<()> {
    run_test_with_global_stores(test_vacuum_segment_wal_stateless_inner).await
}

async fn test_vacuum_segment_wal_stateless_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;
    let vacuum_type = VacuumType::VacuumSegmentWal;

    let segment_id = Uuid::new_v4();
    let wal = fixture.make_segment_wal();
    fixture.initialize_segment_metadata(segment_id).await;

    let entries_seq = wal_inserts();
    for entries in entries_seq.iter() {
        wal.insert(WALScope::Segment(segment_id), entries.clone())
            .await?;
    }

    // Purge entries to set up files for deletion
    let num_purged = fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(4))
        .await?;
    assert_eq!(num_purged, 4);

    // Set vacuum timestamp to allow processing
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            vacuum_type,
            Utc::now() - Duration::days(2),
        )
        .await?;

    // Test 1: Stateless vacuum with specific object IDs (tests object ID to segment ID mapping)
    let object_id = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("test_object").unwrap(),
    };

    let stateless_result = vacuum_segment_wal_stateless(
        VacuumSegmentWalInput {
            object_ids: Some(&[object_id]),
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            dry_run: true,
        },
        VacuumSegmentWalOptionalInput::default(),
        default_vacuum_segment_wal_full_opts_for_testing(),
    )
    .await;

    // Since we don't have the object_id -> segment_id mapping set up in this test,
    // the stateless function should return successfully but with no segments processed
    assert!(stateless_result.success, "Stateless vacuum should succeed");
    assert_eq!(
        stateless_result.num_processed_segments, 0,
        "Should process 0 segments when object IDs don't map to any segments"
    );
    assert!(stateless_result.error.is_none(), "Should have no error");

    Ok(())
}
